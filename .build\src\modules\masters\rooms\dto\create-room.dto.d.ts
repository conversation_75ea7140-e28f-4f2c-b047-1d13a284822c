import { MasterWithNumericValueDto } from '../../common/dto/base-master.dto';
import { MasterType } from '../../common/enums/master-types.enum';
export declare class CreateRoomDto extends MasterWithNumericValueDto {
    name: string;
    description?: string;
    code?: string;
    numericValue: number;
    unit?: string;
    roomType?: string;
    features?: string[];
    typicalArea?: number;
    popularityRating?: number;
    masterType: MasterType.ROOM;
}
