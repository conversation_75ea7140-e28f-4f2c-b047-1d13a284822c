"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoomTypeCategory = exports.PropertyTypeCategory = exports.AmenityCategory = exports.MasterStatus = exports.MasterType = void 0;
var MasterType;
(function (MasterType) {
    MasterType["CITY"] = "city";
    MasterType["LOCATION"] = "location";
    MasterType["AMENITY"] = "amenity";
    MasterType["FLOOR"] = "floor";
    MasterType["TOWER"] = "tower";
    MasterType["PROPERTY_TYPE"] = "property_type";
    MasterType["ROOM"] = "room";
    MasterType["WASHROOM"] = "washroom";
})(MasterType || (exports.MasterType = MasterType = {}));
var MasterStatus;
(function (MasterStatus) {
    MasterStatus["ACTIVE"] = "active";
    MasterStatus["INACTIVE"] = "inactive";
    MasterStatus["ARCHIVED"] = "archived";
})(MasterStatus || (exports.MasterStatus = MasterStatus = {}));
var AmenityCategory;
(function (AmenityCategory) {
    AmenityCategory["BASIC"] = "basic";
    AmenityCategory["RECREATIONAL"] = "recreational";
    AmenityCategory["SECURITY"] = "security";
    AmenityCategory["CONVENIENCE"] = "convenience";
    AmenityCategory["WELLNESS"] = "wellness";
    AmenityCategory["SPORTS"] = "sports";
    AmenityCategory["COMMUNITY"] = "community";
    AmenityCategory["PARKING"] = "parking";
    AmenityCategory["UTILITIES"] = "utilities";
})(AmenityCategory || (exports.AmenityCategory = AmenityCategory = {}));
var PropertyTypeCategory;
(function (PropertyTypeCategory) {
    PropertyTypeCategory["RESIDENTIAL"] = "residential";
    PropertyTypeCategory["COMMERCIAL"] = "commercial";
    PropertyTypeCategory["MIXED_USE"] = "mixed_use";
    PropertyTypeCategory["INDUSTRIAL"] = "industrial";
    PropertyTypeCategory["AGRICULTURAL"] = "agricultural";
    PropertyTypeCategory["INSTITUTIONAL"] = "institutional";
})(PropertyTypeCategory || (exports.PropertyTypeCategory = PropertyTypeCategory = {}));
var RoomTypeCategory;
(function (RoomTypeCategory) {
    RoomTypeCategory["STUDIO"] = "studio";
    RoomTypeCategory["BHK"] = "bhk";
    RoomTypeCategory["VILLA"] = "villa";
    RoomTypeCategory["PENTHOUSE"] = "penthouse";
    RoomTypeCategory["DUPLEX"] = "duplex";
    RoomTypeCategory["TRIPLEX"] = "triplex";
})(RoomTypeCategory || (exports.RoomTypeCategory = RoomTypeCategory = {}));
//# sourceMappingURL=master-types.enum.js.map