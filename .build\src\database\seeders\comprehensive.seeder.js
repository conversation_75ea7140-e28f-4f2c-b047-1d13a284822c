"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ComprehensiveSeeder = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const faker_1 = require("@faker-js/faker");
const city_schema_1 = require("../../modules/masters/cities/schemas/city.schema");
const location_schema_1 = require("../../modules/masters/locations/schemas/location.schema");
const amenity_schema_1 = require("../../modules/masters/amenities/schemas/amenity.schema");
const floor_schema_1 = require("../../modules/masters/floors/schemas/floor.schema");
const tower_schema_1 = require("../../modules/masters/towers/schemas/tower.schema");
const property_type_schema_1 = require("../../modules/masters/property-types/schemas/property-type.schema");
const room_schema_1 = require("../../modules/masters/rooms/schemas/room.schema");
const washroom_schema_1 = require("../../modules/masters/washrooms/schemas/washroom.schema");
const project_schema_1 = require("../../modules/projects/schemas/project.schema");
const builder_schema_1 = require("../../modules/builders/schemas/builder.schema");
const agent_schema_1 = require("../../modules/agents/schemas/agent.schema");
const master_types_enum_1 = require("../../modules/masters/common/enums/master-types.enum");
const project_status_enum_1 = require("../../modules/projects/enums/project-status.enum");
let ComprehensiveSeeder = class ComprehensiveSeeder {
    constructor(cityModel, locationModel, amenityModel, floorModel, towerModel, propertyTypeModel, roomModel, washroomModel, projectModel, builderModel, agentModel) {
        this.cityModel = cityModel;
        this.locationModel = locationModel;
        this.amenityModel = amenityModel;
        this.floorModel = floorModel;
        this.towerModel = towerModel;
        this.propertyTypeModel = propertyTypeModel;
        this.roomModel = roomModel;
        this.washroomModel = washroomModel;
        this.projectModel = projectModel;
        this.builderModel = builderModel;
        this.agentModel = agentModel;
    }
    async seedAll() {
        console.log('🌱 Starting comprehensive database seeding...');
        try {
            await this.clearDatabase();
            const cities = await this.seedCities();
            const locations = await this.seedLocations(cities);
            const amenities = await this.seedAmenities();
            const floors = await this.seedFloors();
            const towers = await this.seedTowers();
            const propertyTypes = await this.seedPropertyTypes();
            const rooms = await this.seedRooms();
            const washrooms = await this.seedWashrooms();
            const builders = await this.seedBuilders();
            const agents = await this.seedAgents();
            await this.seedProjects(cities, locations, amenities, builders);
            console.log('✅ Database seeding completed successfully!');
        }
        catch (error) {
            console.error('❌ Error during seeding:', error);
            throw error;
        }
    }
    async clearDatabase() {
        console.log('🧹 Clearing existing data...');
        await Promise.all([
            this.projectModel.deleteMany({}),
            this.builderModel.deleteMany({}),
            this.agentModel.deleteMany({}),
            this.cityModel.deleteMany({}),
            this.locationModel.deleteMany({}),
            this.amenityModel.deleteMany({}),
            this.floorModel.deleteMany({}),
            this.towerModel.deleteMany({}),
            this.propertyTypeModel.deleteMany({}),
            this.roomModel.deleteMany({}),
            this.washroomModel.deleteMany({}),
        ]);
        console.log('✅ Database cleared');
    }
    async seedCities() {
        console.log('🏙️ Seeding cities...');
        const indianCities = [
            { name: 'Mumbai', state: 'Maharashtra', coordinates: [72.8777, 19.0760] },
            { name: 'Delhi', state: 'Delhi', coordinates: [77.1025, 28.7041] },
            { name: 'Bangalore', state: 'Karnataka', coordinates: [77.5946, 12.9716] },
            { name: 'Hyderabad', state: 'Telangana', coordinates: [78.4867, 17.3850] },
            { name: 'Chennai', state: 'Tamil Nadu', coordinates: [80.2707, 13.0827] },
            { name: 'Kolkata', state: 'West Bengal', coordinates: [88.3639, 22.5726] },
            { name: 'Pune', state: 'Maharashtra', coordinates: [73.8567, 18.5204] },
            { name: 'Ahmedabad', state: 'Gujarat', coordinates: [72.5714, 23.0225] },
            { name: 'Jaipur', state: 'Rajasthan', coordinates: [75.7873, 26.9124] },
            { name: 'Surat', state: 'Gujarat', coordinates: [72.8311, 21.1702] },
            { name: 'Lucknow', state: 'Uttar Pradesh', coordinates: [80.9462, 26.8467] },
            { name: 'Kanpur', state: 'Uttar Pradesh', coordinates: [80.3319, 26.4499] },
            { name: 'Nagpur', state: 'Maharashtra', coordinates: [79.0882, 21.1458] },
            { name: 'Indore', state: 'Madhya Pradesh', coordinates: [75.8577, 22.7196] },
            { name: 'Thane', state: 'Maharashtra', coordinates: [72.9781, 19.2183] },
            { name: 'Bhopal', state: 'Madhya Pradesh', coordinates: [77.4126, 23.2599] },
            { name: 'Visakhapatnam', state: 'Andhra Pradesh', coordinates: [83.3018, 17.6868] },
            { name: 'Pimpri-Chinchwad', state: 'Maharashtra', coordinates: [73.8067, 18.6298] },
            { name: 'Patna', state: 'Bihar', coordinates: [85.1376, 25.5941] },
            { name: 'Vadodara', state: 'Gujarat', coordinates: [73.1812, 22.3072] },
        ];
        const cities = [];
        for (const cityData of indianCities) {
            const city = new this.cityModel({
                name: cityData.name,
                masterType: master_types_enum_1.MasterType.CITY,
                status: master_types_enum_1.MasterStatus.ACTIVE,
                state: cityData.state,
                country: 'India',
                coordinates: cityData.coordinates,
                timezone: 'Asia/Kolkata',
                pinCodes: this.generatePinCodes(),
                isPopular: Math.random() > 0.3,
                sortOrder: cities.length + 1,
                metadata: {
                    population: faker_1.faker.number.int({ min: 500000, max: 20000000 }),
                    area: faker_1.faker.number.int({ min: 100, max: 4000 }),
                    language: this.getStateLanguage(cityData.state),
                    climate: faker_1.faker.helpers.arrayElement(['tropical', 'subtropical', 'arid', 'temperate']),
                }
            });
            cities.push(await city.save());
        }
        console.log(`✅ Seeded ${cities.length} cities`);
        return cities;
    }
    async seedLocations(cities) {
        console.log('📍 Seeding locations...');
        const locations = [];
        for (const city of cities) {
            const locationCount = faker_1.faker.number.int({ min: 8, max: 15 });
            const cityLocationCodes = new Set();
            for (let i = 0; i < locationCount; i++) {
                const locationName = this.generateLocationName(city.name);
                const locationCode = this.generateUniqueLocationCode(city.name, cityLocationCodes);
                const existingLocation = await this.locationModel.findOne({
                    name: locationName,
                    parentId: city._id
                });
                if (existingLocation) {
                    console.log(`⚠️  Location "${locationName}" already exists in ${city.name}, skipping...`);
                    locations.push(existingLocation);
                    continue;
                }
                const locationData = {
                    name: locationName,
                    masterType: master_types_enum_1.MasterType.LOCATION,
                    status: master_types_enum_1.MasterStatus.ACTIVE,
                    parentId: city._id,
                    parentType: master_types_enum_1.MasterType.CITY,
                    locationCode,
                    coordinates: this.generateNearbyCoordinates(city.coordinates),
                    isPopular: Math.random() > 0.4,
                    sortOrder: i + 1,
                    locationType: faker_1.faker.helpers.arrayElement(['residential', 'commercial', 'mixed', 'industrial']),
                    locationCategory: faker_1.faker.helpers.arrayElement(['prime', 'premium', 'mid_range', 'budget']),
                    address: faker_1.faker.location.streetAddress(),
                    pincode: faker_1.faker.location.zipCode('######'),
                    landmark: `Near ${faker_1.faker.helpers.arrayElement(['Metro Station', 'Shopping Mall', 'Hospital', 'School', 'IT Park'])}`,
                    metadata: {
                        cityName: city.name,
                        locationType: faker_1.faker.helpers.arrayElement(['residential', 'commercial', 'mixed', 'industrial']),
                        connectivity: faker_1.faker.helpers.arrayElements(['metro', 'bus', 'railway', 'airport'], { min: 1, max: 3 }),
                        averagePrice: faker_1.faker.number.int({ min: 3000, max: 25000 }),
                    }
                };
                try {
                    const location = new this.locationModel(locationData);
                    const savedLocation = await location.save();
                    locations.push(savedLocation);
                    cityLocationCodes.add(locationCode);
                }
                catch (error) {
                    console.error(`❌ Error creating location "${locationName}" in ${city.name}:`, error.message);
                    if (error.code === 11000) {
                        console.log(`🔄 Retrying with different location code...`);
                        const retryLocationCode = this.generateUniqueLocationCode(city.name, cityLocationCodes, true);
                        try {
                            const retryLocationData = {
                                ...locationData,
                                locationCode: retryLocationCode
                            };
                            const retryLocation = new this.locationModel(retryLocationData);
                            const savedRetryLocation = await retryLocation.save();
                            locations.push(savedRetryLocation);
                            cityLocationCodes.add(retryLocationCode);
                        }
                        catch (retryError) {
                            console.error(`❌ Retry failed for location "${locationName}":`, retryError.message);
                        }
                    }
                }
            }
        }
        console.log(`✅ Seeded ${locations.length} locations`);
        return locations;
    }
    async seedAmenities() {
        console.log('🏊 Seeding amenities...');
        const amenitiesData = [
            { name: 'Swimming Pool', category: master_types_enum_1.AmenityCategory.SPORTS, icon: 'pool', importance: 5 },
            { name: 'Gymnasium', category: master_types_enum_1.AmenityCategory.SPORTS, icon: 'gym', importance: 4 },
            { name: 'Tennis Court', category: master_types_enum_1.AmenityCategory.SPORTS, icon: 'tennis', importance: 3 },
            { name: 'Basketball Court', category: master_types_enum_1.AmenityCategory.SPORTS, icon: 'basketball', importance: 3 },
            { name: 'Badminton Court', category: master_types_enum_1.AmenityCategory.SPORTS, icon: 'badminton', importance: 3 },
            { name: 'Jogging Track', category: master_types_enum_1.AmenityCategory.SPORTS, icon: 'track', importance: 4 },
            { name: 'Yoga Studio', category: master_types_enum_1.AmenityCategory.SPORTS, icon: 'yoga', importance: 3 },
            { name: 'Cricket Pitch', category: master_types_enum_1.AmenityCategory.SPORTS, icon: 'cricket', importance: 2 },
            { name: '24/7 Security', category: master_types_enum_1.AmenityCategory.SECURITY, icon: 'security', importance: 5 },
            { name: 'CCTV Surveillance', category: master_types_enum_1.AmenityCategory.SECURITY, icon: 'cctv', importance: 5 },
            { name: 'Intercom System', category: master_types_enum_1.AmenityCategory.SECURITY, icon: 'intercom', importance: 4 },
            { name: 'Access Control', category: master_types_enum_1.AmenityCategory.SECURITY, icon: 'access', importance: 4 },
            { name: 'Fire Safety', category: master_types_enum_1.AmenityCategory.SECURITY, icon: 'fire', importance: 5 },
            { name: 'Elevator', category: master_types_enum_1.AmenityCategory.CONVENIENCE, icon: 'elevator', importance: 5 },
            { name: 'Power Backup', category: master_types_enum_1.AmenityCategory.CONVENIENCE, icon: 'power', importance: 5 },
            { name: 'Water Supply', category: master_types_enum_1.AmenityCategory.CONVENIENCE, icon: 'water', importance: 5 },
            { name: 'Parking', category: master_types_enum_1.AmenityCategory.CONVENIENCE, icon: 'parking', importance: 5 },
            { name: 'Maintenance Service', category: master_types_enum_1.AmenityCategory.CONVENIENCE, icon: 'maintenance', importance: 4 },
            { name: 'Clubhouse', category: master_types_enum_1.AmenityCategory.COMMUNITY, icon: 'club', importance: 4 },
            { name: 'Party Hall', category: master_types_enum_1.AmenityCategory.COMMUNITY, icon: 'party', importance: 3 },
            { name: 'Library', category: master_types_enum_1.AmenityCategory.COMMUNITY, icon: 'library', importance: 2 },
            { name: 'Game Room', category: master_types_enum_1.AmenityCategory.RECREATIONAL, icon: 'games', importance: 3 },
            { name: 'Home Theater', category: master_types_enum_1.AmenityCategory.RECREATIONAL, icon: 'theater', importance: 2 },
            { name: 'Spa', category: master_types_enum_1.AmenityCategory.WELLNESS, icon: 'spa', importance: 2 },
            { name: 'Sauna', category: master_types_enum_1.AmenityCategory.WELLNESS, icon: 'sauna', importance: 2 },
            { name: 'Meditation Center', category: master_types_enum_1.AmenityCategory.WELLNESS, icon: 'meditation', importance: 2 },
            { name: 'Garden', category: master_types_enum_1.AmenityCategory.RECREATIONAL, icon: 'garden', importance: 4 },
            { name: 'Children\'s Play Area', category: master_types_enum_1.AmenityCategory.RECREATIONAL, icon: 'playground', importance: 4 },
            { name: 'BBQ Area', category: master_types_enum_1.AmenityCategory.RECREATIONAL, icon: 'bbq', importance: 2 },
            { name: 'Terrace Garden', category: master_types_enum_1.AmenityCategory.RECREATIONAL, icon: 'terrace', importance: 3 },
        ];
        const amenities = [];
        for (const amenityData of amenitiesData) {
            const amenity = new this.amenityModel({
                name: amenityData.name,
                masterType: master_types_enum_1.MasterType.AMENITY,
                status: master_types_enum_1.MasterStatus.ACTIVE,
                category: amenityData.category,
                icon: amenityData.icon,
                importanceLevel: amenityData.importance,
                popularityScore: faker_1.faker.number.int({ min: 20, max: 95 }),
                isPopular: amenityData.importance >= 4,
                sortOrder: amenities.length + 1,
                description: `${amenityData.name} facility for residents`,
                tags: this.generateAmenityTags(amenityData.name),
                availability: {
                    residential: true,
                    commercial: Math.random() > 0.5,
                    luxury: amenityData.importance >= 4,
                    basic: amenityData.importance >= 4,
                },
                specifications: {
                    operatingHours: faker_1.faker.helpers.arrayElement(['24/7', '6 AM - 10 PM', '5 AM - 11 PM', '6 AM - 9 PM']),
                    maintenanceFee: faker_1.faker.number.int({ min: 500, max: 5000 }),
                },
                keywords: this.generateAmenityKeywords(amenityData.name),
            });
            amenities.push(await amenity.save());
        }
        console.log(`✅ Seeded ${amenities.length} amenities`);
        return amenities;
    }
    generatePinCodes() {
        const count = faker_1.faker.number.int({ min: 5, max: 15 });
        const pinCodes = [];
        for (let i = 0; i < count; i++) {
            pinCodes.push(faker_1.faker.location.zipCode('######'));
        }
        return pinCodes;
    }
    getStateLanguage(state) {
        const languages = {
            'Maharashtra': 'Marathi',
            'Delhi': 'Hindi',
            'Karnataka': 'Kannada',
            'Telangana': 'Telugu',
            'Tamil Nadu': 'Tamil',
            'West Bengal': 'Bengali',
            'Gujarat': 'Gujarati',
            'Rajasthan': 'Hindi',
            'Uttar Pradesh': 'Hindi',
            'Madhya Pradesh': 'Hindi',
            'Andhra Pradesh': 'Telugu',
            'Bihar': 'Hindi',
        };
        return languages[state] || 'Hindi';
    }
    generateLocationName(cityName) {
        const prefixes = ['New', 'Old', 'East', 'West', 'North', 'South', 'Central'];
        const suffixes = ['Nagar', 'Colony', 'Park', 'Gardens', 'Heights', 'Plaza', 'Residency', 'Enclave'];
        if (Math.random() > 0.5) {
            return `${faker_1.faker.helpers.arrayElement(prefixes)} ${faker_1.faker.person.lastName()} ${faker_1.faker.helpers.arrayElement(suffixes)}`;
        }
        else {
            return `${faker_1.faker.person.lastName()} ${faker_1.faker.helpers.arrayElement(suffixes)}`;
        }
    }
    generateUniqueLocationCode(cityName, usedCodes, isRetry = false) {
        const cityPrefix = cityName.substring(0, 3).toUpperCase();
        let attempts = 0;
        const maxAttempts = 100;
        while (attempts < maxAttempts) {
            let locationCode;
            if (isRetry || attempts > 10) {
                locationCode = `${cityPrefix}${faker_1.faker.string.alphanumeric(4).toUpperCase()}`;
            }
            else {
                const randomNum = faker_1.faker.number.int({ min: 100, max: 999 });
                locationCode = `${cityPrefix}${randomNum}`;
            }
            if (!usedCodes.has(locationCode)) {
                return locationCode;
            }
            attempts++;
        }
        const timestamp = Date.now().toString().slice(-4);
        return `${cityPrefix}${timestamp}`;
    }
    generateNearbyCoordinates(baseCoords) {
        const [baseLng, baseLat] = baseCoords;
        const offset = 0.1;
        return [
            baseLng + (Math.random() - 0.5) * offset,
            baseLat + (Math.random() - 0.5) * offset
        ];
    }
    generateAmenityTags(name) {
        const baseTags = name.toLowerCase().split(' ');
        const additionalTags = ['facility', 'amenity', 'service'];
        return [...baseTags, ...faker_1.faker.helpers.arrayElements(additionalTags, { min: 1, max: 2 })];
    }
    generateAmenityKeywords(name) {
        const baseKeywords = name.toLowerCase().split(' ');
        const relatedKeywords = {
            'swimming': ['pool', 'water', 'swim', 'aquatic'],
            'gym': ['fitness', 'workout', 'exercise', 'health'],
            'security': ['safety', 'guard', 'protection', 'surveillance'],
            'parking': ['car', 'vehicle', 'garage', 'space'],
            'garden': ['green', 'plants', 'nature', 'outdoor'],
        };
        let keywords = [...baseKeywords];
        for (const [key, values] of Object.entries(relatedKeywords)) {
            if (name.toLowerCase().includes(key)) {
                keywords.push(...values);
            }
        }
        return keywords;
    }
    async seedFloors() {
        console.log('🏢 Seeding floors...');
        const floorsData = [
            { name: 'Basement 2', numericValue: -2, type: 'basement' },
            { name: 'Basement 1', numericValue: -1, type: 'basement' },
            { name: 'Ground Floor', numericValue: 0, type: 'ground' },
            { name: '1st Floor', numericValue: 1, type: 'upper' },
            { name: '2nd Floor', numericValue: 2, type: 'upper' },
            { name: '3rd Floor', numericValue: 3, type: 'upper' },
            { name: '4th Floor', numericValue: 4, type: 'upper' },
            { name: '5th Floor', numericValue: 5, type: 'upper' },
            { name: '10th Floor', numericValue: 10, type: 'upper' },
            { name: '15th Floor', numericValue: 15, type: 'upper' },
            { name: '20th Floor', numericValue: 20, type: 'premium' },
            { name: '25th Floor', numericValue: 25, type: 'premium' },
            { name: '30th Floor', numericValue: 30, type: 'premium' },
            { name: 'Penthouse', numericValue: 35, type: 'premium' },
        ];
        const floors = [];
        for (const floorData of floorsData) {
            const floor = new this.floorModel({
                name: floorData.name,
                masterType: master_types_enum_1.MasterType.FLOOR,
                status: master_types_enum_1.MasterStatus.ACTIVE,
                numericValue: floorData.numericValue,
                sortOrder: floors.length + 1,
                isPopular: floorData.numericValue >= 0 && floorData.numericValue <= 10,
                metadata: {
                    type: floorData.type,
                    usage: faker_1.faker.helpers.arrayElement(['residential', 'commercial', 'mixed']),
                    premium: floorData.type === 'premium',
                }
            });
            floors.push(await floor.save());
        }
        console.log(`✅ Seeded ${floors.length} floors`);
        return floors;
    }
    async seedTowers() {
        console.log('🏗️ Seeding towers...');
        const towersData = [
            { name: 'Tower A', numericValue: 1 },
            { name: 'Tower B', numericValue: 2 },
            { name: 'Tower C', numericValue: 3 },
            { name: 'Tower D', numericValue: 4 },
            { name: 'Tower E', numericValue: 5 },
            { name: 'North Tower', numericValue: 6 },
            { name: 'South Tower', numericValue: 7 },
            { name: 'East Tower', numericValue: 8 },
            { name: 'West Tower', numericValue: 9 },
            { name: 'Central Tower', numericValue: 10 },
        ];
        const towers = [];
        for (const towerData of towersData) {
            const tower = new this.towerModel({
                name: towerData.name,
                masterType: master_types_enum_1.MasterType.TOWER,
                status: master_types_enum_1.MasterStatus.ACTIVE,
                numericValue: towerData.numericValue,
                sortOrder: towers.length + 1,
                isPopular: Math.random() > 0.3,
                metadata: {
                    type: faker_1.faker.helpers.arrayElement(['residential', 'commercial', 'mixed']),
                    floors: faker_1.faker.number.int({ min: 10, max: 40 }),
                    unitsPerFloor: faker_1.faker.number.int({ min: 2, max: 8 }),
                }
            });
            towers.push(await tower.save());
        }
        console.log(`✅ Seeded ${towers.length} towers`);
        return towers;
    }
    async seedPropertyTypes() {
        console.log('🏠 Seeding property types...');
        const propertyTypesData = [
            { name: 'Apartment', category: 'residential' },
            { name: 'Villa', category: 'residential' },
            { name: 'Penthouse', category: 'residential' },
            { name: 'Studio', category: 'residential' },
            { name: 'Duplex', category: 'residential' },
            { name: 'Triplex', category: 'residential' },
            { name: 'Office Space', category: 'commercial' },
            { name: 'Retail Shop', category: 'commercial' },
            { name: 'Warehouse', category: 'commercial' },
            { name: 'Showroom', category: 'commercial' },
            { name: 'Restaurant Space', category: 'commercial' },
            { name: 'Co-working Space', category: 'commercial' },
        ];
        const propertyTypes = [];
        for (const typeData of propertyTypesData) {
            const propertyType = new this.propertyTypeModel({
                name: typeData.name,
                masterType: master_types_enum_1.MasterType.PROPERTY_TYPE,
                status: master_types_enum_1.MasterStatus.ACTIVE,
                category: typeData.category,
                sortOrder: propertyTypes.length + 1,
                isPopular: ['Apartment', 'Villa', 'Office Space'].includes(typeData.name),
                metadata: {
                    category: typeData.category,
                    targetAudience: typeData.category === 'residential' ? 'families' : 'businesses',
                }
            });
            propertyTypes.push(await propertyType.save());
        }
        console.log(`✅ Seeded ${propertyTypes.length} property types`);
        return propertyTypes;
    }
    async seedRooms() {
        console.log('🛏️ Seeding rooms...');
        const roomsData = [
            { name: 'Studio', numericValue: 0 },
            { name: '1 BHK', numericValue: 1 },
            { name: '2 BHK', numericValue: 2 },
            { name: '3 BHK', numericValue: 3 },
            { name: '4 BHK', numericValue: 4 },
            { name: '5 BHK', numericValue: 5 },
            { name: '6+ BHK', numericValue: 6 },
        ];
        const rooms = [];
        for (const roomData of roomsData) {
            const room = new this.roomModel({
                name: roomData.name,
                masterType: master_types_enum_1.MasterType.ROOM,
                status: master_types_enum_1.MasterStatus.ACTIVE,
                numericValue: roomData.numericValue,
                sortOrder: rooms.length + 1,
                isPopular: roomData.numericValue >= 1 && roomData.numericValue <= 3,
                metadata: {
                    type: roomData.numericValue === 0 ? 'studio' : 'bhk',
                    targetFamily: this.getTargetFamily(roomData.numericValue),
                }
            });
            rooms.push(await room.save());
        }
        console.log(`✅ Seeded ${rooms.length} rooms`);
        return rooms;
    }
    async seedWashrooms() {
        console.log('🚿 Seeding washrooms...');
        const washroomsData = [
            { name: '1 Bathroom', numericValue: 1 },
            { name: '2 Bathrooms', numericValue: 2 },
            { name: '3 Bathrooms', numericValue: 3 },
            { name: '4 Bathrooms', numericValue: 4 },
            { name: '5+ Bathrooms', numericValue: 5 },
        ];
        const washrooms = [];
        for (const washroomData of washroomsData) {
            const washroom = new this.washroomModel({
                name: washroomData.name,
                masterType: master_types_enum_1.MasterType.WASHROOM,
                status: master_types_enum_1.MasterStatus.ACTIVE,
                numericValue: washroomData.numericValue,
                sortOrder: washrooms.length + 1,
                isPopular: washroomData.numericValue >= 1 && washroomData.numericValue <= 3,
                metadata: {
                    type: washroomData.numericValue === 1 ? 'single' : 'multiple',
                }
            });
            washrooms.push(await washroom.save());
        }
        console.log(`✅ Seeded ${washrooms.length} washrooms`);
        return washrooms;
    }
    getTargetFamily(bedrooms) {
        if (bedrooms === 0)
            return 'single/couple';
        if (bedrooms === 1)
            return 'couple/small family';
        if (bedrooms === 2)
            return 'small family';
        if (bedrooms === 3)
            return 'medium family';
        return 'large family';
    }
    async seedBuilders() {
        console.log('🏗️ Seeding builders...');
        const buildersData = [
            'Lodha Group', 'Godrej Properties', 'DLF Limited', 'Oberoi Realty', 'Prestige Group',
            'Brigade Group', 'Sobha Limited', 'Puravankara Limited', 'Mahindra Lifespace',
            'Tata Housing', 'Hiranandani Group', 'Shapoorji Pallonji', 'L&T Realty',
            'Embassy Group', 'Phoenix Mills', 'Kolte-Patil', 'Rohan Builders',
            'Kalpataru Limited', 'Piramal Realty', 'Runwal Group', 'Rustomjee Group',
            'Shriram Properties', 'Nitesh Estates', 'Salarpuria Sattva', 'Mantri Developers'
        ];
        const builders = [];
        for (const builderName of buildersData) {
            const builder = new this.builderModel({
                name: builderName,
                description: `${builderName} is a leading real estate developer known for quality construction and timely delivery of projects across India.`,
                website: `https://www.${builderName.toLowerCase().replace(/\s+/g, '').replace(/[^a-z0-9]/g, '')}.com`,
                contactEmail: `info@${builderName.toLowerCase().replace(/\s+/g, '').replace(/[^a-z0-9]/g, '')}.com`,
                contactPhone: `+91-${faker_1.faker.string.numeric(10)}`,
                logo: `https://example.com/logos/${builderName.toLowerCase().replace(/\s+/g, '-')}.png`,
            });
            builders.push(await builder.save());
        }
        console.log(`✅ Seeded ${builders.length} builders`);
        return builders;
    }
    async seedAgents() {
        console.log('👥 Seeding agents...');
        const agents = [];
        const agentCount = 50;
        for (let i = 0; i < agentCount; i++) {
            const firstName = faker_1.faker.person.firstName();
            const lastName = faker_1.faker.person.lastName();
            const agent = new this.agentModel({
                name: `${firstName} ${lastName}`,
                description: `Experienced real estate agent specializing in ${faker_1.faker.helpers.arrayElement(['residential', 'commercial', 'luxury'])} properties with ${faker_1.faker.number.int({ min: 2, max: 15 })} years of experience.`,
                email: faker_1.faker.internet.email({ firstName, lastName }),
                phone: `+91-${faker_1.faker.string.numeric(10)}`,
                address: faker_1.faker.location.streetAddress({ useFullAddress: true }),
                licenseNumber: `REA${faker_1.faker.string.alphanumeric(8).toUpperCase()}`,
                profileImage: faker_1.faker.image.avatar(),
                isActive: Math.random() > 0.1,
            });
            agents.push(await agent.save());
        }
        console.log(`✅ Seeded ${agents.length} agents`);
        return agents;
    }
    async seedProjects(cities, locations, amenities, builders) {
        console.log('🏢 Seeding projects...');
        const projects = [];
        const projectCount = 100;
        for (let i = 0; i < projectCount; i++) {
            const city = faker_1.faker.helpers.arrayElement(cities);
            const cityLocations = locations.filter(loc => loc.parentId?.toString() === city._id.toString());
            const location = faker_1.faker.helpers.arrayElement(cityLocations);
            const builder = faker_1.faker.helpers.arrayElement(builders);
            const selectedAmenities = faker_1.faker.helpers.arrayElements(amenities, { min: 5, max: 15 });
            const projectName = this.generateProjectName();
            const propertyType = faker_1.faker.helpers.arrayElement(Object.values(project_status_enum_1.PropertyType));
            const unitConfigs = this.generateUnitConfigurations(propertyType);
            const project = new this.projectModel({
                projectName,
                projectDescription: `${projectName} is a premium ${propertyType.toLowerCase()} project by ${builder.name} located in the heart of ${location.name}, ${city.name}. This project offers world-class amenities and modern living spaces designed for contemporary lifestyle.`,
                builder: {
                    name: builder.name,
                    description: builder.description,
                    website: builder.website,
                    contactEmail: builder.contactEmail,
                    contactPhone: builder.contactPhone,
                    logo: builder.logo,
                },
                projectStatus: faker_1.faker.helpers.arrayElement(Object.values(project_status_enum_1.ProjectStatus)),
                location: {
                    address: faker_1.faker.location.streetAddress(),
                    cityId: city._id,
                    locationId: location._id,
                    state: city.state,
                    country: city.country,
                    pincode: faker_1.faker.location.zipCode('######'),
                    landmark: `Near ${faker_1.faker.helpers.arrayElement(['Metro Station', 'Shopping Mall', 'Hospital', 'School', 'IT Park'])}`,
                    coordinates: location.coordinates,
                },
                reraNumber: `RERA${faker_1.faker.string.alphanumeric(10).toUpperCase()}`,
                propertyType,
                unitConfigurations: unitConfigs,
                possessionStatus: faker_1.faker.helpers.arrayElement(Object.values(project_status_enum_1.PossessionStatus)),
                possessionDate: faker_1.faker.date.future({ years: 3 }),
                totalArea: faker_1.faker.number.int({ min: 1, max: 50 }),
                totalUnits: faker_1.faker.number.int({ min: 50, max: 500 }),
                totalFloors: faker_1.faker.number.int({ min: 5, max: 40 }),
                totalTowers: faker_1.faker.number.int({ min: 1, max: 8 }),
                priceMin: Math.min(...unitConfigs.map(u => u.priceMin)),
                priceMax: Math.max(...unitConfigs.map(u => u.priceMax)),
                pricePerSqFt: faker_1.faker.number.int({ min: 3000, max: 15000 }),
                amenities: {
                    amenityIds: selectedAmenities.map(a => a._id),
                },
                media: {
                    images: this.generateMediaUrls('images', faker_1.faker.number.int({ min: 5, max: 20 })),
                    videos: this.generateMediaUrls('videos', faker_1.faker.number.int({ min: 1, max: 5 })),
                    brochures: this.generateMediaUrls('brochures', faker_1.faker.number.int({ min: 1, max: 3 })),
                    floorPlans: this.generateMediaUrls('floor-plans', faker_1.faker.number.int({ min: 2, max: 8 })),
                    masterPlan: this.generateMediaUrls('master-plan', 1),
                    locationMap: this.generateMediaUrls('location-map', 1),
                },
                documents: {
                    approvals: this.generateMediaUrls('approvals', faker_1.faker.number.int({ min: 1, max: 5 })),
                    legalDocuments: this.generateMediaUrls('legal', faker_1.faker.number.int({ min: 1, max: 3 })),
                    certificates: this.generateMediaUrls('certificates', faker_1.faker.number.int({ min: 1, max: 4 })),
                    others: this.generateMediaUrls('others', faker_1.faker.number.int({ min: 0, max: 2 })),
                },
                tags: this.generateProjectTags(propertyType, city.name),
                highlights: `Premium ${propertyType.toLowerCase()} with ${selectedAmenities.length}+ amenities in ${city.name}`,
                approvalStatus: faker_1.faker.helpers.arrayElement(Object.values(project_status_enum_1.ApprovalStatus)),
                nearbyFacilities: this.generateNearbyFacilities(),
                isActive: Math.random() > 0.05,
                isFeatured: Math.random() > 0.7,
                viewCount: faker_1.faker.number.int({ min: 0, max: 10000 }),
                inquiryCount: faker_1.faker.number.int({ min: 0, max: 500 }),
            });
            projects.push(await project.save());
        }
        console.log(`✅ Seeded ${projects.length} projects`);
        return projects;
    }
    generateProjectName() {
        const prefixes = ['Royal', 'Grand', 'Elite', 'Premium', 'Luxury', 'Golden', 'Silver', 'Diamond', 'Platinum', 'Crown'];
        const middle = ['Heights', 'Residency', 'Gardens', 'Plaza', 'Towers', 'Enclave', 'Paradise', 'Vista', 'Palms', 'Springs'];
        const suffixes = ['Phase 1', 'Phase 2', 'Phase 3', 'Block A', 'Block B', 'Extension', 'Annexe', ''];
        const prefix = faker_1.faker.helpers.arrayElement(prefixes);
        const mid = faker_1.faker.helpers.arrayElement(middle);
        const suffix = faker_1.faker.helpers.arrayElement(suffixes);
        return `${prefix} ${mid}${suffix ? ' ' + suffix : ''}`.trim();
    }
    generateUnitConfigurations(propertyType) {
        const configs = [];
        let unitTypes;
        if (propertyType === project_status_enum_1.PropertyType.COMMERCIAL) {
            unitTypes = [project_status_enum_1.UnitType.OFFICE, project_status_enum_1.UnitType.SHOP, project_status_enum_1.UnitType.WAREHOUSE];
        }
        else {
            unitTypes = [project_status_enum_1.UnitType.STUDIO, project_status_enum_1.UnitType.APARTMENT, project_status_enum_1.UnitType.VILLA, project_status_enum_1.UnitType.PENTHOUSE];
        }
        const numConfigs = faker_1.faker.number.int({ min: 1, max: 3 });
        const selectedTypes = faker_1.faker.helpers.arrayElements(unitTypes, { min: numConfigs, max: numConfigs });
        for (const unitType of selectedTypes) {
            const bedrooms = this.getBedroomsFromUnitType(unitType);
            const bathrooms = Math.min(bedrooms + 1, bedrooms === 0 ? 1 : bedrooms);
            const carpetArea = this.getCarpetAreaRange(bedrooms, unitType);
            const priceRange = this.getPriceRange(carpetArea, propertyType);
            configs.push({
                type: unitType,
                name: this.getUnitDisplayName(unitType, bedrooms),
                bedrooms,
                bathrooms,
                balconies: bedrooms > 0 ? faker_1.faker.number.int({ min: 1, max: Math.max(1, bedrooms - 1) }) : 0,
                carpetArea,
                builtUpArea: Math.round(carpetArea * 1.2),
                superBuiltUpArea: Math.round(carpetArea * 1.4),
                priceMin: priceRange.min,
                priceMax: priceRange.max,
                totalUnits: faker_1.faker.number.int({ min: 10, max: 100 }),
                availableUnits: faker_1.faker.number.int({ min: 5, max: 50 }),
                facing: faker_1.faker.helpers.arrayElements(['NORTH', 'SOUTH', 'EAST', 'WEST'], { min: 1, max: 2 }),
                floorPlans: this.generateMediaUrls('floor-plans', faker_1.faker.number.int({ min: 1, max: 3 })),
            });
        }
        return configs;
    }
    getBedroomsFromUnitType(unitType) {
        const mapping = {
            [project_status_enum_1.UnitType.STUDIO]: 0,
            [project_status_enum_1.UnitType.APARTMENT]: faker_1.faker.number.int({ min: 1, max: 4 }),
            [project_status_enum_1.UnitType.VILLA]: faker_1.faker.number.int({ min: 3, max: 6 }),
            [project_status_enum_1.UnitType.PENTHOUSE]: faker_1.faker.number.int({ min: 3, max: 5 }),
            [project_status_enum_1.UnitType.OFFICE]: 0,
            [project_status_enum_1.UnitType.SHOP]: 0,
            [project_status_enum_1.UnitType.WAREHOUSE]: 0,
            [project_status_enum_1.UnitType.PLOT]: 0,
        };
        return mapping[unitType] || 2;
    }
    getUnitDisplayName(unitType, bedrooms) {
        if (unitType === project_status_enum_1.UnitType.STUDIO)
            return 'Studio';
        if (unitType === project_status_enum_1.UnitType.OFFICE)
            return 'Office Space';
        if (unitType === project_status_enum_1.UnitType.SHOP)
            return 'Retail Shop';
        if (unitType === project_status_enum_1.UnitType.WAREHOUSE)
            return 'Warehouse';
        if (unitType === project_status_enum_1.UnitType.PLOT)
            return 'Plot';
        if (unitType === project_status_enum_1.UnitType.PENTHOUSE)
            return `${bedrooms} BHK Penthouse`;
        if (unitType === project_status_enum_1.UnitType.VILLA)
            return `${bedrooms} BHK Villa`;
        return `${bedrooms} BHK Apartment`;
    }
    getCarpetAreaRange(bedrooms, unitType) {
        if (unitType === project_status_enum_1.UnitType.OFFICE) {
            return faker_1.faker.number.int({ min: 200, max: 2000 });
        }
        if (unitType === project_status_enum_1.UnitType.SHOP) {
            return faker_1.faker.number.int({ min: 100, max: 800 });
        }
        if (unitType === project_status_enum_1.UnitType.WAREHOUSE) {
            return faker_1.faker.number.int({ min: 1000, max: 10000 });
        }
        if (unitType === project_status_enum_1.UnitType.PLOT) {
            return faker_1.faker.number.int({ min: 1000, max: 5000 });
        }
        const ranges = {
            0: { min: 300, max: 500 },
            1: { min: 500, max: 700 },
            2: { min: 700, max: 1200 },
            3: { min: 1200, max: 1800 },
            4: { min: 1800, max: 2500 },
            5: { min: 2500, max: 3500 },
            6: { min: 3500, max: 5000 },
        };
        let multiplier = 1;
        if (unitType === project_status_enum_1.UnitType.VILLA)
            multiplier = 1.5;
        if (unitType === project_status_enum_1.UnitType.PENTHOUSE)
            multiplier = 1.3;
        const range = ranges[bedrooms] || ranges[2];
        return Math.round(faker_1.faker.number.int({ min: range.min, max: range.max }) * multiplier);
    }
    getPriceRange(carpetArea, propertyType) {
        const basePrice = carpetArea * faker_1.faker.number.int({ min: 4000, max: 12000 });
        const variation = basePrice * 0.2;
        const multiplier = propertyType === project_status_enum_1.PropertyType.RESIDENTIAL ? 1.2 :
            propertyType === project_status_enum_1.PropertyType.COMMERCIAL ? 1.5 : 1;
        return {
            min: Math.round((basePrice - variation) * multiplier),
            max: Math.round((basePrice + variation) * multiplier),
        };
    }
    generateMediaUrls(type, count) {
        const urls = [];
        for (let i = 0; i < count; i++) {
            urls.push(`https://trelax-media.s3.amazonaws.com/${type}/${faker_1.faker.string.uuid()}.${this.getFileExtension(type)}`);
        }
        return urls;
    }
    getFileExtension(type) {
        const extensions = {
            'images': 'jpg',
            'videos': 'mp4',
            'brochures': 'pdf',
            'floor-plans': 'jpg',
            'master-plan': 'jpg',
            'location-map': 'jpg',
            'approvals': 'pdf',
            'legal': 'pdf',
            'certificates': 'pdf',
            'others': 'pdf',
        };
        return extensions[type] || 'jpg';
    }
    generateProjectTags(propertyType, cityName) {
        const baseTags = [propertyType.toLowerCase(), cityName.toLowerCase()];
        const additionalTags = [
            'luxury', 'premium', 'modern', 'spacious', 'well-connected', 'gated-community',
            'ready-to-move', 'under-construction', 'investment', 'family-friendly'
        ];
        return [...baseTags, ...faker_1.faker.helpers.arrayElements(additionalTags, { min: 3, max: 6 })];
    }
    generateNearbyFacilities() {
        const facilities = [
            'Metro Station - 500m', 'Shopping Mall - 1km', 'Hospital - 800m', 'School - 300m',
            'IT Park - 2km', 'Airport - 15km', 'Railway Station - 3km', 'Bus Stop - 200m',
            'Bank - 400m', 'Restaurant - 100m', 'Pharmacy - 250m', 'Gym - 150m',
            'Park - 300m', 'Temple - 600m', 'Market - 500m', 'ATM - 100m'
        ];
        return faker_1.faker.helpers.arrayElements(facilities, { min: 5, max: 10 });
    }
};
exports.ComprehensiveSeeder = ComprehensiveSeeder;
exports.ComprehensiveSeeder = ComprehensiveSeeder = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(city_schema_1.City.name)),
    __param(1, (0, mongoose_1.InjectModel)(location_schema_1.Location.name)),
    __param(2, (0, mongoose_1.InjectModel)(amenity_schema_1.Amenity.name)),
    __param(3, (0, mongoose_1.InjectModel)(floor_schema_1.Floor.name)),
    __param(4, (0, mongoose_1.InjectModel)(tower_schema_1.Tower.name)),
    __param(5, (0, mongoose_1.InjectModel)(property_type_schema_1.PropertyType.name)),
    __param(6, (0, mongoose_1.InjectModel)(room_schema_1.Room.name)),
    __param(7, (0, mongoose_1.InjectModel)(washroom_schema_1.Washroom.name)),
    __param(8, (0, mongoose_1.InjectModel)(project_schema_1.Project.name)),
    __param(9, (0, mongoose_1.InjectModel)(builder_schema_1.Builder.name)),
    __param(10, (0, mongoose_1.InjectModel)(agent_schema_1.Agent.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model])
], ComprehensiveSeeder);
//# sourceMappingURL=comprehensive.seeder.js.map