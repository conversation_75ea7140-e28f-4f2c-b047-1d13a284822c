import { WashroomsService } from './washrooms.service';
import { CreateWashroomDto } from './dto/create-washroom.dto';
import { UpdateWashroomDto } from './dto/update-washroom.dto';
import { QueryMasterWithNumericRangeDto } from '../common/dto/query-master.dto';
export declare class WashroomsController {
    private readonly washroomsService;
    constructor(washroomsService: WashroomsService);
    create(createWashroomDto: CreateWashroomDto): Promise<{
        success: boolean;
        data: import("./schemas/washroom.schema").Washroom;
        message: string;
    }>;
    findAll(queryDto: QueryMasterWithNumericRangeDto): Promise<import("../common/interfaces/base-master.interface").MasterListResponse<import("./schemas/washroom.schema").Washroom>>;
    getStatistics(): Promise<{
        success: boolean;
        data: {
            byWashroomType: any;
            byPopularityRating: any;
            totalCount: number;
            activeCount: number;
            inactiveCount: number;
            popularCount: number;
            defaultCount: number;
            byCategory?: Record<string, number>;
            byStatus?: Record<string, number>;
        };
        message: string;
    }>;
    findByType(type: string): Promise<{
        success: boolean;
        data: import("./schemas/washroom.schema").Washroom[];
        message: string;
    }>;
    findOne(id: string): Promise<{
        success: boolean;
        data: import("./schemas/washroom.schema").Washroom;
        message: string;
    }>;
    update(id: string, updateWashroomDto: UpdateWashroomDto): Promise<{
        success: boolean;
        data: import("./schemas/washroom.schema").Washroom;
        message: string;
    }>;
    remove(id: string): Promise<{
        success: boolean;
        message: string;
    }>;
}
