import { TowersService } from './towers.service';
import { CreateTowerDto } from './dto/create-tower.dto';
import { UpdateTowerDto } from './dto/update-tower.dto';
import { QueryMasterWithNumericRangeDto } from '../common/dto/query-master.dto';
export declare class TowersController {
    private readonly towersService;
    constructor(towersService: TowersService);
    create(createTowerDto: CreateTowerDto): Promise<{
        success: boolean;
        data: import("./schemas/tower.schema").Tower;
        message: string;
    }>;
    findAll(queryDto: QueryMasterWithNumericRangeDto): Promise<import("../common/interfaces/base-master.interface").MasterListResponse<import("./schemas/tower.schema").Tower>>;
    getStatistics(): Promise<{
        success: boolean;
        data: {
            activeTowersCount: number;
            totalUnitsAcrossAllTowers: any;
            byTowerType: any;
            totalCount: number;
            activeCount: number;
            inactiveCount: number;
            popularCount: number;
            defaultCount: number;
            byCategory?: Record<string, number>;
            byStatus?: Record<string, number>;
        };
        message: string;
    }>;
    findActive(): Promise<{
        success: boolean;
        data: import("./schemas/tower.schema").Tower[];
        message: string;
    }>;
    findByType(type: string): Promise<{
        success: boolean;
        data: import("./schemas/tower.schema").Tower[];
        message: string;
    }>;
    findOne(id: string): Promise<{
        success: boolean;
        data: import("./schemas/tower.schema").Tower;
        message: string;
    }>;
    update(id: string, updateTowerDto: UpdateTowerDto): Promise<{
        success: boolean;
        data: import("./schemas/tower.schema").Tower;
        message: string;
    }>;
    remove(id: string): Promise<{
        success: boolean;
        message: string;
    }>;
}
