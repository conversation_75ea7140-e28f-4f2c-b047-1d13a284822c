import { AuthService } from './auth.service';
import { LoginDto } from './dto/login.dto';
export declare class AuthController {
    private readonly authService;
    constructor(authService: AuthService);
    login(loginDto: LoginDto): Promise<import("../../common/interfaces/api-response.interface").ApiResponse<{
        user: {
            id: any;
            email: any;
            firstName: any;
            lastName: any;
            role: any;
            isActive: boolean;
        };
        tokens: {
            accessToken: string;
        };
    }>>;
    getProfile(req: any): Promise<import("../../common/interfaces/api-response.interface").ApiResponse<{
        id: string;
        email: string;
        firstName: string;
        lastName: string;
        role: string;
        isActive: boolean;
    }>>;
    refreshToken(req: any): Promise<import("../../common/interfaces/api-response.interface").ApiResponse<{
        accessToken: string;
    }>>;
}
