jest.setTimeout(30000);
beforeAll(async () => {
    process.env.NODE_ENV = 'test';
    process.env.JWT_SECRET = 'test_jwt_secret_key_for_e2e_tests';
    process.env.JWT_EXPIRES_IN = '1h';
    if (!process.env.MONGO_URI_TEST) {
        process.env.MONGO_URI_TEST = process.env.MONGO_URI?.replace(/\/[^\/]*$/, '/trelax_test_db') ||
            'mongodb://localhost:27017/trelax_test_db';
    }
});
afterAll(async () => {
});
if (process.env.SUPPRESS_LOGS === 'true') {
    console.log = jest.fn();
    console.warn = jest.fn();
    console.error = jest.fn();
}
//# sourceMappingURL=setup.js.map