import { MasterStatus, MasterType } from '../enums/master-types.enum';
export declare class BaseMasterDto {
    name: string;
    description?: string;
    code?: string;
    masterType: MasterType;
    status?: MasterStatus;
    sortOrder?: number;
    isDefault?: boolean;
    isPopular?: boolean;
    metadata?: Record<string, any>;
}
export declare class MasterWithParentDto extends BaseMasterDto {
    parentId?: string;
    parentType?: MasterType;
}
export declare class MasterWithCategoryDto extends BaseMasterDto {
    category?: string;
    icon?: string;
    color?: string;
}
export declare class MasterWithNumericValueDto extends BaseMasterDto {
    numericValue?: number;
    unit?: string;
    minValue?: number;
    maxValue?: number;
}
export declare class MasterWithLocationDto extends BaseMasterDto {
    state?: string;
    country?: string;
    coordinates?: [number, number];
    timezone?: string;
    pinCodes?: string[];
}
