import { Model } from 'mongoose';
import { Room, RoomDocument } from './schemas/room.schema';
import { BaseMasterService } from '../common/services/base-master.service';
import { CreateRoomDto } from './dto/create-room.dto';
import { UpdateRoomDto } from './dto/update-room.dto';
import { QueryMasterWithNumericRangeDto } from '../common/dto/query-master.dto';
import { MasterListResponse } from '../common/interfaces/base-master.interface';
export declare class RoomsService extends BaseMasterService<Room> {
    private roomModel;
    constructor(roomModel: Model<RoomDocument>);
    createRoom(createRoomDto: CreateRoomDto): Promise<Room>;
    findAllRooms(queryDto?: QueryMasterWithNumericRangeDto): Promise<MasterListResponse<Room>>;
    findRoomById(id: string): Promise<Room>;
    updateRoom(id: string, updateRoomDto: UpdateRoomDto): Promise<Room>;
    removeRoom(id: string): Promise<void>;
    findRoomsByType(roomType: string): Promise<Room[]>;
    getRoomStatistics(): Promise<{
        byRoomType: any;
        byPopularityRating: any;
        totalCount: number;
        activeCount: number;
        inactiveCount: number;
        popularCount: number;
        defaultCount: number;
        byCategory?: Record<string, number>;
        byStatus?: Record<string, number>;
    }>;
    protected checkUsageBeforeDelete(id: string): Promise<void>;
}
