import { Model } from 'mongoose';
import { Location, LocationDocument } from './schemas/location.schema';
import { BaseMasterService } from '../common/services/base-master.service';
import { CreateLocationDto } from './dto/create-location.dto';
import { UpdateLocationDto } from './dto/update-location.dto';
import { QueryLocationDto } from './dto/query-location.dto';
import { MasterListResponse } from '../common/interfaces/base-master.interface';
export declare class LocationsService extends BaseMasterService<Location> {
    private locationModel;
    constructor(locationModel: Model<LocationDocument>);
    createLocation(createLocationDto: CreateLocationDto): Promise<Location>;
    findAllLocations(queryDto?: QueryLocationDto): Promise<MasterListResponse<Location>>;
    findLocationById(id: string): Promise<Location>;
    updateLocation(id: string, updateLocationDto: UpdateLocationDto): Promise<Location>;
    removeLocation(id: string): Promise<void>;
    findLocationsByCity(cityId: string): Promise<Location[]>;
    findPopularLocations(limit?: number): Promise<Location[]>;
    findLocationsByType(locationType: string): Promise<Location[]>;
    findLocationsNearLocation(longitude: number, latitude: number, maxDistance?: number): Promise<Location[]>;
    getLocationStatistics(): Promise<{
        locationsWithRealEstateData: number;
        averagePropertyPrice: any;
        byLocationType: any;
        byLocationCategory: any;
        topCitiesByLocationCount: {
            cityId: any;
            locationCount: any;
        }[];
        totalCount: number;
        activeCount: number;
        inactiveCount: number;
        popularCount: number;
        defaultCount: number;
        byCategory?: Record<string, number>;
        byStatus?: Record<string, number>;
    }>;
    private validateParentCity;
    protected checkUsageBeforeDelete(id: string): Promise<void>;
}
