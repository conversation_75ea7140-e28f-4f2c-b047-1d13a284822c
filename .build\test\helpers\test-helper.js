"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TestHelper = void 0;
const testing_1 = require("@nestjs/testing");
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const config_1 = require("@nestjs/config");
const request = require("supertest");
const app_module_1 = require("../../src/app.module");
class TestHelper {
    static async initializeApp() {
        if (this.app) {
            return this.app;
        }
        this.moduleFixture = await testing_1.Test.createTestingModule({
            imports: [
                config_1.ConfigModule.forRoot({
                    isGlobal: true,
                    envFilePath: '.env.test',
                }),
                app_module_1.AppModule,
            ],
        })
            .overrideModule(mongoose_1.MongooseModule)
            .useModule(mongoose_1.MongooseModule.forRoot(process.env.MONGO_URI_TEST || 'mongodb://localhost:27017/trelax_test_db'))
            .compile();
        this.app = this.moduleFixture.createNestApplication();
        this.app.useGlobalPipes(new common_1.ValidationPipe({
            whitelist: true,
            forbidNonWhitelisted: true,
            transform: true,
        }));
        this.app.setGlobalPrefix('api/v1');
        await this.app.init();
        return this.app;
    }
    static getApp() {
        if (!this.app) {
            throw new Error('App not initialized. Call initializeApp() first.');
        }
        return this.app;
    }
    static async login(email = '<EMAIL>', password = 'admin123') {
        if (this.authToken) {
            return this.authToken;
        }
        const response = await request(this.app.getHttpServer())
            .post('/api/v1/auth/login')
            .send({ email, password })
            .expect(200);
        this.authToken = response.body.data.token;
        this.testUserId = response.body.data.user.id;
        return this.authToken;
    }
    static async getAuthHeaders() {
        const token = await this.login();
        return { Authorization: `Bearer ${token}` };
    }
    static getTestUserId() {
        return this.testUserId;
    }
    static clearAuth() {
        this.authToken = '';
        this.testUserId = '';
    }
    static async cleanupTestData() {
        console.log('Cleaning up test data...');
    }
    static async closeApp() {
        if (this.app) {
            await this.app.close();
            this.app = null;
            this.moduleFixture = null;
            this.authToken = '';
            this.testUserId = '';
        }
    }
    static createTestCity() {
        return {
            name: `Test City ${Date.now()}`,
            state: 'Test State',
            country: 'India',
            coordinates: [77.1025, 28.7041],
            isPopular: true,
        };
    }
    static createTestLocation(cityId) {
        return {
            name: `Test Location ${Date.now()}`,
            parentId: cityId,
            locationCode: `TST${Date.now().toString().slice(-3)}`,
            coordinates: [77.1025, 28.7041],
            locationType: 'residential',
        };
    }
    static createTestAmenity() {
        return {
            name: `Test Amenity ${Date.now()}`,
            category: 'SPORTS',
            importance: 4,
            icon: 'test-icon',
        };
    }
    static createTestBuilder() {
        return {
            name: `Test Builder ${Date.now()}`,
            description: 'A test real estate builder',
            website: 'https://testbuilder.com',
            contactEmail: '<EMAIL>',
            contactPhone: '+91-**********',
        };
    }
    static createTestAgent() {
        return {
            firstName: 'Test',
            lastName: 'Agent',
            email: `testagent${Date.now()}@example.com`,
            phone: '+91-**********',
            licenseNumber: `LIC${Date.now()}`,
            experience: 5,
        };
    }
    static createTestProject(cityId, locationId, builderId) {
        return {
            projectName: `Test Project ${Date.now()}`,
            projectDescription: 'A comprehensive test project',
            builder: {
                name: 'Test Builder',
                contactEmail: '<EMAIL>',
                contactPhone: '+91-**********',
            },
            projectStatus: 'UNDER_CONSTRUCTION',
            location: {
                address: '123 Test Street',
                cityId,
                locationId,
                state: 'Test State',
                country: 'India',
                pincode: '123456',
                coordinates: [77.1025, 28.7041],
            },
            reraNumber: `RERA${Date.now()}`,
            propertyType: 'RESIDENTIAL',
            totalUnits: 100,
            totalFloors: 10,
            totalTowers: 2,
            priceMin: 5000000,
            priceMax: 15000000,
        };
    }
    static expectSuccessResponse(response, expectedMessage) {
        expect(response.body).toHaveProperty('success', true);
        expect(response.body).toHaveProperty('data');
        if (expectedMessage) {
            expect(response.body.message).toContain(expectedMessage);
        }
    }
    static expectErrorResponse(response, expectedStatus, expectedMessage) {
        expect(response.status).toBe(expectedStatus);
        expect(response.body).toHaveProperty('success', false);
        if (expectedMessage) {
            expect(response.body.message).toContain(expectedMessage);
        }
    }
    static expectValidationError(response) {
        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('success', false);
        expect(response.body.message).toContain('validation');
    }
    static expectUnauthorizedError(response) {
        expect(response.status).toBe(401);
        expect(response.body).toHaveProperty('success', false);
    }
    static expectNotFoundError(response) {
        expect(response.status).toBe(404);
        expect(response.body).toHaveProperty('success', false);
    }
    static async wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}
exports.TestHelper = TestHelper;
//# sourceMappingURL=test-helper.js.map