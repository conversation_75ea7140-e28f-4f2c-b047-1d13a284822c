"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MastersModule = void 0;
const common_1 = require("@nestjs/common");
const cities_module_1 = require("./cities/cities.module");
const locations_module_1 = require("./locations/locations.module");
const amenities_module_1 = require("./amenities/amenities.module");
const floors_module_1 = require("./floors/floors.module");
const towers_module_1 = require("./towers/towers.module");
const property_types_module_1 = require("./property-types/property-types.module");
const rooms_module_1 = require("./rooms/rooms.module");
const washrooms_module_1 = require("./washrooms/washrooms.module");
const uploads_module_1 = require("./uploads/uploads.module");
let MastersModule = class MastersModule {
};
exports.MastersModule = MastersModule;
exports.MastersModule = MastersModule = __decorate([
    (0, common_1.Module)({
        imports: [
            cities_module_1.CitiesModule,
            locations_module_1.LocationsModule,
            amenities_module_1.AmenitiesModule,
            floors_module_1.FloorsModule,
            towers_module_1.TowersModule,
            property_types_module_1.PropertyTypesModule,
            rooms_module_1.RoomsModule,
            washrooms_module_1.WashroomsModule,
            uploads_module_1.UploadsModule,
        ],
        exports: [
            cities_module_1.CitiesModule,
            locations_module_1.LocationsModule,
            amenities_module_1.AmenitiesModule,
            floors_module_1.FloorsModule,
            towers_module_1.TowersModule,
            property_types_module_1.PropertyTypesModule,
            rooms_module_1.RoomsModule,
            washrooms_module_1.WashroomsModule,
            uploads_module_1.UploadsModule,
        ],
    })
], MastersModule);
//# sourceMappingURL=masters.module.js.map