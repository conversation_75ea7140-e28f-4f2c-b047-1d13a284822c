"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CitiesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const cities_service_1 = require("./cities.service");
const create_city_dto_1 = require("./dto/create-city.dto");
const update_city_dto_1 = require("./dto/update-city.dto");
const query_city_dto_1 = require("./dto/query-city.dto");
const city_response_dto_1 = require("./dto/city-response.dto");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let CitiesController = class CitiesController {
    constructor(citiesService) {
        this.citiesService = citiesService;
    }
    async create(createCityDto) {
        const city = await this.citiesService.createCity(createCityDto);
        return {
            success: true,
            data: city,
            message: 'City created successfully'
        };
    }
    async findAll(queryDto) {
        return await this.citiesService.findAllCities(queryDto);
    }
    async getStatistics() {
        const statistics = await this.citiesService.getCityStatistics();
        return {
            success: true,
            data: statistics,
            message: 'City statistics retrieved successfully'
        };
    }
    async findPopular(limit) {
        const cities = await this.citiesService.findPopularCities(limit);
        return {
            success: true,
            data: cities,
            message: 'Popular cities retrieved successfully'
        };
    }
    async findByState(state) {
        const cities = await this.citiesService.findCitiesByState(state);
        return {
            success: true,
            data: cities,
            message: `Cities in ${state} retrieved successfully`
        };
    }
    async findByCountry(country) {
        const cities = await this.citiesService.findCitiesByCountry(country);
        return {
            success: true,
            data: cities,
            message: `Cities in ${country} retrieved successfully`
        };
    }
    async findNearLocation(longitude, latitude, maxDistance) {
        const cities = await this.citiesService.findCitiesNearLocation(longitude, latitude, maxDistance);
        return {
            success: true,
            data: cities,
            message: 'Nearby cities retrieved successfully'
        };
    }
    async findOne(id) {
        const city = await this.citiesService.findCityById(id);
        return {
            success: true,
            data: city,
            message: 'City retrieved successfully'
        };
    }
    async update(id, updateCityDto) {
        const city = await this.citiesService.updateCity(id, updateCityDto);
        return {
            success: true,
            data: city,
            message: 'City updated successfully'
        };
    }
    async remove(id) {
        await this.citiesService.removeCity(id);
        return {
            success: true,
            message: 'City deleted successfully'
        };
    }
};
exports.CitiesController = CitiesController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Create a new city',
        description: 'Create a new city master data entry with comprehensive information including geographical, economic, and real estate data.'
    }),
    (0, swagger_1.ApiBody)({ type: create_city_dto_1.CreateCityDto }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'City created successfully',
        type: city_response_dto_1.SingleCityResponseDto
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid input data'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CONFLICT,
        description: 'City with same name or code already exists'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Body)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_city_dto_1.CreateCityDto]),
    __metadata("design:returntype", Promise)
], CitiesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get all cities',
        description: 'Retrieve all cities with advanced filtering, search, and pagination capabilities.'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Cities retrieved successfully',
        type: city_response_dto_1.CityListResponseDto
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid query parameters'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Query)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [query_city_dto_1.QueryCityDto]),
    __metadata("design:returntype", Promise)
], CitiesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('statistics'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get city statistics',
        description: 'Retrieve comprehensive statistics about cities including counts by state, country, and real estate data.'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'City statistics retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], CitiesController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)('popular'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get popular cities',
        description: 'Retrieve list of popular cities marked as popular in the system.'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Number of popular cities to retrieve',
        example: 10
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Popular cities retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], CitiesController.prototype, "findPopular", null);
__decorate([
    (0, common_1.Get)('by-state/:state'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get cities by state',
        description: 'Retrieve all cities belonging to a specific state.'
    }),
    (0, swagger_1.ApiParam)({
        name: 'state',
        description: 'State name',
        example: 'Maharashtra'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Cities retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Param)('state')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CitiesController.prototype, "findByState", null);
__decorate([
    (0, common_1.Get)('by-country/:country'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get cities by country',
        description: 'Retrieve all cities belonging to a specific country.'
    }),
    (0, swagger_1.ApiParam)({
        name: 'country',
        description: 'Country name',
        example: 'India'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Cities retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Param)('country')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CitiesController.prototype, "findByCountry", null);
__decorate([
    (0, common_1.Get)('near'),
    (0, swagger_1.ApiOperation)({
        summary: 'Find cities near location',
        description: 'Find cities near a specific geographical location using coordinates.'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'longitude',
        required: true,
        type: Number,
        description: 'Longitude coordinate',
        example: 72.8777
    }),
    (0, swagger_1.ApiQuery)({
        name: 'latitude',
        required: true,
        type: Number,
        description: 'Latitude coordinate',
        example: 19.0760
    }),
    (0, swagger_1.ApiQuery)({
        name: 'maxDistance',
        required: false,
        type: Number,
        description: 'Maximum distance in meters',
        example: 100000
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Nearby cities retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid coordinates'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Query)('longitude')),
    __param(1, (0, common_1.Query)('latitude')),
    __param(2, (0, common_1.Query)('maxDistance')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, Number]),
    __metadata("design:returntype", Promise)
], CitiesController.prototype, "findNearLocation", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get city by ID',
        description: 'Retrieve a specific city by its unique identifier.'
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'City ID',
        example: '507f1f77bcf86cd799439011'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'City retrieved successfully',
        type: city_response_dto_1.SingleCityResponseDto
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'City not found'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid city ID'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CitiesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Update city by ID',
        description: 'Update a specific city with new information.'
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'City ID',
        example: '507f1f77bcf86cd799439011'
    }),
    (0, swagger_1.ApiBody)({ type: update_city_dto_1.UpdateCityDto }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'City updated successfully',
        type: city_response_dto_1.SingleCityResponseDto
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'City not found'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid input data or city ID'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CONFLICT,
        description: 'City with same name or code already exists'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_city_dto_1.UpdateCityDto]),
    __metadata("design:returntype", Promise)
], CitiesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Delete city by ID',
        description: 'Soft delete a specific city (marks as archived).'
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'City ID',
        example: '507f1f77bcf86cd799439011'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'City deleted successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'City not found'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Cannot delete city as it is being used'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], CitiesController.prototype, "remove", null);
exports.CitiesController = CitiesController = __decorate([
    (0, swagger_1.ApiTags)('Masters - Cities'),
    (0, common_1.Controller)('masters/cities'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [cities_service_1.CitiesService])
], CitiesController);
//# sourceMappingURL=cities.controller.js.map