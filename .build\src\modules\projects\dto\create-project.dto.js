"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateProjectDto = exports.DocumentsDto = exports.MediaDto = exports.AmenitiesDto = exports.UnitConfigurationDto = exports.BuilderDto = exports.LocationDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const swagger_1 = require("@nestjs/swagger");
const project_status_enum_1 = require("../enums/project-status.enum");
class LocationDto {
}
exports.LocationDto = LocationDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Full address', example: '123 Main Street, Downtown' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], LocationDto.prototype, "address", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'City ID from Masters module',
        example: '507f1f77bcf86cd799439011'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], LocationDto.prototype, "cityId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Location ID from Masters module',
        example: '507f1f77bcf86cd799439012'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], LocationDto.prototype, "locationId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'State name', example: 'Maharashtra' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], LocationDto.prototype, "state", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Country name', example: 'India' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], LocationDto.prototype, "country", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Postal/ZIP code', example: '400001' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], LocationDto.prototype, "pincode", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Nearby landmark', example: 'Near Central Mall' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], LocationDto.prototype, "landmark", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'GPS coordinates [longitude, latitude]',
        example: [72.8777, 19.0760],
        type: [Number],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsNumber)({}, { each: true }),
    __metadata("design:type", Array)
], LocationDto.prototype, "coordinates", void 0);
class BuilderDto {
}
exports.BuilderDto = BuilderDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Builder name', example: 'ABC Constructions' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], BuilderDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Builder description', example: 'Leading real estate developer' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(1000),
    __metadata("design:type", String)
], BuilderDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Builder website', example: 'https://abcconstructions.com' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], BuilderDto.prototype, "website", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Contact email', example: '<EMAIL>' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEmail)(),
    __metadata("design:type", String)
], BuilderDto.prototype, "contactEmail", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Contact phone', example: '+91-9876543210' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BuilderDto.prototype, "contactPhone", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Builder logo URL', example: 'https://s3.amazonaws.com/bucket/logo.jpg' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BuilderDto.prototype, "logo", void 0);
class UnitConfigurationDto {
}
exports.UnitConfigurationDto = UnitConfigurationDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Unit type', enum: project_status_enum_1.UnitType, example: project_status_enum_1.UnitType.APARTMENT }),
    (0, class_validator_1.IsEnum)(project_status_enum_1.UnitType),
    __metadata("design:type", String)
], UnitConfigurationDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Unit name/configuration', example: '2 BHK' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], UnitConfigurationDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of bedrooms', example: 2, minimum: 0 }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], UnitConfigurationDto.prototype, "bedrooms", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of bathrooms', example: 2, minimum: 0 }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], UnitConfigurationDto.prototype, "bathrooms", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of balconies', example: 1, minimum: 0 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], UnitConfigurationDto.prototype, "balconies", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Carpet area in sq ft', example: 850, minimum: 0 }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], UnitConfigurationDto.prototype, "carpetArea", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Built-up area in sq ft', example: 1000, minimum: 0 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], UnitConfigurationDto.prototype, "builtUpArea", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Super built-up area in sq ft', example: 1200, minimum: 0 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], UnitConfigurationDto.prototype, "superBuiltUpArea", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Minimum price', example: 5000000, minimum: 0 }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], UnitConfigurationDto.prototype, "priceMin", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Maximum price', example: 7000000, minimum: 0 }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], UnitConfigurationDto.prototype, "priceMax", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Total units of this configuration', example: 50, minimum: 0 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], UnitConfigurationDto.prototype, "totalUnits", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Available units', example: 25, minimum: 0 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], UnitConfigurationDto.prototype, "availableUnits", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Facing directions',
        enum: project_status_enum_1.FacingDirection,
        isArray: true,
        example: [project_status_enum_1.FacingDirection.NORTH, project_status_enum_1.FacingDirection.EAST],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsEnum)(project_status_enum_1.FacingDirection, { each: true }),
    __metadata("design:type", Array)
], UnitConfigurationDto.prototype, "facing", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Floor plan image URLs',
        type: [String],
        example: ['https://s3.amazonaws.com/bucket/floorplan1.jpg'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], UnitConfigurationDto.prototype, "floorPlans", void 0);
class AmenitiesDto {
}
exports.AmenitiesDto = AmenitiesDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Array of amenity IDs from Masters module',
        example: ['507f1f77bcf86cd799439011', '507f1f77bcf86cd799439012', '507f1f77bcf86cd799439013'],
        type: [String],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], AmenitiesDto.prototype, "amenityIds", void 0);
class MediaDto {
}
exports.MediaDto = MediaDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Project image URLs',
        type: [String],
        example: ['https://s3.amazonaws.com/bucket/image1.jpg'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], MediaDto.prototype, "images", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Project video URLs',
        type: [String],
        example: ['https://s3.amazonaws.com/bucket/video1.mp4'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], MediaDto.prototype, "videos", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Brochure URLs',
        type: [String],
        example: ['https://s3.amazonaws.com/bucket/brochure.pdf'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], MediaDto.prototype, "brochures", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Floor plan URLs',
        type: [String],
        example: ['https://s3.amazonaws.com/bucket/floorplan.jpg'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], MediaDto.prototype, "floorPlans", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Master plan URLs',
        type: [String],
        example: ['https://s3.amazonaws.com/bucket/masterplan.jpg'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], MediaDto.prototype, "masterPlan", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Location map URLs',
        type: [String],
        example: ['https://s3.amazonaws.com/bucket/locationmap.jpg'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], MediaDto.prototype, "locationMap", void 0);
class DocumentsDto {
}
exports.DocumentsDto = DocumentsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Approval document URLs',
        type: [String],
        example: ['https://s3.amazonaws.com/bucket/approval.pdf'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], DocumentsDto.prototype, "approvals", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Legal document URLs',
        type: [String],
        example: ['https://s3.amazonaws.com/bucket/legal.pdf'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], DocumentsDto.prototype, "legalDocuments", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Certificate URLs',
        type: [String],
        example: ['https://s3.amazonaws.com/bucket/certificate.pdf'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], DocumentsDto.prototype, "certificates", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Other document URLs',
        type: [String],
        example: ['https://s3.amazonaws.com/bucket/other.pdf'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], DocumentsDto.prototype, "others", void 0);
class CreateProjectDto {
}
exports.CreateProjectDto = CreateProjectDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Project name', example: 'Luxury Heights Residency' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.MaxLength)(200),
    __metadata("design:type", String)
], CreateProjectDto.prototype, "projectName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Project description', example: 'Premium residential project with modern amenities' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.MaxLength)(2000),
    __metadata("design:type", String)
], CreateProjectDto.prototype, "projectDescription", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Builder information', type: BuilderDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => BuilderDto),
    __metadata("design:type", BuilderDto)
], CreateProjectDto.prototype, "builder", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Project status', enum: project_status_enum_1.ProjectStatus, example: project_status_enum_1.ProjectStatus.PLANNED }),
    (0, class_validator_1.IsEnum)(project_status_enum_1.ProjectStatus),
    __metadata("design:type", String)
], CreateProjectDto.prototype, "projectStatus", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Project location', type: LocationDto }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => LocationDto),
    __metadata("design:type", LocationDto)
], CreateProjectDto.prototype, "location", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'RERA registration number', example: 'RERA123456789' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(50),
    __metadata("design:type", String)
], CreateProjectDto.prototype, "reraNumber", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Property type', enum: project_status_enum_1.PropertyType, example: project_status_enum_1.PropertyType.RESIDENTIAL }),
    (0, class_validator_1.IsEnum)(project_status_enum_1.PropertyType),
    __metadata("design:type", String)
], CreateProjectDto.prototype, "propertyType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Unit configurations',
        type: [UnitConfigurationDto],
        example: [
            {
                type: 'apartment',
                name: '2 BHK',
                bedrooms: 2,
                bathrooms: 2,
                carpetArea: 850,
                priceMin: 5000000,
                priceMax: 7000000,
            },
        ],
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ArrayMinSize)(1),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => UnitConfigurationDto),
    __metadata("design:type", Array)
], CreateProjectDto.prototype, "unitConfigurations", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Possession status', enum: project_status_enum_1.PossessionStatus }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(project_status_enum_1.PossessionStatus),
    __metadata("design:type", String)
], CreateProjectDto.prototype, "possessionStatus", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Expected possession date', example: '2025-12-31' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateProjectDto.prototype, "possessionDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Total project area in sq ft', example: 50000, minimum: 0 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateProjectDto.prototype, "totalArea", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Total number of units', example: 200, minimum: 0 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateProjectDto.prototype, "totalUnits", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Total number of floors', example: 25, minimum: 0 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateProjectDto.prototype, "totalFloors", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Total number of towers', example: 3, minimum: 0 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateProjectDto.prototype, "totalTowers", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Minimum price across all units', example: 3000000, minimum: 0 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateProjectDto.prototype, "priceMin", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Maximum price across all units', example: 15000000, minimum: 0 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateProjectDto.prototype, "priceMax", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Average price per sq ft', example: 8000, minimum: 0 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateProjectDto.prototype, "pricePerSqFt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Project amenities', type: AmenitiesDto }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => AmenitiesDto),
    __metadata("design:type", AmenitiesDto)
], CreateProjectDto.prototype, "amenities", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Project media files', type: MediaDto }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => MediaDto),
    __metadata("design:type", MediaDto)
], CreateProjectDto.prototype, "media", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Project documents', type: DocumentsDto }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => DocumentsDto),
    __metadata("design:type", DocumentsDto)
], CreateProjectDto.prototype, "documents", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Project tags for categorization',
        type: [String],
        example: ['luxury', 'gated-community', 'eco-friendly'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateProjectDto.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Project highlights',
        example: 'Prime location with excellent connectivity and world-class amenities',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(1000),
    __metadata("design:type", String)
], CreateProjectDto.prototype, "highlights", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Approval status', enum: project_status_enum_1.ApprovalStatus }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(project_status_enum_1.ApprovalStatus),
    __metadata("design:type", String)
], CreateProjectDto.prototype, "approvalStatus", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Nearby facilities',
        type: [String],
        example: ['Metro Station - 500m', 'Shopping Mall - 1km', 'Hospital - 2km'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateProjectDto.prototype, "nearbyFacilities", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Whether project is featured', example: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateProjectDto.prototype, "isFeatured", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Project images to upload',
        type: 'array',
        items: { type: 'string', format: 'binary' },
    }),
    __metadata("design:type", Array)
], CreateProjectDto.prototype, "projectImages", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Floor plan images to upload',
        type: 'array',
        items: { type: 'string', format: 'binary' },
    }),
    __metadata("design:type", Array)
], CreateProjectDto.prototype, "floorPlanImages", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Project brochure PDF to upload',
        type: 'string',
        format: 'binary',
    }),
    __metadata("design:type", Object)
], CreateProjectDto.prototype, "brochurePdf", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Additional project documents to upload',
        type: 'array',
        items: { type: 'string', format: 'binary' },
    }),
    __metadata("design:type", Array)
], CreateProjectDto.prototype, "additionalDocuments", void 0);
//# sourceMappingURL=create-project.dto.js.map