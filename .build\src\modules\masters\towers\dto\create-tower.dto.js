"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateTowerDto = exports.TowerSpecificationsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const base_master_dto_1 = require("../../common/dto/base-master.dto");
const master_types_enum_1 = require("../../common/enums/master-types.enum");
class TowerSpecificationsDto {
}
exports.TowerSpecificationsDto = TowerSpecificationsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Structure type', example: 'RCC' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TowerSpecificationsDto.prototype, "structure", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of elevators', example: 2 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], TowerSpecificationsDto.prototype, "elevators", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of staircases', example: 2 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], TowerSpecificationsDto.prototype, "staircases", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Parking levels', example: 2 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], TowerSpecificationsDto.prototype, "parkingLevels", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Total parking spots', example: 100 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], TowerSpecificationsDto.prototype, "totalParkingSpots", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Power backup available', example: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], TowerSpecificationsDto.prototype, "powerBackup", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Water storage capacity in liters', example: 50000 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], TowerSpecificationsDto.prototype, "waterStorage", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Sewage treatment plant', example: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], TowerSpecificationsDto.prototype, "sewageTreatment", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Rainwater harvesting', example: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], TowerSpecificationsDto.prototype, "rainwaterHarvesting", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Solar panels installed', example: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], TowerSpecificationsDto.prototype, "solarPanels", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Green building certified', example: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], TowerSpecificationsDto.prototype, "greenBuilding", void 0);
class CreateTowerDto extends base_master_dto_1.MasterWithNumericValueDto {
    constructor() {
        super(...arguments);
        this.unit = 'Tower';
        this.isActive = true;
        this.masterType = master_types_enum_1.MasterType.TOWER;
    }
}
exports.CreateTowerDto = CreateTowerDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Tower name', example: 'Tower A' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CreateTowerDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Tower description' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(500),
    __metadata("design:type", String)
], CreateTowerDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Tower code', example: 'TA' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(20),
    __metadata("design:type", String)
], CreateTowerDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Tower number', example: 1 }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], CreateTowerDto.prototype, "numericValue", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Unit type', default: 'Tower' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(20),
    __metadata("design:type", String)
], CreateTowerDto.prototype, "unit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Tower type',
        enum: ['residential', 'commercial', 'mixed_use', 'office', 'retail', 'hospitality', 'industrial']
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['residential', 'commercial', 'mixed_use', 'office', 'retail', 'hospitality', 'industrial']),
    __metadata("design:type", String)
], CreateTowerDto.prototype, "towerType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Total floors', example: 20 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(200),
    __metadata("design:type", Number)
], CreateTowerDto.prototype, "totalFloors", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Tower height in feet', example: 200 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(10),
    __metadata("design:type", Number)
], CreateTowerDto.prototype, "height", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Total units', example: 160 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], CreateTowerDto.prototype, "totalUnits", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Units per floor', example: 8 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], CreateTowerDto.prototype, "unitsPerFloor", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ type: TowerSpecificationsDto }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => TowerSpecificationsDto),
    __metadata("design:type", TowerSpecificationsDto)
], CreateTowerDto.prototype, "specifications", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Tower amenities', example: ['Gym', 'Pool'] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateTowerDto.prototype, "amenities", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Special features', example: ['Sea View', 'Corner Units'] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateTowerDto.prototype, "features", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Tower is active', default: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateTowerDto.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Construction year', example: 2023 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1900),
    (0, class_validator_1.Max)(2030),
    __metadata("design:type", Number)
], CreateTowerDto.prototype, "constructionYear", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Architect name', example: 'ABC Architects' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CreateTowerDto.prototype, "architect", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Contractor name', example: 'XYZ Builders' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CreateTowerDto.prototype, "contractor", void 0);
//# sourceMappingURL=create-tower.dto.js.map