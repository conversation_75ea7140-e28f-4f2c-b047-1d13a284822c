import { Document, Types } from 'mongoose';
export type FileDocument = File & Document;
export declare class File {
    filename: string;
    originalName: string;
    mimetype: string;
    size: number;
    url: string;
    key: string;
    bucket: string;
    description?: string;
    tags?: string[];
    uploadedBy: Types.ObjectId;
    isActive: boolean;
    isPublic: boolean;
    expiresAt?: Date;
    downloadCount: number;
    lastAccessedAt?: Date;
    category?: string;
    createdAt?: Date;
    updatedAt?: Date;
}
export declare const FileSchema: import("mongoose").Schema<File, import("mongoose").Model<File, any, any, any, Document<unknown, any, File, any> & File & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, File, Document<unknown, {}, import("mongoose").FlatRecord<File>, {}> & import("mongoose").FlatRecord<File> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
