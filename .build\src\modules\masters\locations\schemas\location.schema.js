"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocationSchema = exports.Location = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const base_master_schema_1 = require("../../common/schemas/base-master.schema");
const master_types_enum_1 = require("../../common/enums/master-types.enum");
let Location = class Location extends base_master_schema_1.MasterWithParent {
};
exports.Location = Location;
__decorate([
    (0, mongoose_1.Prop)({
        required: true,
        type: String,
        enum: Object.values(master_types_enum_1.MasterType),
        default: master_types_enum_1.MasterType.LOCATION,
        immutable: true
    }),
    __metadata("design:type", String)
], Location.prototype, "masterType", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: true,
        type: mongoose_2.Types.ObjectId,
        ref: 'City'
    }),
    __metadata("design:type", mongoose_2.Types.ObjectId)
], Location.prototype, "parentId", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: true,
        type: String,
        enum: Object.values(master_types_enum_1.MasterType),
        default: master_types_enum_1.MasterType.CITY,
        immutable: true
    }),
    __metadata("design:type", String)
], Location.prototype, "parentType", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 20
    }),
    __metadata("design:type", String)
], Location.prototype, "locationCode", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: [Number],
        validate: [arrayLimit, 'Coordinates must have exactly 2 elements']
    }),
    __metadata("design:type", Array)
], Location.prototype, "coordinates", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 200
    }),
    __metadata("design:type", String)
], Location.prototype, "address", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 20
    }),
    __metadata("design:type", String)
], Location.prototype, "pincode", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 100
    }),
    __metadata("design:type", String)
], Location.prototype, "landmark", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: [String],
        default: []
    }),
    __metadata("design:type", Array)
], Location.prototype, "alternateNames", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 50,
        enum: [
            'residential',
            'commercial',
            'mixed',
            'industrial',
            'it_hub',
            'business_district',
            'suburb',
            'downtown',
            'waterfront',
            'hillside'
        ]
    }),
    __metadata("design:type", String)
], Location.prototype, "locationType", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 50,
        enum: [
            'prime',
            'premium',
            'mid_range',
            'budget',
            'luxury',
            'affordable',
            'upcoming',
            'established'
        ]
    }),
    __metadata("design:type", String)
], Location.prototype, "locationCategory", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: Object,
        default: {}
    }),
    __metadata("design:type", Object)
], Location.prototype, "connectivity", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: Object,
        default: {}
    }),
    __metadata("design:type", Object)
], Location.prototype, "realEstateData", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: Object,
        default: {}
    }),
    __metadata("design:type", Object)
], Location.prototype, "nearbyFacilities", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: Object,
        default: {}
    }),
    __metadata("design:type", Object)
], Location.prototype, "demographics", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: Object,
        default: {}
    }),
    __metadata("design:type", Object)
], Location.prototype, "safetyData", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: Object,
        default: {}
    }),
    __metadata("design:type", Object)
], Location.prototype, "investmentData", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 200
    }),
    __metadata("design:type", String)
], Location.prototype, "seoTitle", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 500
    }),
    __metadata("design:type", String)
], Location.prototype, "seoDescription", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: [String],
        default: []
    }),
    __metadata("design:type", Array)
], Location.prototype, "seoKeywords", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true
    }),
    __metadata("design:type", String)
], Location.prototype, "featuredImage", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: [String],
        default: []
    }),
    __metadata("design:type", Array)
], Location.prototype, "gallery", void 0);
exports.Location = Location = __decorate([
    (0, mongoose_1.Schema)({
        timestamps: true,
        collection: 'masters',
        discriminatorKey: 'masterType'
    })
], Location);
function arrayLimit(val) {
    return val.length === 2;
}
exports.LocationSchema = mongoose_1.SchemaFactory.createForClass(Location);
exports.LocationSchema.index({ name: 1, parentId: 1 }, { unique: true });
exports.LocationSchema.index({ locationCode: 1, parentId: 1 }, { unique: true, sparse: true });
exports.LocationSchema.index({ parentId: 1, status: 1 });
exports.LocationSchema.index({ locationType: 1, locationCategory: 1 });
exports.LocationSchema.index({ isPopular: 1, status: 1 });
exports.LocationSchema.index({ coordinates: '2dsphere' });
exports.LocationSchema.index({ pincode: 1 });
exports.LocationSchema.index({ 'realEstateData.averagePropertyPrice': 1 });
exports.LocationSchema.index({ 'investmentData.investmentPotential': 1 });
exports.LocationSchema.index({
    name: 'text',
    description: 'text',
    address: 'text',
    landmark: 'text',
    alternateNames: 'text',
    seoKeywords: 'text'
}, {
    weights: {
        name: 10,
        alternateNames: 8,
        landmark: 6,
        address: 5,
        description: 3,
        seoKeywords: 2
    },
    name: 'location_text_index'
});
exports.LocationSchema.pre('save', function (next) {
    if (this.isNew) {
        this.masterType = master_types_enum_1.MasterType.LOCATION;
        this.parentType = master_types_enum_1.MasterType.CITY;
    }
    next();
});
exports.LocationSchema.virtual('fullName').get(function () {
    return this.populated('parentId') && this.parentId && typeof this.parentId === 'object' && 'name' in this.parentId
        ? `${this.name}, ${this.parentId.name}`
        : this.name;
});
exports.LocationSchema.virtual('geoLocation').get(function () {
    if (this.coordinates && this.coordinates.length === 2) {
        return {
            type: 'Point',
            coordinates: this.coordinates
        };
    }
    return null;
});
exports.LocationSchema.set('toJSON', { virtuals: true });
exports.LocationSchema.set('toObject', { virtuals: true });
//# sourceMappingURL=location.schema.js.map