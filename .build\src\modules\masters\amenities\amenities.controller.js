"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AmenitiesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const amenities_service_1 = require("./amenities.service");
const create_amenity_dto_1 = require("./dto/create-amenity.dto");
const update_amenity_dto_1 = require("./dto/update-amenity.dto");
const query_master_dto_1 = require("../common/dto/query-master.dto");
const master_types_enum_1 = require("../common/enums/master-types.enum");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let AmenitiesController = class AmenitiesController {
    constructor(amenitiesService) {
        this.amenitiesService = amenitiesService;
    }
    async create(createAmenityDto) {
        const amenity = await this.amenitiesService.createAmenity(createAmenityDto);
        return {
            success: true,
            data: amenity,
            message: 'Amenity created successfully'
        };
    }
    async findAll(queryDto) {
        return await this.amenitiesService.findAllAmenities(queryDto);
    }
    async getStatistics() {
        const statistics = await this.amenitiesService.getAmenityStatistics();
        return {
            success: true,
            data: statistics,
            message: 'Amenity statistics retrieved successfully'
        };
    }
    async findPopular(limit) {
        const amenities = await this.amenitiesService.findPopularAmenities(limit);
        return {
            success: true,
            data: amenities,
            message: 'Popular amenities retrieved successfully'
        };
    }
    async findByCategory(category) {
        const amenities = await this.amenitiesService.findAmenitiesByCategory(category);
        return {
            success: true,
            data: amenities,
            message: `${category} amenities retrieved successfully`
        };
    }
    async findByImportance(level) {
        const amenities = await this.amenitiesService.findAmenitiesByImportance(level);
        return {
            success: true,
            data: amenities,
            message: `Level ${level} amenities retrieved successfully`
        };
    }
    async findResidential() {
        const amenities = await this.amenitiesService.findResidentialAmenities();
        return {
            success: true,
            data: amenities,
            message: 'Residential amenities retrieved successfully'
        };
    }
    async findCommercial() {
        const amenities = await this.amenitiesService.findCommercialAmenities();
        return {
            success: true,
            data: amenities,
            message: 'Commercial amenities retrieved successfully'
        };
    }
    async findLuxury() {
        const amenities = await this.amenitiesService.findLuxuryAmenities();
        return {
            success: true,
            data: amenities,
            message: 'Luxury amenities retrieved successfully'
        };
    }
    async findBasic() {
        const amenities = await this.amenitiesService.findBasicAmenities();
        return {
            success: true,
            data: amenities,
            message: 'Basic amenities retrieved successfully'
        };
    }
    async searchByTags(tagsString) {
        const tags = tagsString.split(',').map(tag => tag.trim());
        const amenities = await this.amenitiesService.searchAmenitiesByTags(tags);
        return {
            success: true,
            data: amenities,
            message: 'Amenities found successfully'
        };
    }
    async findOne(id) {
        const amenity = await this.amenitiesService.findAmenityById(id);
        return {
            success: true,
            data: amenity,
            message: 'Amenity retrieved successfully'
        };
    }
    async update(id, updateAmenityDto) {
        const amenity = await this.amenitiesService.updateAmenity(id, updateAmenityDto);
        return {
            success: true,
            data: amenity,
            message: 'Amenity updated successfully'
        };
    }
    async remove(id) {
        await this.amenitiesService.removeAmenity(id);
        return {
            success: true,
            message: 'Amenity deleted successfully'
        };
    }
};
exports.AmenitiesController = AmenitiesController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Create a new amenity',
        description: 'Create a new amenity master data entry with categorization and specifications.'
    }),
    (0, swagger_1.ApiBody)({ type: create_amenity_dto_1.CreateAmenityDto }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Amenity created successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid input data'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CONFLICT,
        description: 'Amenity with same name or code already exists'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Body)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_amenity_dto_1.CreateAmenityDto]),
    __metadata("design:returntype", Promise)
], AmenitiesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get all amenities',
        description: 'Retrieve all amenities with filtering, search, and pagination capabilities.'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Amenities retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid query parameters'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Query)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [query_master_dto_1.QueryMasterWithCategoryDto]),
    __metadata("design:returntype", Promise)
], AmenitiesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('statistics'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get amenity statistics',
        description: 'Retrieve comprehensive statistics about amenities including counts by category and importance.'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Amenity statistics retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AmenitiesController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)('popular'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get popular amenities',
        description: 'Retrieve list of popular amenities based on popularity score.'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Number of popular amenities to retrieve',
        example: 10
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Popular amenities retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], AmenitiesController.prototype, "findPopular", null);
__decorate([
    (0, common_1.Get)('category/:category'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get amenities by category',
        description: 'Retrieve all amenities belonging to a specific category.'
    }),
    (0, swagger_1.ApiParam)({
        name: 'category',
        description: 'Amenity category',
        enum: master_types_enum_1.AmenityCategory,
        example: master_types_enum_1.AmenityCategory.RECREATIONAL
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Amenities retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Param)('category')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AmenitiesController.prototype, "findByCategory", null);
__decorate([
    (0, common_1.Get)('importance/:level'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get amenities by importance level',
        description: 'Retrieve all amenities with a specific importance level (1-5).'
    }),
    (0, swagger_1.ApiParam)({
        name: 'level',
        description: 'Importance level (1-5)',
        example: 4
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Amenities retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Param)('level')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], AmenitiesController.prototype, "findByImportance", null);
__decorate([
    (0, common_1.Get)('residential'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get residential amenities',
        description: 'Retrieve amenities suitable for residential projects.'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Residential amenities retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AmenitiesController.prototype, "findResidential", null);
__decorate([
    (0, common_1.Get)('commercial'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get commercial amenities',
        description: 'Retrieve amenities suitable for commercial projects.'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Commercial amenities retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AmenitiesController.prototype, "findCommercial", null);
__decorate([
    (0, common_1.Get)('luxury'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get luxury amenities',
        description: 'Retrieve premium/luxury amenities.'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Luxury amenities retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AmenitiesController.prototype, "findLuxury", null);
__decorate([
    (0, common_1.Get)('basic'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get basic amenities',
        description: 'Retrieve basic/essential amenities.'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Basic amenities retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], AmenitiesController.prototype, "findBasic", null);
__decorate([
    (0, common_1.Get)('search/tags'),
    (0, swagger_1.ApiOperation)({
        summary: 'Search amenities by tags',
        description: 'Search amenities using tags.'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'tags',
        required: true,
        type: String,
        description: 'Comma-separated tags',
        example: 'water,sports,fitness'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Amenities found successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Query)('tags')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AmenitiesController.prototype, "searchByTags", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get amenity by ID',
        description: 'Retrieve a specific amenity by its unique identifier.'
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Amenity ID',
        example: '507f1f77bcf86cd799439013'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Amenity retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Amenity not found'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid amenity ID'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AmenitiesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Update amenity by ID',
        description: 'Update a specific amenity with new information.'
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Amenity ID',
        example: '507f1f77bcf86cd799439013'
    }),
    (0, swagger_1.ApiBody)({ type: update_amenity_dto_1.UpdateAmenityDto }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Amenity updated successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Amenity not found'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid input data or amenity ID'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CONFLICT,
        description: 'Amenity with same name or code already exists'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_amenity_dto_1.UpdateAmenityDto]),
    __metadata("design:returntype", Promise)
], AmenitiesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Delete amenity by ID',
        description: 'Soft delete a specific amenity (marks as archived).'
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Amenity ID',
        example: '507f1f77bcf86cd799439013'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Amenity deleted successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Amenity not found'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Cannot delete amenity as it is being used'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AmenitiesController.prototype, "remove", null);
exports.AmenitiesController = AmenitiesController = __decorate([
    (0, swagger_1.ApiTags)('Masters - Amenities'),
    (0, common_1.Controller)('masters/amenities'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [amenities_service_1.AmenitiesService])
], AmenitiesController);
//# sourceMappingURL=amenities.controller.js.map