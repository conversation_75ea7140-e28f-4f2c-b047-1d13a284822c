"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResponseUtil = void 0;
class ResponseUtil {
    static success(data, message = 'Operation successful') {
        return {
            success: true,
            message,
            data,
            timestamp: new Date().toISOString(),
        };
    }
    static successMessage(message = 'Operation successful') {
        return {
            success: true,
            message,
            timestamp: new Date().toISOString(),
        };
    }
    static error(message, error, statusCode = 500, path) {
        return {
            success: false,
            message,
            error: error || 'Internal Server Error',
            statusCode,
            timestamp: new Date().toISOString(),
            path,
        };
    }
    static paginated(data, page, limit, total, message = 'Data retrieved successfully') {
        const totalPages = Math.ceil(total / limit);
        const hasNext = page < totalPages;
        const hasPrev = page > 1;
        return {
            success: true,
            message,
            data,
            pagination: {
                page,
                limit,
                total,
                totalPages,
                hasNext,
                hasPrev,
            },
            timestamp: new Date().toISOString(),
        };
    }
    static created(data, message = 'Resource created successfully') {
        return {
            success: true,
            message,
            data,
            timestamp: new Date().toISOString(),
        };
    }
    static notFound(message = 'Resource not found', path) {
        return this.error(message, 'Not Found', 404, path);
    }
    static badRequest(message = 'Bad request', path) {
        return this.error(message, 'Bad Request', 400, path);
    }
    static unauthorized(message = 'Unauthorized', path) {
        return this.error(message, 'Unauthorized', 401, path);
    }
    static forbidden(message = 'Forbidden', path) {
        return this.error(message, 'Forbidden', 403, path);
    }
}
exports.ResponseUtil = ResponseUtil;
//# sourceMappingURL=response.util.js.map