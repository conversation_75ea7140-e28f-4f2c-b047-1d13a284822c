import { ApiResponse, PaginatedResponse, ErrorResponse } from '../interfaces/api-response.interface';
export declare class ResponseUtil {
    static success<T>(data: T, message?: string): ApiResponse<T>;
    static successMessage(message?: string): ApiResponse;
    static error(message: string, error?: string, statusCode?: number, path?: string): ErrorResponse;
    static paginated<T>(data: T[], page: number, limit: number, total: number, message?: string): PaginatedResponse<T>;
    static created<T>(data: T, message?: string): ApiResponse<T>;
    static notFound(message?: string, path?: string): ErrorResponse;
    static badRequest(message?: string, path?: string): ErrorResponse;
    static unauthorized(message?: string, path?: string): ErrorResponse;
    static forbidden(message?: string, path?: string): ErrorResponse;
}
