"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WashroomsModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const washrooms_controller_1 = require("./washrooms.controller");
const washrooms_service_1 = require("./washrooms.service");
const washroom_schema_1 = require("./schemas/washroom.schema");
let WashroomsModule = class WashroomsModule {
};
exports.WashroomsModule = WashroomsModule;
exports.WashroomsModule = WashroomsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forFeature([
                { name: washroom_schema_1.Washroom.name, schema: washroom_schema_1.WashroomSchema }
            ])
        ],
        controllers: [washrooms_controller_1.WashroomsController],
        providers: [washrooms_service_1.WashroomsService],
        exports: [washrooms_service_1.WashroomsService]
    })
], WashroomsModule);
//# sourceMappingURL=washrooms.module.js.map