import { MasterWithCategoryDto } from '../../common/dto/base-master.dto';
import { MasterType, AmenityCategory } from '../../common/enums/master-types.enum';
export declare class SpecificationsDto {
    size?: string;
    capacity?: number;
    operatingHours?: string;
    maintenanceFee?: number;
    features?: string[];
}
export declare class AvailabilityDto {
    residential?: boolean;
    commercial?: boolean;
    luxury?: boolean;
    basic?: boolean;
}
export declare class CreateAmenityDto extends MasterWithCategoryDto {
    name: string;
    description?: string;
    code?: string;
    category: AmenityCategory;
    icon?: string;
    color?: string;
    tags?: string[];
    specifications?: SpecificationsDto;
    availability?: AvailabilityDto;
    importanceLevel?: number;
    popularityScore?: number;
    relatedAmenities?: string[];
    keywords?: string[];
    seoTitle?: string;
    seoDescription?: string;
    featuredImage?: string;
    gallery?: string[];
    masterType: MasterType.AMENITY;
}
