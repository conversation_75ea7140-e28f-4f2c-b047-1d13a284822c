import { QueryMasterWithLocationDto } from '../../common/dto/query-master.dto';
export declare class QueryCityDto extends QueryMasterWithLocationDto {
    stateCode?: string;
    countryCode?: string;
    minPopulation?: number;
    maxPopulation?: number;
    minArea?: number;
    maxArea?: number;
    majorLanguage?: string;
    district?: string;
    division?: string;
    minPropertyPrice?: number;
    maxPropertyPrice?: number;
    minAppreciationRate?: number;
    maxAppreciationRate?: number;
    nearbyAirport?: string;
    highway?: string;
    hasFeaturedImage?: boolean;
    hasGallery?: boolean;
    hasRealEstateData?: boolean;
    hasEconomicData?: boolean;
    hasClimateData?: boolean;
}
