#!/usr/bin/env ts-node
interface TestSuite {
    name: string;
    file: string;
    description: string;
    dependencies?: string[];
}
declare const testSuites: TestSuite[];
declare class TestRunner {
    private results;
    runAllTests(): Promise<void>;
    private setupTestEnvironment;
    private runTestSuite;
    runSingleTest(testName: string): Promise<void>;
    listTests(): void;
}
export { TestRunner, testSuites };
