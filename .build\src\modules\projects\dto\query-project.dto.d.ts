import { ProjectStatus, PropertyType, UnitType, ApprovalStatus } from '../enums/project-status.enum';
export declare class QueryProjectDto {
    page?: number;
    limit?: number;
    search?: string;
    projectStatus?: ProjectStatus;
    propertyType?: PropertyType;
    city?: string;
    state?: string;
    builder?: string;
    priceMin?: number;
    priceMax?: number;
    bedroomsMin?: number;
    bedroomsMax?: number;
    unitType?: UnitType;
    isActive?: boolean;
    isFeatured?: boolean;
    approvalStatus?: ApprovalStatus;
    amenities?: string;
    tags?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    latitude?: number;
    longitude?: number;
    radius?: number;
}
