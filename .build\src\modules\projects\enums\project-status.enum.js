"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApprovalStatus = exports.PossessionStatus = exports.FacingDirection = exports.UnitType = exports.PropertyType = exports.ProjectStatus = void 0;
var ProjectStatus;
(function (ProjectStatus) {
    ProjectStatus["PLANNED"] = "planned";
    ProjectStatus["UNDER_CONSTRUCTION"] = "under_construction";
    ProjectStatus["READY_TO_MOVE"] = "ready_to_move";
    ProjectStatus["COMPLETED"] = "completed";
    ProjectStatus["ON_HOLD"] = "on_hold";
    ProjectStatus["CANCELLED"] = "cancelled";
})(ProjectStatus || (exports.ProjectStatus = ProjectStatus = {}));
var PropertyType;
(function (PropertyType) {
    PropertyType["RESIDENTIAL"] = "residential";
    PropertyType["COMMERCIAL"] = "commercial";
    PropertyType["MIXED_USE"] = "mixed_use";
    PropertyType["INDUSTRIAL"] = "industrial";
    PropertyType["AGRICULTURAL"] = "agricultural";
})(PropertyType || (exports.PropertyType = PropertyType = {}));
var UnitType;
(function (UnitType) {
    UnitType["APARTMENT"] = "apartment";
    UnitType["VILLA"] = "villa";
    UnitType["PLOT"] = "plot";
    UnitType["OFFICE"] = "office";
    UnitType["SHOP"] = "shop";
    UnitType["WAREHOUSE"] = "warehouse";
    UnitType["STUDIO"] = "studio";
    UnitType["PENTHOUSE"] = "penthouse";
})(UnitType || (exports.UnitType = UnitType = {}));
var FacingDirection;
(function (FacingDirection) {
    FacingDirection["NORTH"] = "north";
    FacingDirection["SOUTH"] = "south";
    FacingDirection["EAST"] = "east";
    FacingDirection["WEST"] = "west";
    FacingDirection["NORTH_EAST"] = "north_east";
    FacingDirection["NORTH_WEST"] = "north_west";
    FacingDirection["SOUTH_EAST"] = "south_east";
    FacingDirection["SOUTH_WEST"] = "south_west";
})(FacingDirection || (exports.FacingDirection = FacingDirection = {}));
var PossessionStatus;
(function (PossessionStatus) {
    PossessionStatus["IMMEDIATE"] = "immediate";
    PossessionStatus["UNDER_CONSTRUCTION"] = "under_construction";
    PossessionStatus["READY_BY_DATE"] = "ready_by_date";
})(PossessionStatus || (exports.PossessionStatus = PossessionStatus = {}));
var ApprovalStatus;
(function (ApprovalStatus) {
    ApprovalStatus["APPROVED"] = "approved";
    ApprovalStatus["PENDING"] = "pending";
    ApprovalStatus["REJECTED"] = "rejected";
    ApprovalStatus["NOT_APPLICABLE"] = "not_applicable";
})(ApprovalStatus || (exports.ApprovalStatus = ApprovalStatus = {}));
//# sourceMappingURL=project-status.enum.js.map