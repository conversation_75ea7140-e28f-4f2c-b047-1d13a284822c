{"name": "trelax-core-admin-backend", "version": "1.0.0", "description": "TrelaX Core Admin Backend - NestJS Monolith", "author": "TrelaX Team", "private": true, "license": "MIT", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "NEST_DEBUG=true nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:e2e:watch": "jest --config ./test/jest-e2e.json --watch", "test:e2e:coverage": "jest --config ./test/jest-e2e.json --coverage", "test:e2e:runner": "ts-node test/run-tests.ts", "test:e2e:list": "ts-node test/run-tests.ts --list", "test:e2e:single": "ts-node test/run-tests.ts --test", "docker:build": "docker build -t trelax-admin-backend .", "docker:build-dev": "docker build -t trelax-admin-backend:dev --target development .", "docker:start-prod": "docker-compose -f docker-compose.yml up -d --build", "docker:start-dev": "docker-compose -f docker-compose.dev.yml up -d --build", "docker:stop": "docker-compose -f docker-compose.yml down && docker-compose -f docker-compose.dev.yml down", "docker:test": "docker-compose -f docker-compose.test.yml --profile testing up --build --abort-on-container-exit", "docker:logs": "docker-compose -f docker-compose.yml logs -f", "docker:logs-dev": "docker-compose -f docker-compose.dev.yml logs -f", "build:lambda": "nest build && tsc src/lambda.ts --outDir dist --target es2020 --module commonjs --esModuleInterop --allowSyntheticDefaultImports --experimentalDecorators --emitDecoratorMetadata --skipLibCheck", "sls:offline": "serverless offline", "sls:deploy": "serverless deploy", "sls:remove": "serverless remove", "sls:logs": "serverless logs -f main", "sls:info": "serverless info", "sls:offline:start": "serverless offline --config serverless-offline.yml", "sls:deploy:dev": "serverless deploy --stage dev --config serverless.dev.yml", "sls:deploy:prod": "serverless deploy --stage prod --config serverless.prod.yml", "sls:remove:dev": "serverless remove --stage dev --config serverless.dev.yml", "sls:remove:prod": "serverless remove --stage prod --config serverless.prod.yml", "sls:package": "serverless package", "sls:package:dev": "serverless package --stage dev --config serverless.dev.yml", "sls:package:prod": "serverless package --stage prod --config serverless.prod.yml", "dev": "nest start --watch", "seed": "ts-node -r tsconfig-paths/register src/database/seeders/seed.command.ts", "seed:prod": "node dist/database/seeders/seed.command.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.842.0", "@nestjs/common": "^10.4.19", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.4.19", "@nestjs/jwt": "^10.2.0", "@nestjs/mongoose": "^10.1.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.19", "@nestjs/swagger": "^7.4.2", "aws-sdk": "^2.1692.0", "bcryptjs": "^2.4.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "jsonwebtoken": "^9.0.2", "mongodb": "^6.17.0", "mongoose": "^8.16.1", "multer": "^1.4.5-lts.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "reflect-metadata": "^0.1.14", "rxjs": "^7.8.2", "swagger-ui-express": "^5.0.0", "@vendia/serverless-express": "^4.12.6"}, "devDependencies": {"@faker-js/faker": "^9.9.0", "@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/aws-lambda": "^8.10.145", "@types/aws-sdk": "^2.7.4", "@types/bcryptjs": "^2.4.6", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20.3.1", "@types/passport-jwt": "^3.0.13", "@types/passport-local": "^1.0.38", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "serverless": "^3.38.0", "serverless-offline": "^13.3.2", "serverless-plugin-typescript": "^2.1.5", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.1.2", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "main": ".eslintrc.js", "directories": {"test": "test"}, "keywords": []}