#!/bin/bash

# LocalStack S3 Initialization Script
# This script creates the necessary S3 buckets and folders for the TrelaX Admin Backend

echo "🚀 Initializing LocalStack S3 buckets..."

# Wait for LocalStack to be ready
echo "⏳ Waiting for LocalStack to be ready..."
until curl -s http://localhost:4566/health | grep -q '"s3": "available"'; do
  echo "Waiting for S3 service..."
  sleep 2
done

echo "✅ LocalStack S3 service is ready!"

# Create main S3 bucket
echo "📦 Creating S3 bucket: trelax-admin-uploads"
awslocal s3 mb s3://trelax-admin-uploads

# Create folder structure
echo "📁 Creating folder structure..."

# Project-related folders
awslocal s3api put-object --bucket trelax-admin-uploads --key projects/
awslocal s3api put-object --bucket trelax-admin-uploads --key projects/images/
awslocal s3api put-object --bucket trelax-admin-uploads --key projects/videos/
awslocal s3api put-object --bucket trelax-admin-uploads --key projects/brochures/
awslocal s3api put-object --bucket trelax-admin-uploads --key projects/floor-plans/
awslocal s3api put-object --bucket trelax-admin-uploads --key projects/documents/

# Builder-related folders
awslocal s3api put-object --bucket trelax-admin-uploads --key builders/
awslocal s3api put-object --bucket trelax-admin-uploads --key builders/logos/
awslocal s3api put-object --bucket trelax-admin-uploads --key builders/documents/

# Agent-related folders
awslocal s3api put-object --bucket trelax-admin-uploads --key agents/
awslocal s3api put-object --bucket trelax-admin-uploads --key agents/photos/
awslocal s3api put-object --bucket trelax-admin-uploads --key agents/documents/

# Masters-related folders
awslocal s3api put-object --bucket trelax-admin-uploads --key masters/
awslocal s3api put-object --bucket trelax-admin-uploads --key masters/documents/
awslocal s3api put-object --bucket trelax-admin-uploads --key masters/images/

# General folders
awslocal s3api put-object --bucket trelax-admin-uploads --key temp/
awslocal s3api put-object --bucket trelax-admin-uploads --key uploads/

# Set bucket policy for public read access (for development)
echo "🔐 Setting bucket policy..."
awslocal s3api put-bucket-policy --bucket trelax-admin-uploads --policy '{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "PublicReadGetObject",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "s3:GetObject",
      "Resource": "arn:aws:s3:::trelax-admin-uploads/*"
    }
  ]
}'

# Enable CORS for the bucket
echo "🌐 Enabling CORS..."
awslocal s3api put-bucket-cors --bucket trelax-admin-uploads --cors-configuration '{
  "CORSRules": [
    {
      "AllowedHeaders": ["*"],
      "AllowedMethods": ["GET", "PUT", "POST", "DELETE", "HEAD"],
      "AllowedOrigins": ["*"],
      "ExposeHeaders": ["ETag"],
      "MaxAgeSeconds": 3000
    }
  ]
}'

# List created buckets and folders
echo "📋 Listing created buckets and folders:"
awslocal s3 ls
echo ""
echo "📂 Folder structure in trelax-admin-uploads:"
awslocal s3 ls s3://trelax-admin-uploads --recursive

echo "🎉 LocalStack S3 initialization completed successfully!"
echo "📍 S3 endpoint: http://localhost:4566"
echo "🪣 Bucket name: trelax-admin-uploads"
echo "🔑 Access Key: test"
echo "🔐 Secret Key: test"
