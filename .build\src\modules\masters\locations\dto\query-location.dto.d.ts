import { QueryMasterWithParentDto } from '../../common/dto/query-master.dto';
export declare class QueryLocationDto extends QueryMasterWithParentDto {
    cityId?: string;
    locationType?: string;
    locationCategory?: string;
    pincode?: string;
    minPropertyPrice?: number;
    maxPropertyPrice?: number;
    minAppreciationRate?: number;
    maxAppreciationRate?: number;
    minRentalYield?: number;
    maxRentalYield?: number;
    nearbyMetroStation?: string;
    maxMetroDistance?: number;
    nearbyRailwayStation?: string;
    maxRailwayDistance?: number;
    maxAirportDistance?: number;
    propertyType?: string;
    hasConnectivity?: boolean;
    hasRealEstateData?: boolean;
    hasNearbyFacilities?: boolean;
    hasFeaturedImage?: boolean;
    hasGallery?: boolean;
}
