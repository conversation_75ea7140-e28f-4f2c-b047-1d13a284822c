# Production stage configuration
# Usage: serverless deploy --stage prod --config serverless.prod.yml

service: trelax-admin-backend

frameworkVersion: '3'

provider:
  name: aws
  runtime: nodejs18.x
  region: ap-south-1
  stage: prod
  memorySize: 1024
  timeout: 30
  logRetentionInDays: 30
  
  environment:
    NODE_ENV: production
    STAGE: prod
    REGION: ap-south-1
    # Production database
    MONGO_URI: ${env:MONGO_URI_PROD}
    # Production JWT
    JWT_SECRET: ${env:JWT_SECRET_PROD}
    JWT_EXPIRES_IN: 7d
    # Production S3
    AWS_S3_BUCKET: trelax-admin-uploads-prod
    # Production CORS
    CORS_ORIGIN: ${env:CORS_ORIGIN_PROD, 'https://admin.trelax.com'}
    # Production logging
    LOG_LEVEL: warn

  iam:
    role:
      statements:
        - Effect: Allow
          Action:
            - s3:GetObject
            - s3:PutObject
            - s3:DeleteObject
            - s3:ListBucket
          Resource:
            - arn:aws:s3:::trelax-admin-uploads-prod
            - arn:aws:s3:::trelax-admin-uploads-prod/*
        - Effect: Allow
          Action:
            - logs:CreateLogGroup
            - logs:CreateLogStream
            - logs:PutLogEvents
          Resource: arn:aws:logs:ap-south-1:*:*

  # Production-specific API Gateway settings
  apiGateway:
    minimumCompressionSize: 1024
    binaryMediaTypes:
      - 'multipart/form-data'
      - 'image/*'
      - 'application/pdf'
      - 'application/octet-stream'

plugins:
  - serverless-plugin-typescript

functions:
  main:
    handler: src/lambda.handler
    name: trelax-admin-backend-prod-main
    memorySize: 1024
    timeout: 30
    reservedConcurrency: 50
    events:
      - http:
          path: /{proxy+}
          method: ANY
          cors:
            origin: ${env:CORS_ORIGIN_PROD, 'https://admin.trelax.com'}
            headers:
              - Content-Type
              - Authorization
              - Accept
            allowCredentials: true
      - http:
          path: /
          method: ANY
          cors:
            origin: ${env:CORS_ORIGIN_PROD, 'https://admin.trelax.com'}
            headers:
              - Content-Type
              - Authorization
              - Accept
            allowCredentials: true

  health:
    handler: src/lambda.healthCheck
    name: trelax-admin-backend-prod-health
    memorySize: 128
    timeout: 10
    events:
      - http:
          path: /health
          method: GET
          cors:
            origin: '*'

  warmup:
    handler: src/lambda.warmup
    name: trelax-admin-backend-prod-warmup
    memorySize: 128
    timeout: 10
    events:
      - schedule:
          rate: rate(5 minutes)
          enabled: true

resources:
  Resources:
    S3BucketUploads:
      Type: AWS::S3::Bucket
      Properties:
        BucketName: trelax-admin-uploads-prod
        PublicAccessBlockConfiguration:
          BlockPublicAcls: true
          BlockPublicPolicy: true
          IgnorePublicAcls: true
          RestrictPublicBuckets: true
        CorsConfiguration:
          CorsRules:
            - AllowedHeaders: ['Content-Type', 'Authorization']
              AllowedMethods: [GET, PUT, POST, DELETE]
              AllowedOrigins: [${env:CORS_ORIGIN_PROD, 'https://admin.trelax.com'}]
              ExposedHeaders: [ETag]
              MaxAge: 3000
        VersioningConfiguration:
          Status: Enabled
        LifecycleConfiguration:
          Rules:
            - Id: DeleteIncompleteMultipartUploads
              Status: Enabled
              AbortIncompleteMultipartUpload:
                DaysAfterInitiation: 1

    S3BucketUploadsPolicy:
      Type: AWS::S3::BucketPolicy
      Properties:
        Bucket: !Ref S3BucketUploads
        PolicyDocument:
          Statement:
            - Sid: DenyInsecureConnections
              Effect: Deny
              Principal: '*'
              Action: 's3:*'
              Resource:
                - !Sub '${S3BucketUploads}/*'
                - !Ref S3BucketUploads
              Condition:
                Bool:
                  'aws:SecureTransport': 'false'

    # CloudWatch Alarms for monitoring
    LambdaErrorAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: ${self:service}-${self:provider.stage}-lambda-errors
        AlarmDescription: Lambda function errors
        MetricName: Errors
        Namespace: AWS/Lambda
        Statistic: Sum
        Period: 300
        EvaluationPeriods: 2
        Threshold: 5
        ComparisonOperator: GreaterThanThreshold
        Dimensions:
          - Name: FunctionName
            Value: !Ref MainLambdaFunction

    LambdaDurationAlarm:
      Type: AWS::CloudWatch::Alarm
      Properties:
        AlarmName: ${self:service}-${self:provider.stage}-lambda-duration
        AlarmDescription: Lambda function duration
        MetricName: Duration
        Namespace: AWS/Lambda
        Statistic: Average
        Period: 300
        EvaluationPeriods: 2
        Threshold: 25000
        ComparisonOperator: GreaterThanThreshold
        Dimensions:
          - Name: FunctionName
            Value: !Ref MainLambdaFunction
