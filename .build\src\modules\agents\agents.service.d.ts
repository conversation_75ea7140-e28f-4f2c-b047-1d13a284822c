import { Model } from 'mongoose';
import { Agent, AgentDocument } from './schemas/agent.schema';
import { CreateAgentDto } from './dto/create-agent.dto';
import { UpdateAgentDto } from './dto/update-agent.dto';
export declare class AgentsService {
    private agentModel;
    constructor(agentModel: Model<AgentDocument>);
    create(createAgentDto: CreateAgentDto): Promise<Agent>;
    findAll(): Promise<Agent[]>;
    findById(id: string): Promise<Agent>;
    update(id: string, updateAgentDto: UpdateAgentDto): Promise<Agent>;
    remove(id: string): Promise<void>;
}
