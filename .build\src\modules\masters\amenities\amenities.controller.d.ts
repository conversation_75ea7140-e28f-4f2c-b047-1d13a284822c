import { AmenitiesService } from './amenities.service';
import { CreateAmenityDto } from './dto/create-amenity.dto';
import { UpdateAmenityDto } from './dto/update-amenity.dto';
import { QueryMasterWithCategoryDto } from '../common/dto/query-master.dto';
import { AmenityCategory } from '../common/enums/master-types.enum';
export declare class AmenitiesController {
    private readonly amenitiesService;
    constructor(amenitiesService: AmenitiesService);
    create(createAmenityDto: CreateAmenityDto): Promise<{
        success: boolean;
        data: import("./schemas/amenity.schema").Amenity;
        message: string;
    }>;
    findAll(queryDto: QueryMasterWithCategoryDto): Promise<import("../common/interfaces/base-master.interface").MasterListResponse<import("./schemas/amenity.schema").Amenity>>;
    getStatistics(): Promise<{
        success: boolean;
        data: {
            averagePopularityScore: any;
            byCategory: any;
            byImportanceLevel: any;
            availability: any;
            topPopularAmenities: (import("mongoose").Document<unknown, {}, import("./schemas/amenity.schema").AmenityDocument, {}> & import("./schemas/amenity.schema").Amenity & import("mongoose").Document<unknown, any, any, Record<string, any>> & Required<{
                _id: unknown;
            }> & {
                __v: number;
            })[];
            totalCount: number;
            activeCount: number;
            inactiveCount: number;
            popularCount: number;
            defaultCount: number;
            byStatus?: Record<string, number>;
        };
        message: string;
    }>;
    findPopular(limit?: number): Promise<{
        success: boolean;
        data: import("./schemas/amenity.schema").Amenity[];
        message: string;
    }>;
    findByCategory(category: AmenityCategory): Promise<{
        success: boolean;
        data: import("./schemas/amenity.schema").Amenity[];
        message: string;
    }>;
    findByImportance(level: number): Promise<{
        success: boolean;
        data: import("./schemas/amenity.schema").Amenity[];
        message: string;
    }>;
    findResidential(): Promise<{
        success: boolean;
        data: import("./schemas/amenity.schema").Amenity[];
        message: string;
    }>;
    findCommercial(): Promise<{
        success: boolean;
        data: import("./schemas/amenity.schema").Amenity[];
        message: string;
    }>;
    findLuxury(): Promise<{
        success: boolean;
        data: import("./schemas/amenity.schema").Amenity[];
        message: string;
    }>;
    findBasic(): Promise<{
        success: boolean;
        data: import("./schemas/amenity.schema").Amenity[];
        message: string;
    }>;
    searchByTags(tagsString: string): Promise<{
        success: boolean;
        data: import("./schemas/amenity.schema").Amenity[];
        message: string;
    }>;
    findOne(id: string): Promise<{
        success: boolean;
        data: import("./schemas/amenity.schema").Amenity;
        message: string;
    }>;
    update(id: string, updateAmenityDto: UpdateAmenityDto): Promise<{
        success: boolean;
        data: import("./schemas/amenity.schema").Amenity;
        message: string;
    }>;
    remove(id: string): Promise<{
        success: boolean;
        message: string;
    }>;
}
