# Serverless Offline Configuration for Local Testing
# This file is used specifically for local development and testing

service: trelax-admin-backend-offline

frameworkVersion: '3'

provider:
  name: aws
  runtime: nodejs18.x
  region: ap-south-1
  stage: offline
  
  environment:
    NODE_ENV: development
    STAGE: offline
    REGION: ap-south-1
    # Local database
    MONGO_URI: mongodb://localhost:27017/trelax_offline_db
    # Local JWT
    JWT_SECRET: offline_jwt_secret_key_2024
    JWT_EXPIRES_IN: 24h
    # Local S3 (using LocalStack)
    AWS_S3_BUCKET: trelax-admin-uploads-offline
    AWS_ENDPOINT_URL: http://localhost:4566
    AWS_ACCESS_KEY_ID: test
    AWS_SECRET_ACCESS_KEY: test
    # Local CORS
    CORS_ORIGIN: 'http://localhost:3000,http://localhost:3001'
    # Debug settings
    DEBUG: 'true'
    LOG_LEVEL: debug
    ENABLE_SWAGGER: 'true'

plugins:
  - serverless-plugin-typescript
  - serverless-offline

custom:
  serverless-offline:
    httpPort: 3000
    host: 0.0.0.0
    stage: offline
    prefix: api/v1
    printOutput: true
    noAuth: true
    corsAllowOrigin: '*'
    corsAllowHeaders: 'accept,content-type,x-api-key,authorization,x-amz-date,x-amz-security-token,x-amz-user-agent'
    corsExposedHeaders: 'WWW-Authenticate,Server-Authorization'
    ignoreJWTSignature: true
    noTimeout: true
    resourceRoutes: true
    
  serverless-plugin-typescript:
    tsConfigFileLocation: './tsconfig.build.json'

functions:
  main:
    handler: src/lambda.handler
    name: trelax-admin-backend-offline-main
    memorySize: 512
    timeout: 30
    events:
      - http:
          path: /{proxy+}
          method: ANY
          cors: true
      - http:
          path: /
          method: ANY
          cors: true

  health:
    handler: src/lambda.healthCheck
    name: trelax-admin-backend-offline-health
    memorySize: 128
    timeout: 10
    events:
      - http:
          path: /health
          method: GET
          cors: true

# No resources needed for offline testing
