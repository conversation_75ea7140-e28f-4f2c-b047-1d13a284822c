"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CitiesService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const city_schema_1 = require("./schemas/city.schema");
const base_master_service_1 = require("../common/services/base-master.service");
const master_types_enum_1 = require("../common/enums/master-types.enum");
let CitiesService = class CitiesService extends base_master_service_1.BaseMasterService {
    constructor(cityModel) {
        super(cityModel, master_types_enum_1.MasterType.CITY);
        this.cityModel = cityModel;
    }
    async createCity(createCityDto) {
        const cityData = {
            ...createCityDto,
            masterType: master_types_enum_1.MasterType.CITY,
            country: createCityDto.country || 'India',
            status: 'active'
        };
        return await this.create(cityData);
    }
    async findAllCities(queryDto = {}) {
        try {
            const { page = 1, limit = 10, search, status, isDefault, isPopular, sortBy = 'sortOrder', sortOrder = 'asc', state, country, pinCode, stateCode, countryCode, minPopulation, maxPopulation, minArea, maxArea, majorLanguage, district, division, minPropertyPrice, maxPropertyPrice, minAppreciationRate, maxAppreciationRate, nearbyAirport, highway, hasFeaturedImage, hasGallery, hasRealEstateData, hasEconomicData, hasClimateData } = queryDto;
            const filter = {
                masterType: master_types_enum_1.MasterType.CITY,
                status: { $ne: 'archived' }
            };
            if (search) {
                filter.$or = [
                    { name: { $regex: search, $options: 'i' } },
                    { description: { $regex: search, $options: 'i' } },
                    { code: { $regex: search, $options: 'i' } },
                    { state: { $regex: search, $options: 'i' } },
                    { alternateNames: { $regex: search, $options: 'i' } }
                ];
            }
            if (status)
                filter.status = status;
            if (typeof isDefault === 'boolean')
                filter.isDefault = isDefault;
            if (typeof isPopular === 'boolean')
                filter.isPopular = isPopular;
            if (state)
                filter.state = { $regex: state, $options: 'i' };
            if (country)
                filter.country = { $regex: country, $options: 'i' };
            if (stateCode)
                filter.stateCode = stateCode;
            if (countryCode)
                filter.countryCode = countryCode;
            if (district)
                filter.district = { $regex: district, $options: 'i' };
            if (division)
                filter.division = { $regex: division, $options: 'i' };
            if (majorLanguage)
                filter.majorLanguage = { $regex: majorLanguage, $options: 'i' };
            if (pinCode) {
                filter.pinCodes = { $in: [pinCode] };
            }
            if (minPopulation !== undefined || maxPopulation !== undefined) {
                filter.population = {};
                if (minPopulation !== undefined)
                    filter.population.$gte = minPopulation;
                if (maxPopulation !== undefined)
                    filter.population.$lte = maxPopulation;
            }
            if (minArea !== undefined || maxArea !== undefined) {
                filter.area = {};
                if (minArea !== undefined)
                    filter.area.$gte = minArea;
                if (maxArea !== undefined)
                    filter.area.$lte = maxArea;
            }
            if (minPropertyPrice !== undefined || maxPropertyPrice !== undefined) {
                filter['realEstateData.averagePropertyPrice'] = {};
                if (minPropertyPrice !== undefined) {
                    filter['realEstateData.averagePropertyPrice'].$gte = minPropertyPrice;
                }
                if (maxPropertyPrice !== undefined) {
                    filter['realEstateData.averagePropertyPrice'].$lte = maxPropertyPrice;
                }
            }
            if (minAppreciationRate !== undefined || maxAppreciationRate !== undefined) {
                filter['realEstateData.priceAppreciationRate'] = {};
                if (minAppreciationRate !== undefined) {
                    filter['realEstateData.priceAppreciationRate'].$gte = minAppreciationRate;
                }
                if (maxAppreciationRate !== undefined) {
                    filter['realEstateData.priceAppreciationRate'].$lte = maxAppreciationRate;
                }
            }
            if (nearbyAirport) {
                filter.nearbyAirports = { $in: [nearbyAirport] };
            }
            if (highway) {
                filter.highways = { $in: [highway] };
            }
            if (hasFeaturedImage) {
                filter.featuredImage = { $exists: true, $nin: [null, ''] };
            }
            if (hasGallery) {
                filter.gallery = { $exists: true, $not: { $size: 0 } };
            }
            if (hasRealEstateData) {
                filter.realEstateData = { $exists: true, $ne: null };
            }
            if (hasEconomicData) {
                filter.economicData = { $exists: true, $ne: null };
            }
            if (hasClimateData) {
                filter.climateData = { $exists: true, $ne: null };
            }
            const sort = {};
            sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
            const skip = (page - 1) * limit;
            const [data, total] = await Promise.all([
                this.cityModel
                    .find(filter)
                    .sort(sort)
                    .skip(skip)
                    .limit(limit)
                    .exec(),
                this.cityModel.countDocuments(filter)
            ]);
            const totalPages = Math.ceil(total / limit);
            return {
                success: true,
                data: data,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages
                }
            };
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch cities: ${error.message}`);
        }
    }
    async findCityById(id) {
        return await this.findById(id);
    }
    async updateCity(id, updateCityDto) {
        return await this.update(id, updateCityDto);
    }
    async removeCity(id) {
        return await this.remove(id);
    }
    async findCitiesByState(state) {
        try {
            return await this.cityModel
                .find({
                state: { $regex: state, $options: 'i' },
                masterType: master_types_enum_1.MasterType.CITY,
                status: { $ne: 'archived' }
            })
                .sort({ sortOrder: 1, name: 1 })
                .exec();
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch cities by state: ${error.message}`);
        }
    }
    async findCitiesByCountry(country) {
        try {
            return await this.cityModel
                .find({
                country: { $regex: country, $options: 'i' },
                masterType: master_types_enum_1.MasterType.CITY,
                status: { $ne: 'archived' }
            })
                .sort({ sortOrder: 1, name: 1 })
                .exec();
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch cities by country: ${error.message}`);
        }
    }
    async findPopularCities(limit = 10) {
        try {
            return await this.cityModel
                .find({
                isPopular: true,
                masterType: master_types_enum_1.MasterType.CITY,
                status: 'active'
            })
                .sort({ sortOrder: 1, name: 1 })
                .limit(limit)
                .exec();
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch popular cities: ${error.message}`);
        }
    }
    async findCitiesNearLocation(longitude, latitude, maxDistance = 100000) {
        try {
            return await this.cityModel
                .find({
                coordinates: {
                    $near: {
                        $geometry: {
                            type: 'Point',
                            coordinates: [longitude, latitude]
                        },
                        $maxDistance: maxDistance
                    }
                },
                masterType: master_types_enum_1.MasterType.CITY,
                status: { $ne: 'archived' }
            })
                .exec();
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to find cities near location: ${error.message}`);
        }
    }
    async getCityStatistics() {
        try {
            const baseStats = await this.getStatistics();
            const [stateStats, countryStats, popularCitiesCount, citiesWithRealEstateData, averagePropertyPrice] = await Promise.all([
                this.cityModel.aggregate([
                    {
                        $match: {
                            masterType: master_types_enum_1.MasterType.CITY,
                            status: { $ne: 'archived' }
                        }
                    },
                    { $group: { _id: '$state', count: { $sum: 1 } } },
                    { $sort: { count: -1 } }
                ]),
                this.cityModel.aggregate([
                    {
                        $match: {
                            masterType: master_types_enum_1.MasterType.CITY,
                            status: { $ne: 'archived' }
                        }
                    },
                    { $group: { _id: '$country', count: { $sum: 1 } } },
                    { $sort: { count: -1 } }
                ]),
                this.cityModel.countDocuments({
                    masterType: master_types_enum_1.MasterType.CITY,
                    isPopular: true,
                    status: 'active'
                }),
                this.cityModel.countDocuments({
                    masterType: master_types_enum_1.MasterType.CITY,
                    'realEstateData.averagePropertyPrice': { $exists: true, $ne: null },
                    status: { $ne: 'archived' }
                }),
                this.cityModel.aggregate([
                    {
                        $match: {
                            masterType: master_types_enum_1.MasterType.CITY,
                            'realEstateData.averagePropertyPrice': { $exists: true, $ne: null },
                            status: { $ne: 'archived' }
                        }
                    },
                    {
                        $group: {
                            _id: null,
                            avgPrice: { $avg: '$realEstateData.averagePropertyPrice' }
                        }
                    }
                ])
            ]);
            const byState = stateStats.reduce((acc, item) => {
                acc[item._id] = item.count;
                return acc;
            }, {});
            const byCountry = countryStats.reduce((acc, item) => {
                acc[item._id] = item.count;
                return acc;
            }, {});
            return {
                ...baseStats,
                popularCitiesCount,
                citiesWithRealEstateData,
                averagePropertyPrice: averagePropertyPrice[0]?.avgPrice || 0,
                byState,
                byCountry
            };
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to get city statistics: ${error.message}`);
        }
    }
    async checkUsageBeforeDelete(id) {
    }
};
exports.CitiesService = CitiesService;
exports.CitiesService = CitiesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(city_schema_1.City.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], CitiesService);
//# sourceMappingURL=cities.service.js.map