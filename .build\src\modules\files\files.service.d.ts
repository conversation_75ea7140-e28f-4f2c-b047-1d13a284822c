import { Model } from 'mongoose';
import { File, FileDocument } from './schemas/file.schema';
import { UploadFileDto } from './dto/upload-file.dto';
import { QueryFileDto } from './dto/query-file.dto';
import { S3Service } from './services/s3.service';
export declare class FilesService {
    private fileModel;
    private readonly s3Service;
    constructor(fileModel: Model<FileDocument>, s3Service: S3Service);
    uploadFile(file: any, uploadFileDto: UploadFileDto, userId: string, folder?: string): Promise<File>;
    uploadMultipleFiles(files: any[], uploadFileDto: UploadFileDto, userId: string, folder?: string): Promise<File[]>;
    findAll(queryDto: QueryFileDto, userId?: string): Promise<{
        files: (import("mongoose").Document<unknown, {}, FileDocument, {}> & File & import("mongoose").Document<unknown, any, any, Record<string, any>> & Required<{
            _id: unknown;
        }> & {
            __v: number;
        })[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
    }>;
    findById(id: string, userId?: string): Promise<File>;
    getDownloadUrl(id: string, userId?: string): Promise<string>;
    remove(id: string, userId: string): Promise<void>;
    getStatistics(userId?: string): Promise<{
        totalFiles: number;
        activeFiles: number;
        inactiveFiles: number;
        publicFiles: number;
        privateFiles: number;
        totalSize: any;
        categoryDistribution: any;
    }>;
    getFilesByCategory(category: string, limit?: number): Promise<File[]>;
    searchByTags(tags: string[], limit?: number): Promise<File[]>;
}
