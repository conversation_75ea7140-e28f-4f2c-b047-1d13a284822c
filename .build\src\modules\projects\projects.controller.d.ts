import { ProjectsService } from './projects.service';
import { UpdateProjectDto } from './dto/update-project.dto';
import { QueryProjectDto } from './dto/query-project.dto';
import { S3Service } from '../files/services/s3.service';
export declare class ProjectsController {
    private readonly projectsService;
    private readonly s3Service;
    constructor(projectsService: ProjectsService, s3Service: S3Service);
    create(body: any, files: Array<Express.Multer.File>, req: any): Promise<import("../../common/interfaces/api-response.interface").ApiResponse<import("./schemas/project.schema").Project>>;
    findAll(queryDto: QueryProjectDto): Promise<import("../../common/interfaces/api-response.interface").PaginatedResponse<import("mongoose").Document<unknown, {}, import("./schemas/project.schema").ProjectDocument, {}> & import("./schemas/project.schema").Project & import("mongoose").Document<unknown, any, any, Record<string, any>> & Required<{
        _id: unknown;
    }> & {
        __v: number;
    }>>;
    findOne(id: string): Promise<import("../../common/interfaces/api-response.interface").ApiResponse<import("./schemas/project.schema").Project>>;
    update(id: string, updateProjectDto: UpdateProjectDto, req: any): Promise<import("../../common/interfaces/api-response.interface").ApiResponse<import("./schemas/project.schema").Project>>;
    remove(id: string): Promise<import("../../common/interfaces/api-response.interface").ApiResponse<any>>;
    uploadMedia(id: string, type: 'images' | 'videos' | 'brochures' | 'floorPlans' | 'masterPlan' | 'locationMap', files: any[], req: any): Promise<import("../../common/interfaces/api-response.interface").ApiResponse<string[]>>;
    uploadDocuments(id: string, type: 'approvals' | 'legalDocuments' | 'certificates' | 'others', files: any[], req: any): Promise<import("../../common/interfaces/api-response.interface").ApiResponse<string[]>>;
    getStatistics(): Promise<import("../../common/interfaces/api-response.interface").ApiResponse<{
        totalProjects: number;
        activeProjects: number;
        inactiveProjects: number;
        featuredProjects: number;
        statusDistribution: any;
        propertyTypeDistribution: any;
        topCities: any[];
    }>>;
    getFeaturedProjects(limit?: number): Promise<import("../../common/interfaces/api-response.interface").ApiResponse<(import("mongoose").Document<unknown, {}, import("./schemas/project.schema").ProjectDocument, {}> & import("./schemas/project.schema").Project & import("mongoose").Document<unknown, any, any, Record<string, any>> & Required<{
        _id: unknown;
    }> & {
        __v: number;
    })[]>>;
}
