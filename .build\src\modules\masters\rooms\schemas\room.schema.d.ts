import { Document } from 'mongoose';
import { MasterWithNumericValue } from '../../common/schemas/base-master.schema';
import { MasterType } from '../../common/enums/master-types.enum';
export type RoomDocument = Room & Document;
export declare class Room extends MasterWithNumericValue {
    masterType: MasterType.ROOM;
    numericValue?: number;
    unit?: string;
    roomType?: string;
    features?: string[];
    typicalArea?: number;
    popularityRating?: number;
    createdAt?: Date;
    updatedAt?: Date;
}
export declare const RoomSchema: import("mongoose").Schema<Room, import("mongoose").Model<Room, any, any, any, Document<unknown, any, Room, any> & Room & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Room, Document<unknown, {}, import("mongoose").FlatRecord<Room>, {}> & import("mongoose").FlatRecord<Room> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
