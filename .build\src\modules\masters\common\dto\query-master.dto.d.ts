import { MasterStatus } from '../enums/master-types.enum';
export declare class QueryMasterDto {
    page?: number;
    limit?: number;
    search?: string;
    status?: MasterStatus;
    isDefault?: boolean;
    isPopular?: boolean;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}
export declare class QueryMasterWithParentDto extends QueryMasterDto {
    parentId?: string;
}
export declare class QueryMasterWithCategoryDto extends QueryMasterDto {
    category?: string;
}
export declare class QueryMasterWithNumericRangeDto extends QueryMasterDto {
    minValue?: number;
    maxValue?: number;
    unit?: string;
}
export declare class QueryMasterWithLocationDto extends QueryMasterDto {
    state?: string;
    country?: string;
    pinCode?: string;
}
