"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.S3Service = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const AWS = require("aws-sdk");
const uuid_1 = require("uuid");
let S3Service = class S3Service {
    constructor(configService) {
        this.configService = configService;
        AWS.config.update({
            accessKeyId: this.configService.get('AWS_ACCESS_KEY_ID'),
            secretAccessKey: this.configService.get('AWS_SECRET_ACCESS_KEY'),
            region: this.configService.get('AWS_REGION'),
        });
        this.s3 = new AWS.S3();
        this.bucketName = this.configService.get('AWS_S3_BUCKET');
        if (!this.bucketName) {
            throw new Error('AWS_S3_BUCKET is not configured');
        }
    }
    async uploadFile(file, folder = 'uploads', isPublic = false) {
        try {
            const fileExtension = file.originalname.split('.').pop();
            const fileName = `${(0, uuid_1.v4)()}.${fileExtension}`;
            const key = `${folder}/${fileName}`;
            const uploadParams = {
                Bucket: this.bucketName,
                Key: key,
                Body: file.buffer,
                ContentType: file.mimetype,
                ContentDisposition: 'inline',
                Metadata: {
                    originalName: file.originalname,
                    uploadedAt: new Date().toISOString(),
                },
            };
            if (isPublic) {
                uploadParams.ACL = 'public-read';
            }
            const result = await this.s3.upload(uploadParams).promise();
            return {
                key,
                url: result.Location,
                bucket: this.bucketName,
            };
        }
        catch (error) {
            console.error('S3 Upload Error:', error);
            throw new common_1.InternalServerErrorException('Failed to upload file to S3');
        }
    }
    async getPresignedUrl(key, expiresIn = 3600) {
        try {
            const params = {
                Bucket: this.bucketName,
                Key: key,
                Expires: expiresIn,
            };
            return await this.s3.getSignedUrlPromise('getObject', params);
        }
        catch (error) {
            console.error('S3 Presigned URL Error:', error);
            throw new common_1.InternalServerErrorException('Failed to generate download URL');
        }
    }
    async deleteFile(key) {
        try {
            const params = {
                Bucket: this.bucketName,
                Key: key,
            };
            await this.s3.deleteObject(params).promise();
        }
        catch (error) {
            console.error('S3 Delete Error:', error);
            throw new common_1.InternalServerErrorException('Failed to delete file from S3');
        }
    }
    async fileExists(key) {
        try {
            const params = {
                Bucket: this.bucketName,
                Key: key,
            };
            await this.s3.headObject(params).promise();
            return true;
        }
        catch (error) {
            if (error.code === 'NotFound') {
                return false;
            }
            throw new common_1.InternalServerErrorException('Failed to check file existence');
        }
    }
    async getFileMetadata(key) {
        try {
            const params = {
                Bucket: this.bucketName,
                Key: key,
            };
            return await this.s3.headObject(params).promise();
        }
        catch (error) {
            console.error('S3 Metadata Error:', error);
            throw new common_1.InternalServerErrorException('Failed to get file metadata');
        }
    }
    async copyFile(sourceKey, destinationKey) {
        try {
            const params = {
                Bucket: this.bucketName,
                CopySource: `${this.bucketName}/${sourceKey}`,
                Key: destinationKey,
            };
            await this.s3.copyObject(params).promise();
        }
        catch (error) {
            console.error('S3 Copy Error:', error);
            throw new common_1.InternalServerErrorException('Failed to copy file');
        }
    }
    async listFiles(prefix = '', maxKeys = 1000) {
        try {
            const params = {
                Bucket: this.bucketName,
                Prefix: prefix,
                MaxKeys: maxKeys,
            };
            const result = await this.s3.listObjectsV2(params).promise();
            return result.Contents || [];
        }
        catch (error) {
            console.error('S3 List Error:', error);
            throw new common_1.InternalServerErrorException('Failed to list files');
        }
    }
    getBucketName() {
        return this.bucketName;
    }
};
exports.S3Service = S3Service;
exports.S3Service = S3Service = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], S3Service);
//# sourceMappingURL=s3.service.js.map