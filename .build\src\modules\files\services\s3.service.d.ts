import { ConfigService } from '@nestjs/config';
import * as AWS from 'aws-sdk';
export declare class S3Service {
    private configService;
    private s3;
    private bucketName;
    constructor(configService: ConfigService);
    uploadFile(file: Express.Multer.File, folder?: string, isPublic?: boolean): Promise<{
        key: string;
        url: string;
        bucket: string;
    }>;
    getPresignedUrl(key: string, expiresIn?: number): Promise<string>;
    deleteFile(key: string): Promise<void>;
    fileExists(key: string): Promise<boolean>;
    getFileMetadata(key: string): Promise<AWS.S3.HeadObjectOutput>;
    copyFile(sourceKey: string, destinationKey: string): Promise<void>;
    listFiles(prefix?: string, maxKeys?: number): Promise<AWS.S3.Object[]>;
    getBucketName(): string;
}
