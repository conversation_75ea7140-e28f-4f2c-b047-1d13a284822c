import { MasterWithCategoryDto } from '../../common/dto/base-master.dto';
import { MasterType, PropertyTypeCategory } from '../../common/enums/master-types.enum';
export declare class PropertySpecificationsDto {
    minArea?: number;
    maxArea?: number;
    typicalFloors?: number;
    parkingSpaces?: number;
    balconies?: number;
    bathrooms?: number;
    bedrooms?: number;
}
export declare class PriceRangeDto {
    min?: number;
    max?: number;
    currency?: string;
}
export declare class CreatePropertyTypeDto extends MasterWithCategoryDto {
    name: string;
    description?: string;
    code?: string;
    category: PropertyTypeCategory;
    icon?: string;
    color?: string;
    suitableFor?: string[];
    specifications?: PropertySpecificationsDto;
    features?: string[];
    priceRange?: PriceRangeDto;
    popularityRating?: number;
    targetAudience?: string[];
    legalRequirements?: string[];
    seoTitle?: string;
    seoDescription?: string;
    featuredImage?: string;
    gallery?: string[];
    masterType: MasterType.PROPERTY_TYPE;
}
