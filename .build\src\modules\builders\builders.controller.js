"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BuildersController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const builders_service_1 = require("./builders.service");
const create_builder_dto_1 = require("./dto/create-builder.dto");
const update_builder_dto_1 = require("./dto/update-builder.dto");
let BuildersController = class BuildersController {
    constructor(buildersService) {
        this.buildersService = buildersService;
    }
    async create(createBuilderDto) {
        const builder = await this.buildersService.create(createBuilderDto);
        return { success: true, message: 'Builder created successfully', data: builder };
    }
    async findAll() {
        const builders = await this.buildersService.findAll();
        return { success: true, message: 'Builders retrieved successfully', data: builders };
    }
    async findById(id) {
        const builder = await this.buildersService.findById(id);
        return { success: true, message: 'Builder retrieved successfully', data: builder };
    }
    async update(id, updateBuilderDto) {
        const builder = await this.buildersService.update(id, updateBuilderDto);
        return { success: true, message: 'Builder updated successfully', data: builder };
    }
    async remove(id) {
        await this.buildersService.remove(id);
        return { success: true, message: 'Builder deleted successfully' };
    }
};
exports.BuildersController = BuildersController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create Builder', description: 'Create a new builder' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_builder_dto_1.CreateBuilderDto]),
    __metadata("design:returntype", Promise)
], BuildersController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get All Builders', description: 'Retrieve all builders' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], BuildersController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get Builder by ID', description: 'Retrieve a builder by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Builder ID' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], BuildersController.prototype, "findById", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update Builder', description: 'Update a builder by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Builder ID' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_builder_dto_1.UpdateBuilderDto]),
    __metadata("design:returntype", Promise)
], BuildersController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete Builder', description: 'Delete a builder by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Builder ID' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], BuildersController.prototype, "remove", null);
exports.BuildersController = BuildersController = __decorate([
    (0, swagger_1.ApiTags)('🏗️ Builders'),
    (0, common_1.Controller)('builders'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [builders_service_1.BuildersService])
], BuildersController);
//# sourceMappingURL=builders.controller.js.map