import { Document } from 'mongoose';
export type AgentDocument = Agent & Document;
export declare class Agent {
    name: string;
    description?: string;
    email?: string;
    phone?: string;
    address?: string;
    licenseNumber?: string;
    profileImage?: string;
    isActive?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
}
export declare const AgentSchema: import("mongoose").Schema<Agent, import("mongoose").Model<Agent, any, any, any, Document<unknown, any, Agent, any> & Agent & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Agent, Document<unknown, {}, import("mongoose").FlatRecord<Agent>, {}> & import("mongoose").FlatRecord<Agent> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
