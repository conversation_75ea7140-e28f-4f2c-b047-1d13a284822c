import { PropertyTypesService } from './property-types.service';
import { CreatePropertyTypeDto } from './dto/create-property-type.dto';
import { UpdatePropertyTypeDto } from './dto/update-property-type.dto';
import { QueryMasterWithCategoryDto } from '../common/dto/query-master.dto';
import { PropertyTypeCategory } from '../common/enums/master-types.enum';
export declare class PropertyTypesController {
    private readonly propertyTypesService;
    constructor(propertyTypesService: PropertyTypesService);
    create(createPropertyTypeDto: CreatePropertyTypeDto): Promise<{
        success: boolean;
        data: import("./schemas/property-type.schema").PropertyType;
        message: string;
    }>;
    findAll(queryDto: QueryMasterWithCategoryDto): Promise<import("../common/interfaces/base-master.interface").MasterListResponse<import("./schemas/property-type.schema").PropertyType>>;
    getStatistics(): Promise<{
        success: boolean;
        data: {
            byCategory: any;
            byPopularityRating: any;
            bySuitability: any;
            totalCount: number;
            activeCount: number;
            inactiveCount: number;
            popularCount: number;
            defaultCount: number;
            byStatus?: Record<string, number>;
        };
        message: string;
    }>;
    findByCategory(category: PropertyTypeCategory): Promise<{
        success: boolean;
        data: import("./schemas/property-type.schema").PropertyType[];
        message: string;
    }>;
    findResidential(): Promise<{
        success: boolean;
        data: import("./schemas/property-type.schema").PropertyType[];
        message: string;
    }>;
    findCommercial(): Promise<{
        success: boolean;
        data: import("./schemas/property-type.schema").PropertyType[];
        message: string;
    }>;
    findOne(id: string): Promise<{
        success: boolean;
        data: import("./schemas/property-type.schema").PropertyType;
        message: string;
    }>;
    update(id: string, updatePropertyTypeDto: UpdatePropertyTypeDto): Promise<{
        success: boolean;
        data: import("./schemas/property-type.schema").PropertyType;
        message: string;
    }>;
    remove(id: string): Promise<{
        success: boolean;
        message: string;
    }>;
}
