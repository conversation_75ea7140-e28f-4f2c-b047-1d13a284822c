version: '3.8'

services:
  # NestJS Application (Development)
  app-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: trelax-admin-backend-dev
    restart: unless-stopped
    ports:
      - "3000:3000"
      - "9229:9229"  # Debug port
    environment:
      - NODE_ENV=development
      - NEST_DEBUG=true
    env_file:
      - .env.docker
    volumes:
      - .:/app
      - /app/node_modules
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      mongodb-dev:
        condition: service_healthy
      redis-dev:
        condition: service_healthy
      localstack-dev:
        condition: service_healthy
    networks:
      - trelax-dev-network
    command: npm run start:debug
    stdin_open: true
    tty: true

  # MongoDB Database (Development)
  mongodb-dev:
    image: mongo:7.0
    container_name: trelax-mongodb-dev
    restart: unless-stopped
    ports:
      - "27018:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: admin123
      MONGO_INITDB_DATABASE: trelax_dev_db
    volumes:
      - mongodb_dev_data:/data/db
      - mongodb_dev_config:/data/configdb
      - ./docker/mongodb/init-scripts:/docker-entrypoint-initdb.d
    networks:
      - trelax-dev-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis for Development
  redis-dev:
    image: redis:7.2-alpine
    container_name: trelax-redis-dev
    restart: unless-stopped
    ports:
      - "6380:6379"
    volumes:
      - redis_dev_data:/data
    command: redis-server --appendonly yes
    networks:
      - trelax-dev-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # LocalStack for Development
  localstack-dev:
    image: localstack/localstack:3.0
    container_name: trelax-localstack-dev
    restart: unless-stopped
    ports:
      - "4567:4566"
    environment:
      - SERVICES=s3
      - DEBUG=1
      - DATA_DIR=/tmp/localstack/data
      - AWS_DEFAULT_REGION=us-east-1
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
    volumes:
      - localstack_dev_data:/tmp/localstack
      - ./docker/localstack/init-scripts:/etc/localstack/init/ready.d
    networks:
      - trelax-dev-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4566/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # MongoDB Express for Development
  mongo-express-dev:
    image: mongo-express:1.0.2
    container_name: trelax-mongo-express-dev
    restart: unless-stopped
    ports:
      - "8083:8081"
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: admin
      ME_CONFIG_MONGODB_ADMINPASSWORD: admin123
      ME_CONFIG_MONGODB_URL: ******************************************/
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: admin123
    depends_on:
      mongodb-dev:
        condition: service_healthy
    networks:
      - trelax-dev-network

  # Redis Commander for Development
  redis-commander-dev:
    image: rediscommander/redis-commander:latest
    container_name: trelax-redis-commander-dev
    restart: unless-stopped
    ports:
      - "8084:8081"
    environment:
      - REDIS_HOSTS=local:redis-dev:6379
      - HTTP_USER=admin
      - HTTP_PASSWORD=admin123
    depends_on:
      redis-dev:
        condition: service_healthy
    networks:
      - trelax-dev-network

  # Test Database (Separate for E2E tests)
  mongodb-test:
    image: mongo:7.0
    container_name: trelax-mongodb-test
    restart: unless-stopped
    ports:
      - "27019:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: admin123
      MONGO_INITDB_DATABASE: trelax_test_db
    volumes:
      - mongodb_test_data:/data/db
    networks:
      - trelax-dev-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Test Runner Service
  test-runner:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: trelax-test-runner
    environment:
      - NODE_ENV=test
      - MONGO_URI_TEST=***************************************************************************
    env_file:
      - .env.test
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      mongodb-test:
        condition: service_healthy
    networks:
      - trelax-dev-network
    profiles:
      - testing
    command: npm run test:e2e

# Volumes for Development
volumes:
  mongodb_dev_data:
    driver: local
  mongodb_dev_config:
    driver: local
  mongodb_test_data:
    driver: local
  redis_dev_data:
    driver: local
  localstack_dev_data:
    driver: local

# Networks for Development
networks:
  trelax-dev-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
