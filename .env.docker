# Docker Environment Configuration
# This file is used when running the application in Docker containers

# Database Configuration
MONGO_URI=mongodb://mongodb:27017/trelax_admin_db

# JWT Configuration
JWT_SECRET=docker_jwt_secret_key_change_in_production_2024
JWT_EXPIRES_IN=7d

# AWS S3 Configuration (LocalStack for development)
AWS_ACCESS_KEY_ID=test
AWS_SECRET_ACCESS_KEY=test
AWS_REGION=us-east-1
AWS_S3_BUCKET=trelax-admin-uploads
AWS_ENDPOINT_URL=http://localstack:4566

# Server Configuration
PORT=3000
NODE_ENV=production

# API Configuration
API_PREFIX=api/v1

# Redis Configuration (for caching and sessions)
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=

# Database Configuration for different environments
MONGO_INITDB_ROOT_USERNAME=admin
MONGO_INITDB_ROOT_PASSWORD=admin123
MONGO_INITDB_DATABASE=trelax_admin_db

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# Security Configuration
CORS_ORIGIN=http://localhost:3001,http://localhost:3000
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_DEST=./uploads

# Health Check Configuration
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_INTERVAL=30000
