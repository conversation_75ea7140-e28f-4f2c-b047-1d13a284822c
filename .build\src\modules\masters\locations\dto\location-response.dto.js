"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SingleLocationResponseDto = exports.LocationListResponseDto = exports.LocationResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const master_types_enum_1 = require("../../common/enums/master-types.enum");
class LocationResponseDto {
}
exports.LocationResponseDto = LocationResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Location ID',
        example: '507f1f77bcf86cd799439012'
    }),
    __metadata("design:type", String)
], LocationResponseDto.prototype, "_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Location name',
        example: 'Bandra West'
    }),
    __metadata("design:type", String)
], LocationResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Location description',
        example: 'Premium residential and commercial area in Mumbai'
    }),
    __metadata("design:type", String)
], LocationResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Unique location code',
        example: 'BW'
    }),
    __metadata("design:type", String)
], LocationResponseDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Master type',
        enum: master_types_enum_1.MasterType,
        example: master_types_enum_1.MasterType.LOCATION
    }),
    __metadata("design:type", String)
], LocationResponseDto.prototype, "masterType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Status',
        enum: master_types_enum_1.MasterStatus,
        example: master_types_enum_1.MasterStatus.ACTIVE
    }),
    __metadata("design:type", String)
], LocationResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Parent city ID',
        example: '507f1f77bcf86cd799439011'
    }),
    __metadata("design:type", String)
], LocationResponseDto.prototype, "parentId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Parent type',
        enum: master_types_enum_1.MasterType,
        example: master_types_enum_1.MasterType.CITY
    }),
    __metadata("design:type", String)
], LocationResponseDto.prototype, "parentType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Location code within city',
        example: 'BW001'
    }),
    __metadata("design:type", String)
], LocationResponseDto.prototype, "locationCode", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Coordinates [longitude, latitude]',
        example: [72.8265, 19.0596]
    }),
    __metadata("design:type", Array)
], LocationResponseDto.prototype, "coordinates", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Full address',
        example: 'Bandra West, Mumbai, Maharashtra 400050'
    }),
    __metadata("design:type", String)
], LocationResponseDto.prototype, "address", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Pincode',
        example: '400050'
    }),
    __metadata("design:type", String)
], LocationResponseDto.prototype, "pincode", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Nearby landmark',
        example: 'Near Bandra Bandstand'
    }),
    __metadata("design:type", String)
], LocationResponseDto.prototype, "landmark", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Alternative names',
        example: ['Bandra (W)', 'West Bandra']
    }),
    __metadata("design:type", Array)
], LocationResponseDto.prototype, "alternateNames", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Location type',
        example: 'residential'
    }),
    __metadata("design:type", String)
], LocationResponseDto.prototype, "locationType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Location category',
        example: 'premium'
    }),
    __metadata("design:type", String)
], LocationResponseDto.prototype, "locationCategory", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Connectivity information'
    }),
    __metadata("design:type", Object)
], LocationResponseDto.prototype, "connectivity", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Real estate data'
    }),
    __metadata("design:type", Object)
], LocationResponseDto.prototype, "realEstateData", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Nearby facilities'
    }),
    __metadata("design:type", Object)
], LocationResponseDto.prototype, "nearbyFacilities", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Sort order',
        example: 1
    }),
    __metadata("design:type", Number)
], LocationResponseDto.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Is default location',
        example: false
    }),
    __metadata("design:type", Boolean)
], LocationResponseDto.prototype, "isDefault", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Is popular location',
        example: true
    }),
    __metadata("design:type", Boolean)
], LocationResponseDto.prototype, "isPopular", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'SEO title',
        example: 'Bandra West Mumbai - Premium Real Estate Location'
    }),
    __metadata("design:type", String)
], LocationResponseDto.prototype, "seoTitle", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'SEO description',
        example: 'Discover premium properties in Bandra West, Mumbai\'s most sought-after residential area.'
    }),
    __metadata("design:type", String)
], LocationResponseDto.prototype, "seoDescription", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'SEO keywords',
        example: ['bandra west properties', 'mumbai real estate', 'premium apartments bandra']
    }),
    __metadata("design:type", Array)
], LocationResponseDto.prototype, "seoKeywords", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Featured image URL',
        example: 'https://s3.amazonaws.com/bucket/bandra-west.jpg'
    }),
    __metadata("design:type", String)
], LocationResponseDto.prototype, "featuredImage", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Gallery images',
        example: ['https://s3.amazonaws.com/bucket/bandra-1.jpg', 'https://s3.amazonaws.com/bucket/bandra-2.jpg']
    }),
    __metadata("design:type", Array)
], LocationResponseDto.prototype, "gallery", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Additional metadata',
        example: { color: '#FF5733', icon: 'location-icon' }
    }),
    __metadata("design:type", Object)
], LocationResponseDto.prototype, "metadata", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Creation date',
        example: '2024-01-15T10:30:00.000Z'
    }),
    __metadata("design:type", Date)
], LocationResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Last update date',
        example: '2024-01-15T10:30:00.000Z'
    }),
    __metadata("design:type", Date)
], LocationResponseDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Full location name with city (virtual field)',
        example: 'Bandra West, Mumbai'
    }),
    __metadata("design:type", String)
], LocationResponseDto.prototype, "fullName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'GeoJSON location (virtual field)',
        example: { type: 'Point', coordinates: [72.8265, 19.0596] }
    }),
    __metadata("design:type", Object)
], LocationResponseDto.prototype, "geoLocation", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Parent city information (populated)',
        example: { _id: '507f1f77bcf86cd799439011', name: 'Mumbai', masterType: 'city' }
    }),
    __metadata("design:type", Object)
], LocationResponseDto.prototype, "parentCity", void 0);
class LocationListResponseDto {
}
exports.LocationListResponseDto = LocationListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success status',
        example: true
    }),
    __metadata("design:type", Boolean)
], LocationListResponseDto.prototype, "success", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'List of locations',
        type: [LocationResponseDto]
    }),
    __metadata("design:type", Array)
], LocationListResponseDto.prototype, "data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Pagination information'
    }),
    __metadata("design:type", Object)
], LocationListResponseDto.prototype, "pagination", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Response message',
        example: 'Locations retrieved successfully'
    }),
    __metadata("design:type", String)
], LocationListResponseDto.prototype, "message", void 0);
class SingleLocationResponseDto {
}
exports.SingleLocationResponseDto = SingleLocationResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success status',
        example: true
    }),
    __metadata("design:type", Boolean)
], SingleLocationResponseDto.prototype, "success", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Location data',
        type: LocationResponseDto
    }),
    __metadata("design:type", LocationResponseDto)
], SingleLocationResponseDto.prototype, "data", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Response message',
        example: 'Location retrieved successfully'
    }),
    __metadata("design:type", String)
], SingleLocationResponseDto.prototype, "message", void 0);
//# sourceMappingURL=location-response.dto.js.map