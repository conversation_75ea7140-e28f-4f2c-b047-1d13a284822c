"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PropertyTypeSchema = exports.PropertyType = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const base_master_schema_1 = require("../../common/schemas/base-master.schema");
const master_types_enum_1 = require("../../common/enums/master-types.enum");
let PropertyType = class PropertyType extends base_master_schema_1.MasterWithCategory {
};
exports.PropertyType = PropertyType;
__decorate([
    (0, mongoose_1.Prop)({
        required: true,
        type: String,
        enum: Object.values(master_types_enum_1.MasterType),
        default: master_types_enum_1.MasterType.PROPERTY_TYPE,
        immutable: true
    }),
    __metadata("design:type", String)
], PropertyType.prototype, "masterType", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: String,
        enum: Object.values(master_types_enum_1.PropertyTypeCategory),
        index: true
    }),
    __metadata("design:type", String)
], PropertyType.prototype, "category", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 50
    }),
    __metadata("design:type", String)
], PropertyType.prototype, "icon", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 10
    }),
    __metadata("design:type", String)
], PropertyType.prototype, "color", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: [String],
        default: []
    }),
    __metadata("design:type", Array)
], PropertyType.prototype, "suitableFor", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: Object,
        default: {}
    }),
    __metadata("design:type", Object)
], PropertyType.prototype, "specifications", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: [String],
        default: []
    }),
    __metadata("design:type", Array)
], PropertyType.prototype, "features", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: Object,
        default: {}
    }),
    __metadata("design:type", Object)
], PropertyType.prototype, "priceRange", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        min: 1,
        max: 5
    }),
    __metadata("design:type", Number)
], PropertyType.prototype, "popularityRating", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: [String],
        default: []
    }),
    __metadata("design:type", Array)
], PropertyType.prototype, "targetAudience", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: [String],
        default: []
    }),
    __metadata("design:type", Array)
], PropertyType.prototype, "legalRequirements", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 200
    }),
    __metadata("design:type", String)
], PropertyType.prototype, "seoTitle", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 500
    }),
    __metadata("design:type", String)
], PropertyType.prototype, "seoDescription", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true
    }),
    __metadata("design:type", String)
], PropertyType.prototype, "featuredImage", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: [String],
        default: []
    }),
    __metadata("design:type", Array)
], PropertyType.prototype, "gallery", void 0);
exports.PropertyType = PropertyType = __decorate([
    (0, mongoose_1.Schema)({
        timestamps: true,
        collection: 'masters',
        discriminatorKey: 'masterType'
    })
], PropertyType);
exports.PropertyTypeSchema = mongoose_1.SchemaFactory.createForClass(PropertyType);
exports.PropertyTypeSchema.index({ name: 1 }, { unique: true });
exports.PropertyTypeSchema.index({ code: 1 }, { unique: true, sparse: true });
exports.PropertyTypeSchema.index({ category: 1, status: 1 });
exports.PropertyTypeSchema.index({ popularityRating: 1 });
exports.PropertyTypeSchema.index({ suitableFor: 1 });
exports.PropertyTypeSchema.index({
    name: 'text',
    description: 'text',
    features: 'text',
    suitableFor: 'text',
    targetAudience: 'text'
}, {
    weights: {
        name: 10,
        suitableFor: 8,
        features: 6,
        description: 4,
        targetAudience: 2
    },
    name: 'property_type_text_index'
});
exports.PropertyTypeSchema.pre('save', function (next) {
    if (this.isNew) {
        this.masterType = master_types_enum_1.MasterType.PROPERTY_TYPE;
    }
    next();
});
exports.PropertyTypeSchema.virtual('displayName').get(function () {
    return this.category
        ? `${this.name} (${this.category})`
        : this.name;
});
exports.PropertyTypeSchema.set('toJSON', { virtuals: true });
exports.PropertyTypeSchema.set('toObject', { virtuals: true });
//# sourceMappingURL=property-type.schema.js.map