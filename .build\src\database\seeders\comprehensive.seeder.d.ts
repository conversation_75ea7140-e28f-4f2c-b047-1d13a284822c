import { Model } from 'mongoose';
import { CityDocument } from '../../modules/masters/cities/schemas/city.schema';
import { LocationDocument } from '../../modules/masters/locations/schemas/location.schema';
import { AmenityDocument } from '../../modules/masters/amenities/schemas/amenity.schema';
import { FloorDocument } from '../../modules/masters/floors/schemas/floor.schema';
import { TowerDocument } from '../../modules/masters/towers/schemas/tower.schema';
import { PropertyTypeDocument } from '../../modules/masters/property-types/schemas/property-type.schema';
import { RoomDocument } from '../../modules/masters/rooms/schemas/room.schema';
import { WashroomDocument } from '../../modules/masters/washrooms/schemas/washroom.schema';
import { ProjectDocument } from '../../modules/projects/schemas/project.schema';
import { BuilderDocument } from '../../modules/builders/schemas/builder.schema';
import { AgentDocument } from '../../modules/agents/schemas/agent.schema';
export declare class ComprehensiveSeeder {
    private cityModel;
    private locationModel;
    private amenityModel;
    private floorModel;
    private towerModel;
    private propertyTypeModel;
    private roomModel;
    private washroomModel;
    private projectModel;
    private builderModel;
    private agentModel;
    constructor(cityModel: Model<CityDocument>, locationModel: Model<LocationDocument>, amenityModel: Model<AmenityDocument>, floorModel: Model<FloorDocument>, towerModel: Model<TowerDocument>, propertyTypeModel: Model<PropertyTypeDocument>, roomModel: Model<RoomDocument>, washroomModel: Model<WashroomDocument>, projectModel: Model<ProjectDocument>, builderModel: Model<BuilderDocument>, agentModel: Model<AgentDocument>);
    seedAll(): Promise<void>;
    private clearDatabase;
    private seedCities;
    private seedLocations;
    private seedAmenities;
    private generatePinCodes;
    private getStateLanguage;
    private generateLocationName;
    private generateUniqueLocationCode;
    private generateNearbyCoordinates;
    private generateAmenityTags;
    private generateAmenityKeywords;
    private seedFloors;
    private seedTowers;
    private seedPropertyTypes;
    private seedRooms;
    private seedWashrooms;
    private getTargetFamily;
    private seedBuilders;
    private seedAgents;
    private seedProjects;
    private generateProjectName;
    private generateUnitConfigurations;
    private getBedroomsFromUnitType;
    private getUnitDisplayName;
    private getCarpetAreaRange;
    private getPriceRange;
    private generateMediaUrls;
    private getFileExtension;
    private generateProjectTags;
    private generateNearbyFacilities;
}
