"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TowerSchema = exports.Tower = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const base_master_schema_1 = require("../../common/schemas/base-master.schema");
const master_types_enum_1 = require("../../common/enums/master-types.enum");
let Tower = class Tower extends base_master_schema_1.MasterWithNumericValue {
};
exports.Tower = Tower;
__decorate([
    (0, mongoose_1.Prop)({
        required: true,
        type: String,
        enum: Object.values(master_types_enum_1.MasterType),
        default: master_types_enum_1.MasterType.TOWER,
        immutable: true
    }),
    __metadata("design:type", String)
], Tower.prototype, "masterType", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        index: true
    }),
    __metadata("design:type", Number)
], Tower.prototype, "numericValue", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 20,
        default: 'Tower'
    }),
    __metadata("design:type", String)
], Tower.prototype, "unit", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 50,
        enum: [
            'residential',
            'commercial',
            'mixed_use',
            'office',
            'retail',
            'hospitality',
            'industrial'
        ]
    }),
    __metadata("design:type", String)
], Tower.prototype, "towerType", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false
    }),
    __metadata("design:type", Number)
], Tower.prototype, "totalFloors", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false
    }),
    __metadata("design:type", Number)
], Tower.prototype, "height", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false
    }),
    __metadata("design:type", Number)
], Tower.prototype, "totalUnits", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false
    }),
    __metadata("design:type", Number)
], Tower.prototype, "unitsPerFloor", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: Object,
        default: {}
    }),
    __metadata("design:type", Object)
], Tower.prototype, "specifications", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: [String],
        default: []
    }),
    __metadata("design:type", Array)
], Tower.prototype, "amenities", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: [String],
        default: []
    }),
    __metadata("design:type", Array)
], Tower.prototype, "features", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        default: true
    }),
    __metadata("design:type", Boolean)
], Tower.prototype, "isActive", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false
    }),
    __metadata("design:type", Number)
], Tower.prototype, "constructionYear", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 100
    }),
    __metadata("design:type", String)
], Tower.prototype, "architect", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 100
    }),
    __metadata("design:type", String)
], Tower.prototype, "contractor", void 0);
exports.Tower = Tower = __decorate([
    (0, mongoose_1.Schema)({
        timestamps: true,
        collection: 'masters',
        discriminatorKey: 'masterType'
    })
], Tower);
exports.TowerSchema = mongoose_1.SchemaFactory.createForClass(Tower);
exports.TowerSchema.index({ numericValue: 1 }, { unique: true });
exports.TowerSchema.index({ code: 1 }, { unique: true, sparse: true });
exports.TowerSchema.index({ towerType: 1, status: 1 });
exports.TowerSchema.index({ isActive: 1, status: 1 });
exports.TowerSchema.pre('save', function (next) {
    if (this.isNew) {
        this.masterType = master_types_enum_1.MasterType.TOWER;
    }
    next();
});
exports.TowerSchema.virtual('displayName').get(function () {
    return this.numericValue
        ? `${this.unit || 'Tower'} ${this.numericValue}`
        : this.name;
});
exports.TowerSchema.set('toJSON', { virtuals: true });
exports.TowerSchema.set('toObject', { virtuals: true });
//# sourceMappingURL=tower.schema.js.map