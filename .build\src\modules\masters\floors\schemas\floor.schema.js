"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FloorSchema = exports.Floor = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const base_master_schema_1 = require("../../common/schemas/base-master.schema");
const master_types_enum_1 = require("../../common/enums/master-types.enum");
let Floor = class Floor extends base_master_schema_1.MasterWithNumericValue {
};
exports.Floor = Floor;
__decorate([
    (0, mongoose_1.Prop)({
        required: true,
        type: String,
        enum: Object.values(master_types_enum_1.MasterType),
        default: master_types_enum_1.MasterType.FLOOR,
        immutable: true
    }),
    __metadata("design:type", String)
], Floor.prototype, "masterType", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        index: true
    }),
    __metadata("design:type", Number)
], Floor.prototype, "numericValue", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 20,
        default: 'Floor'
    }),
    __metadata("design:type", String)
], Floor.prototype, "unit", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 50,
        enum: [
            'basement',
            'ground',
            'mezzanine',
            'regular',
            'penthouse',
            'rooftop',
            'parking',
            'mechanical',
            'terrace'
        ]
    }),
    __metadata("design:type", String)
], Floor.prototype, "floorType", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 50,
        enum: [
            'residential',
            'commercial',
            'mixed',
            'parking',
            'amenity',
            'mechanical',
            'retail',
            'office'
        ]
    }),
    __metadata("design:type", String)
], Floor.prototype, "usage", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false
    }),
    __metadata("design:type", Number)
], Floor.prototype, "height", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false
    }),
    __metadata("design:type", Number)
], Floor.prototype, "area", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: Object,
        default: {}
    }),
    __metadata("design:type", Object)
], Floor.prototype, "specifications", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: Object,
        default: {}
    }),
    __metadata("design:type", Object)
], Floor.prototype, "amenities", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: [String],
        default: []
    }),
    __metadata("design:type", Array)
], Floor.prototype, "restrictions", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: [String],
        default: []
    }),
    __metadata("design:type", Array)
], Floor.prototype, "features", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false
    }),
    __metadata("design:type", Number)
], Floor.prototype, "priceMultiplier", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false
    }),
    __metadata("design:type", Number)
], Floor.prototype, "premiumPercentage", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        default: true
    }),
    __metadata("design:type", Boolean)
], Floor.prototype, "isAvailable", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false
    }),
    __metadata("design:type", Number)
], Floor.prototype, "maxUnitsPerFloor", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 100
    }),
    __metadata("design:type", String)
], Floor.prototype, "displayName", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 200
    }),
    __metadata("design:type", String)
], Floor.prototype, "shortDescription", void 0);
exports.Floor = Floor = __decorate([
    (0, mongoose_1.Schema)({
        timestamps: true,
        collection: 'masters',
        discriminatorKey: 'masterType'
    })
], Floor);
exports.FloorSchema = mongoose_1.SchemaFactory.createForClass(Floor);
exports.FloorSchema.index({ numericValue: 1 }, { unique: true });
exports.FloorSchema.index({ code: 1 }, { unique: true, sparse: true });
exports.FloorSchema.index({ floorType: 1, status: 1 });
exports.FloorSchema.index({ usage: 1, status: 1 });
exports.FloorSchema.index({ isAvailable: 1, status: 1 });
exports.FloorSchema.index({ priceMultiplier: 1 });
exports.FloorSchema.index({
    name: 'text',
    description: 'text',
    displayName: 'text',
    shortDescription: 'text',
    features: 'text'
}, {
    weights: {
        name: 10,
        displayName: 8,
        shortDescription: 5,
        description: 3,
        features: 2
    },
    name: 'floor_text_index'
});
exports.FloorSchema.pre('save', function (next) {
    if (this.isNew) {
        this.masterType = master_types_enum_1.MasterType.FLOOR;
    }
    if (!this.displayName && this.numericValue !== undefined) {
        if (this.numericValue === 0) {
            this.displayName = 'Ground Floor';
        }
        else if (this.numericValue < 0) {
            this.displayName = `Basement ${Math.abs(this.numericValue)}`;
        }
        else {
            const suffix = this.numericValue === 1 ? 'st' :
                this.numericValue === 2 ? 'nd' :
                    this.numericValue === 3 ? 'rd' : 'th';
            this.displayName = `${this.numericValue}${suffix} Floor`;
        }
    }
    next();
});
exports.FloorSchema.virtual('levelDescription').get(function () {
    if (this.numericValue === undefined)
        return 'Not specified';
    if (this.numericValue < 0) {
        return `Basement Level ${Math.abs(this.numericValue)}`;
    }
    else if (this.numericValue === 0) {
        return 'Ground Level';
    }
    else {
        return `${this.numericValue} floors above ground`;
    }
});
exports.FloorSchema.virtual('accessibilityInfo').get(function () {
    const info = [];
    if (this.amenities?.elevator)
        info.push('Elevator Access');
    if (this.amenities?.escalator)
        info.push('Escalator Access');
    if (this.specifications?.accessibility)
        info.push('Wheelchair Accessible');
    if (this.amenities?.emergencyStaircase)
        info.push('Emergency Staircase');
    return info.length > 0 ? info.join(', ') : 'Standard Access';
});
exports.FloorSchema.set('toJSON', { virtuals: true });
exports.FloorSchema.set('toObject', { virtuals: true });
//# sourceMappingURL=floor.schema.js.map