import { NestFactory } from '@nestjs/core';
import { ExpressAdapter } from '@nestjs/platform-express';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { configure as serverlessExpress } from '@vendia/serverless-express';
import { Context, APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import express from 'express';
import { AppModule } from './app.module';

let cachedServer: any;

/**
 * Create and configure the NestJS application for Lambda
 */
async function createApp(): Promise<INestApplication> {
  // Create Express instance
  const expressApp = express();
  
  // Create NestJS application with Express adapter
  const app = await NestFactory.create(
    AppModule,
    new ExpressAdapter(expressApp),
    {
      logger: process.env.NODE_ENV === 'production' ? ['error', 'warn'] : ['log', 'error', 'warn', 'debug', 'verbose'],
    }
  );

  // Enable CORS
  app.enableCors({
    origin: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
    credentials: true,
  });

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    })
  );

  // Set global prefix
  app.setGlobalPrefix('api/v1');

  // Swagger configuration (only in development)
  if (process.env.NODE_ENV !== 'production') {
    const config = new DocumentBuilder()
      .setTitle('TrelaX Admin Backend API')
      .setDescription('RESTful API for TrelaX Real Estate Admin Backend')
      .setVersion('1.0')
      .addBearerAuth(
        {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          name: 'JWT',
          description: 'Enter JWT token',
          in: 'header',
        },
        'JWT-auth'
      )
      .addServer('/', 'Local Development')
      .addServer('/dev', 'Development Stage')
      .addServer('/prod', 'Production Stage')
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('api/v1/docs', app, document, {
      swaggerOptions: {
        persistAuthorization: true,
        tagsSorter: 'alpha',
        operationsSorter: 'alpha',
      },
    });
  }

  // Initialize the application
  await app.init();

  return app;
}

/**
 * Bootstrap function for Lambda
 */
async function bootstrap(): Promise<any> {
  if (!cachedServer) {
    try {
      const app = await createApp();
      const expressApp = app.getHttpAdapter().getInstance();
      
      // Configure serverless-express
      cachedServer = serverlessExpress({
        app: expressApp,
        logSettings: {
          level: process.env.NODE_ENV === 'production' ? 'warn' : 'info',
        },
        resolutionMode: 'PROMISE',
      });
    } catch (error) {
      console.error('Error during Lambda bootstrap:', error);
      throw error;
    }
  }

  return cachedServer;
}

/**
 * Lambda handler function
 */
export const handler = async (
  event: APIGatewayProxyEvent,
  context: Context
): Promise<APIGatewayProxyResult> => {
  // Set Lambda context timeout
  context.callbackWaitsForEmptyEventLoop = false;

  try {
    const server = await bootstrap();
    return await server(event, context);
  } catch (error) {
    console.error('Lambda handler error:', error);
    
    return {
      statusCode: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization, Accept',
      },
      body: JSON.stringify({
        success: false,
        message: 'Internal server error',
        error: process.env.NODE_ENV === 'production' ? 'Something went wrong' : error.message,
      }),
    };
  }
};

/**
 * Health check handler for monitoring
 */
export const healthCheck = async (): Promise<APIGatewayProxyResult> => {
  return {
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*',
    },
    body: JSON.stringify({
      success: true,
      message: 'Lambda function is healthy',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
    }),
  };
};

/**
 * Warmup handler to prevent cold starts
 */
export const warmup = async (): Promise<APIGatewayProxyResult> => {
  console.log('Lambda warmup invoked');
  
  return {
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      success: true,
      message: 'Lambda warmed up successfully',
      timestamp: new Date().toISOString(),
    }),
  };
};
