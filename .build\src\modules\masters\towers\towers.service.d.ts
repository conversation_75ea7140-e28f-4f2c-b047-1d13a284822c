import { Model } from 'mongoose';
import { Tower, TowerDocument } from './schemas/tower.schema';
import { BaseMasterService } from '../common/services/base-master.service';
import { CreateTowerDto } from './dto/create-tower.dto';
import { UpdateTowerDto } from './dto/update-tower.dto';
import { QueryMasterWithNumericRangeDto } from '../common/dto/query-master.dto';
import { MasterListResponse } from '../common/interfaces/base-master.interface';
export declare class TowersService extends BaseMasterService<Tower> {
    private towerModel;
    constructor(towerModel: Model<TowerDocument>);
    createTower(createTowerDto: CreateTowerDto): Promise<Tower>;
    findAllTowers(queryDto?: QueryMasterWithNumericRangeDto): Promise<MasterListResponse<Tower>>;
    findTowerById(id: string): Promise<Tower>;
    updateTower(id: string, updateTowerDto: UpdateTowerDto): Promise<Tower>;
    removeTower(id: string): Promise<void>;
    findTowersByType(towerType: string): Promise<Tower[]>;
    findActiveTowers(): Promise<Tower[]>;
    getTowerStatistics(): Promise<{
        activeTowersCount: number;
        totalUnitsAcrossAllTowers: any;
        byTowerType: any;
        totalCount: number;
        activeCount: number;
        inactiveCount: number;
        popularCount: number;
        defaultCount: number;
        byCategory?: Record<string, number>;
        byStatus?: Record<string, number>;
    }>;
    protected checkUsageBeforeDelete(id: string): Promise<void>;
}
