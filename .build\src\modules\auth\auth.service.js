"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const config_1 = require("@nestjs/config");
const bcrypt = require("bcryptjs");
let AuthService = class AuthService {
    constructor(jwtService, configService) {
        this.jwtService = jwtService;
        this.configService = configService;
        this.adminAccounts = [
            {
                id: 'admin1',
                email: '<EMAIL>',
                password: '$2a$12$LQv3c1yqBwEHxPiLnPZOQOsOHm5XQZQX5QzQX5QzQX5QzQX5QzQX5Q',
                firstName: 'Admin',
                lastName: 'User',
                role: 'admin',
            },
            {
                id: 'admin2',
                email: '<EMAIL>',
                password: '$2a$12$LQv3c1yqBwEHxPiLnPZOQOsOHm5XQZQX5QzQX5QzQX5QzQX5QzQX5Q',
                firstName: 'Super',
                lastName: 'Admin',
                role: 'super_admin',
            },
            {
                id: 'admin3',
                email: '<EMAIL>',
                password: '$2a$12$LQv3c1yqBwEHxPiLnPZOQOsOHm5XQZQX5QzQX5QzQX5QzQX5QzQX5Q',
                firstName: 'Manager',
                lastName: 'User',
                role: 'admin',
            },
        ];
        this.initializeAdminPasswords();
    }
    async initializeAdminPasswords() {
        const defaultPassword = 'admin123';
        const hashedPassword = await bcrypt.hash(defaultPassword, 12);
        this.adminAccounts.forEach(admin => {
            admin.password = hashedPassword;
        });
    }
    async validateUser(email, password) {
        try {
            const admin = this.adminAccounts.find(acc => acc.email === email);
            if (!admin) {
                return null;
            }
            const isPasswordValid = await bcrypt.compare(password, admin.password);
            if (!isPasswordValid) {
                return null;
            }
            const { password: _, ...result } = admin;
            return result;
        }
        catch (error) {
            return null;
        }
    }
    async login(loginDto) {
        const { email, password } = loginDto;
        const admin = await this.validateUser(email, password);
        if (!admin) {
            throw new common_1.UnauthorizedException('Invalid email or password');
        }
        const payload = {
            sub: admin.id,
            email: admin.email,
            role: admin.role,
        };
        const accessToken = this.jwtService.sign(payload);
        return {
            user: {
                id: admin.id,
                email: admin.email,
                firstName: admin.firstName,
                lastName: admin.lastName,
                role: admin.role,
                isActive: true,
            },
            tokens: {
                accessToken,
            },
        };
    }
    async validateJwtPayload(payload) {
        const admin = this.adminAccounts.find(acc => acc.id === payload.sub);
        if (!admin) {
            throw new common_1.UnauthorizedException('Admin not found');
        }
        const { password: _, ...result } = admin;
        return result;
    }
    async getProfile(adminId) {
        const admin = this.adminAccounts.find(acc => acc.id === adminId);
        if (!admin) {
            throw new common_1.UnauthorizedException('Admin not found');
        }
        return {
            id: admin.id,
            email: admin.email,
            firstName: admin.firstName,
            lastName: admin.lastName,
            role: admin.role,
            isActive: true,
        };
    }
    async refreshToken(adminId) {
        const admin = this.adminAccounts.find(acc => acc.id === adminId);
        if (!admin) {
            throw new common_1.UnauthorizedException('Admin not found');
        }
        const payload = {
            sub: admin.id,
            email: admin.email,
            role: admin.role,
        };
        const accessToken = this.jwtService.sign(payload);
        return {
            accessToken,
        };
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [jwt_1.JwtService,
        config_1.ConfigService])
], AuthService);
//# sourceMappingURL=auth.service.js.map