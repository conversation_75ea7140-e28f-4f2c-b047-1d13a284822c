{"version": 3, "file": "auth.e2e-spec.js", "sourceRoot": "", "sources": ["../../test/auth.e2e-spec.ts"], "names": [], "mappings": ";;AACA,qCAAqC;AACrC,uDAAmD;AAEnD,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;IACpC,IAAI,GAAqB,CAAC;IAE1B,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,GAAG,GAAG,MAAM,wBAAU,CAAC,aAAa,EAAE,CAAC;IACzC,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,IAAI,EAAE;QAClB,MAAM,wBAAU,CAAC,QAAQ,EAAE,CAAC;IAC9B,CAAC,CAAC,CAAC;IAEH,UAAU,CAAC,KAAK,IAAI,EAAE;QACpB,wBAAU,CAAC,SAAS,EAAE,CAAC;IACzB,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;YAChE,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC;gBACJ,KAAK,EAAE,kBAAkB;gBACzB,QAAQ,EAAE,UAAU;aACrB,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,wBAAU,CAAC,qBAAqB,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAC;YAC/D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACnD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAClD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC;YAC5E,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACvD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,kDAAkD,CAAC,CAAC;QAC/F,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;YACrE,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC;gBACJ,KAAK,EAAE,uBAAuB;gBAC9B,QAAQ,EAAE,UAAU;aACrB,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,wBAAU,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;YAC3C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;YAClE,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC;gBACJ,KAAK,EAAE,oBAAoB;gBAC3B,QAAQ,EAAE,UAAU;aACrB,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,wBAAU,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;YAC3C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACnE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC;gBACJ,KAAK,EAAE,qBAAqB;gBAC5B,QAAQ,EAAE,UAAU;aACrB,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,wBAAU,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;YAC7C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC;gBACJ,KAAK,EAAE,kBAAkB;gBACzB,QAAQ,EAAE,eAAe;aAC1B,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,wBAAU,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;YAC7C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC;gBACJ,QAAQ,EAAE,UAAU;aACrB,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,wBAAU,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,mCAAmC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC;gBACJ,KAAK,EAAE,kBAAkB;aAC1B,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,wBAAU,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,uCAAuC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC;gBACJ,KAAK,EAAE,eAAe;gBACtB,QAAQ,EAAE,UAAU;aACrB,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,wBAAU,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC,EAAE,CAAC;iBACR,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,wBAAU,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACpD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC;gBACJ,KAAK,EAAE,yCAAyC;gBAChD,QAAQ,EAAE,UAAU;aACrB,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,wBAAU,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;YACnD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC;gBACJ,KAAK,EAAE,2CAA2C;gBAClD,QAAQ,EAAE,UAAU;aACrB,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,wBAAU,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,WAAW,GAAG,MAAM,wBAAU,CAAC,cAAc,EAAE,CAAC;YAEtD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,sBAAsB,CAAC;iBAC3B,GAAG,CAAC,WAAW,CAAC;iBAChB,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,wBAAU,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;YAC3C,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAChD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACnD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YAClD,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,sBAAsB,CAAC;iBAC3B,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,wBAAU,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAC9C,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,sBAAsB,CAAC;iBAC3B,GAAG,CAAC,EAAE,aAAa,EAAE,sBAAsB,EAAE,CAAC;iBAC9C,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,wBAAU,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,sBAAsB,CAAC;iBAC3B,GAAG,CAAC,EAAE,aAAa,EAAE,QAAQ,EAAE,CAAC;iBAChC,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,wBAAU,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;YAG9C,MAAM,YAAY,GAAG,+IAA+I,CAAC;YAErK,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,GAAG,CAAC,sBAAsB,CAAC;iBAC3B,GAAG,CAAC,EAAE,aAAa,EAAE,UAAU,YAAY,EAAE,EAAE,CAAC;iBAChD,MAAM,CAAC,GAAG,CAAC,CAAC;YAEf,wBAAU,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,SAAS,GAAG;gBAChB,KAAK,EAAE,kBAAkB;gBACzB,QAAQ,EAAE,eAAe;aAC1B,CAAC;YAGF,MAAM,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAC7C,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACzB,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC,SAAS,CAAC,CACnB,CAAC;YAEF,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAG9C,MAAM,oBAAoB,GAAG,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,MAAM,KAAK,GAAG,CAAC,CAAC;QAE3E,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,2DAA2D,EAAE,KAAK,IAAI,EAAE;YACzE,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBAChD,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC;gBACJ,KAAK,EAAE,yBAAyB;gBAChC,QAAQ,EAAE,UAAU;aACrB,CAAC;iBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YAGf,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;YAC9D,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,sBAAsB,CAAC,CAAC;QACtE,CAAC,CAAC,CAAC;QAEH,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACvD,MAAM,SAAS,GAAG;gBAChB,KAAK,EAAE,kBAAkB;gBACzB,QAAQ,EAAE,UAAU;aACrB,CAAC;YAEF,MAAM,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAC5C,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;iBACzB,IAAI,CAAC,oBAAoB,CAAC;iBAC1B,IAAI,CAAC,SAAS,CAAC,CACnB,CAAC;YAEF,MAAM,SAAS,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAG9C,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBAC3B,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAClC,wBAAU,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;YAC7C,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}