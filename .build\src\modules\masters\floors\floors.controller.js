"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FloorsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const floors_service_1 = require("./floors.service");
const create_floor_dto_1 = require("./dto/create-floor.dto");
const update_floor_dto_1 = require("./dto/update-floor.dto");
const query_master_dto_1 = require("../common/dto/query-master.dto");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let FloorsController = class FloorsController {
    constructor(floorsService) {
        this.floorsService = floorsService;
    }
    async create(createFloorDto) {
        const floor = await this.floorsService.createFloor(createFloorDto);
        return {
            success: true,
            data: floor,
            message: 'Floor created successfully'
        };
    }
    async findAll(queryDto) {
        return await this.floorsService.findAllFloors(queryDto);
    }
    async getStatistics() {
        const statistics = await this.floorsService.getFloorStatistics();
        return {
            success: true,
            data: statistics,
            message: 'Floor statistics retrieved successfully'
        };
    }
    async findAvailable() {
        const floors = await this.floorsService.findAvailableFloors();
        return {
            success: true,
            data: floors,
            message: 'Available floors retrieved successfully'
        };
    }
    async findByType(type) {
        const floors = await this.floorsService.findFloorsByType(type);
        return {
            success: true,
            data: floors,
            message: `${type} floors retrieved successfully`
        };
    }
    async findByUsage(usage) {
        const floors = await this.floorsService.findFloorsByUsage(usage);
        return {
            success: true,
            data: floors,
            message: `${usage} floors retrieved successfully`
        };
    }
    async findInRange(min, max) {
        const floors = await this.floorsService.findFloorsInRange(min, max);
        return {
            success: true,
            data: floors,
            message: `Floors from ${min} to ${max} retrieved successfully`
        };
    }
    async findBasement() {
        const floors = await this.floorsService.findBasementFloors();
        return {
            success: true,
            data: floors,
            message: 'Basement floors retrieved successfully'
        };
    }
    async findGround() {
        const floor = await this.floorsService.findGroundFloor();
        return {
            success: true,
            data: floor,
            message: 'Ground floor retrieved successfully'
        };
    }
    async findUpper() {
        const floors = await this.floorsService.findUpperFloors();
        return {
            success: true,
            data: floors,
            message: 'Upper floors retrieved successfully'
        };
    }
    async findPremium() {
        const floors = await this.floorsService.findPremiumFloors();
        return {
            success: true,
            data: floors,
            message: 'Premium floors retrieved successfully'
        };
    }
    async findOne(id) {
        const floor = await this.floorsService.findFloorById(id);
        return {
            success: true,
            data: floor,
            message: 'Floor retrieved successfully'
        };
    }
    async update(id, updateFloorDto) {
        const floor = await this.floorsService.updateFloor(id, updateFloorDto);
        return {
            success: true,
            data: floor,
            message: 'Floor updated successfully'
        };
    }
    async remove(id) {
        await this.floorsService.removeFloor(id);
        return {
            success: true,
            message: 'Floor deleted successfully'
        };
    }
};
exports.FloorsController = FloorsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Create a new floor',
        description: 'Create a new floor master data entry with specifications and amenities.'
    }),
    (0, swagger_1.ApiBody)({ type: create_floor_dto_1.CreateFloorDto }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Floor created successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid input data'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CONFLICT,
        description: 'Floor with same number or code already exists'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Body)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_floor_dto_1.CreateFloorDto]),
    __metadata("design:returntype", Promise)
], FloorsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get all floors',
        description: 'Retrieve all floors with filtering, search, and pagination capabilities.'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Floors retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid query parameters'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Query)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [query_master_dto_1.QueryMasterWithNumericRangeDto]),
    __metadata("design:returntype", Promise)
], FloorsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('statistics'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get floor statistics',
        description: 'Retrieve comprehensive statistics about floors including counts by type and usage.'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Floor statistics retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], FloorsController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)('available'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get available floors',
        description: 'Retrieve list of floors that are available for booking.'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Available floors retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], FloorsController.prototype, "findAvailable", null);
__decorate([
    (0, common_1.Get)('type/:type'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get floors by type',
        description: 'Retrieve all floors of a specific type (basement, ground, regular, etc.).'
    }),
    (0, swagger_1.ApiParam)({
        name: 'type',
        description: 'Floor type',
        example: 'regular'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Floors retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Param)('type')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], FloorsController.prototype, "findByType", null);
__decorate([
    (0, common_1.Get)('usage/:usage'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get floors by usage',
        description: 'Retrieve all floors with a specific usage (residential, commercial, etc.).'
    }),
    (0, swagger_1.ApiParam)({
        name: 'usage',
        description: 'Floor usage',
        example: 'residential'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Floors retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Param)('usage')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], FloorsController.prototype, "findByUsage", null);
__decorate([
    (0, common_1.Get)('range'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get floors in range',
        description: 'Retrieve floors within a specific floor number range.'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'min',
        required: true,
        type: Number,
        description: 'Minimum floor number',
        example: 1
    }),
    (0, swagger_1.ApiQuery)({
        name: 'max',
        required: true,
        type: Number,
        description: 'Maximum floor number',
        example: 10
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Floors in range retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid range parameters'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Query)('min')),
    __param(1, (0, common_1.Query)('max')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number]),
    __metadata("design:returntype", Promise)
], FloorsController.prototype, "findInRange", null);
__decorate([
    (0, common_1.Get)('basement'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get basement floors',
        description: 'Retrieve all basement floors (below ground level).'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Basement floors retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], FloorsController.prototype, "findBasement", null);
__decorate([
    (0, common_1.Get)('ground'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get ground floor',
        description: 'Retrieve the ground floor (floor number 0).'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Ground floor retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Ground floor not found'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], FloorsController.prototype, "findGround", null);
__decorate([
    (0, common_1.Get)('upper'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get upper floors',
        description: 'Retrieve all upper floors (above ground level).'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Upper floors retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], FloorsController.prototype, "findUpper", null);
__decorate([
    (0, common_1.Get)('premium'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get premium floors',
        description: 'Retrieve floors with premium pricing (price multiplier > 1).'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Premium floors retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], FloorsController.prototype, "findPremium", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get floor by ID',
        description: 'Retrieve a specific floor by its unique identifier.'
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Floor ID',
        example: '507f1f77bcf86cd799439014'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Floor retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Floor not found'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid floor ID'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], FloorsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Update floor by ID',
        description: 'Update a specific floor with new information.'
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Floor ID',
        example: '507f1f77bcf86cd799439014'
    }),
    (0, swagger_1.ApiBody)({ type: update_floor_dto_1.UpdateFloorDto }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Floor updated successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Floor not found'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid input data or floor ID'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CONFLICT,
        description: 'Floor with same number or code already exists'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_floor_dto_1.UpdateFloorDto]),
    __metadata("design:returntype", Promise)
], FloorsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Delete floor by ID',
        description: 'Soft delete a specific floor (marks as archived).'
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Floor ID',
        example: '507f1f77bcf86cd799439014'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Floor deleted successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Floor not found'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Cannot delete floor as it is being used'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], FloorsController.prototype, "remove", null);
exports.FloorsController = FloorsController = __decorate([
    (0, swagger_1.ApiTags)('Masters - Floors'),
    (0, common_1.Controller)('masters/floors'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [floors_service_1.FloorsService])
], FloorsController);
//# sourceMappingURL=floors.controller.js.map