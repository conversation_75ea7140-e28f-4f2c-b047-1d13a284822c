"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseMasterService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("mongoose");
const master_types_enum_1 = require("../enums/master-types.enum");
let BaseMasterService = class BaseMasterService {
    constructor(model, masterType) {
        this.model = model;
        this.masterType = masterType;
    }
    async create(createDto) {
        try {
            const existingEntry = await this.model.findOne({
                name: createDto.name,
                masterType: this.masterType,
                status: { $ne: master_types_enum_1.MasterStatus.ARCHIVED }
            });
            if (existingEntry) {
                throw new common_1.ConflictException(`${this.masterType} with name '${createDto.name}' already exists`);
            }
            if (createDto.code) {
                const existingCode = await this.model.findOne({
                    code: createDto.code,
                    masterType: this.masterType,
                    status: { $ne: master_types_enum_1.MasterStatus.ARCHIVED }
                });
                if (existingCode) {
                    throw new common_1.ConflictException(`${this.masterType} with code '${createDto.code}' already exists`);
                }
            }
            const createdEntry = new this.model({
                ...createDto,
                masterType: this.masterType
            });
            return await createdEntry.save();
        }
        catch (error) {
            if (error instanceof common_1.ConflictException) {
                throw error;
            }
            throw new common_1.BadRequestException(`Failed to create ${this.masterType}: ${error.message}`);
        }
    }
    async findAll(queryOptions = {}) {
        try {
            const { page = 1, limit = 10, search, status, isDefault, isPopular, sortBy = 'sortOrder', sortOrder = 'asc', parentId, category } = queryOptions;
            const filter = {
                masterType: this.masterType,
                status: { $ne: master_types_enum_1.MasterStatus.ARCHIVED }
            };
            if (search) {
                filter.$or = [
                    { name: { $regex: search, $options: 'i' } },
                    { description: { $regex: search, $options: 'i' } },
                    { code: { $regex: search, $options: 'i' } }
                ];
            }
            if (status) {
                filter.status = status;
            }
            if (typeof isDefault === 'boolean') {
                filter.isDefault = isDefault;
            }
            if (typeof isPopular === 'boolean') {
                filter.isPopular = isPopular;
            }
            if (parentId) {
                filter.parentId = new mongoose_1.Types.ObjectId(parentId);
            }
            if (category) {
                filter.category = category;
            }
            const sort = {};
            sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
            const skip = (page - 1) * limit;
            const [data, total] = await Promise.all([
                this.model
                    .find(filter)
                    .populate('parentId', 'name masterType')
                    .sort(sort)
                    .skip(skip)
                    .limit(limit)
                    .exec(),
                this.model.countDocuments(filter)
            ]);
            const totalPages = Math.ceil(total / limit);
            return {
                success: true,
                data: data,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages
                }
            };
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch ${this.masterType} data: ${error.message}`);
        }
    }
    async findById(id) {
        try {
            let query = this.model.findOne({
                _id: id,
                masterType: this.masterType,
                status: { $ne: master_types_enum_1.MasterStatus.ARCHIVED }
            });
            if (this.masterType !== master_types_enum_1.MasterType.CITY) {
                query = query.populate('parentId', 'name masterType');
            }
            const entry = await query.exec();
            if (!entry) {
                throw new common_1.NotFoundException(`${this.masterType} with ID ${id} not found`);
            }
            return entry;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.BadRequestException(`Invalid ${this.masterType} ID: ${error.message}`);
        }
    }
    async update(id, updateDto) {
        try {
            const existingEntry = await this.findById(id);
            if (updateDto.name && updateDto.name !== existingEntry.name) {
                const nameConflict = await this.model.findOne({
                    name: updateDto.name,
                    masterType: this.masterType,
                    _id: { $ne: id },
                    status: { $ne: master_types_enum_1.MasterStatus.ARCHIVED }
                });
                if (nameConflict) {
                    throw new common_1.ConflictException(`${this.masterType} with name '${updateDto.name}' already exists`);
                }
            }
            if (updateDto.code && updateDto.code !== existingEntry.code) {
                const codeConflict = await this.model.findOne({
                    code: updateDto.code,
                    masterType: this.masterType,
                    _id: { $ne: id },
                    status: { $ne: master_types_enum_1.MasterStatus.ARCHIVED }
                });
                if (codeConflict) {
                    throw new common_1.ConflictException(`${this.masterType} with code '${updateDto.code}' already exists`);
                }
            }
            let updateQuery = this.model
                .findByIdAndUpdate(id, updateDto, { new: true, runValidators: true });
            if (this.masterType !== master_types_enum_1.MasterType.CITY) {
                updateQuery = updateQuery.populate('parentId', 'name masterType');
            }
            const updatedEntry = await updateQuery.exec();
            return updatedEntry;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.ConflictException) {
                throw error;
            }
            throw new common_1.BadRequestException(`Failed to update ${this.masterType}: ${error.message}`);
        }
    }
    async remove(id) {
        try {
            const entry = await this.findById(id);
            await this.checkUsageBeforeDelete(id);
            await this.model.findByIdAndUpdate(id, {
                status: master_types_enum_1.MasterStatus.ARCHIVED
            });
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.BadRequestException(`Failed to delete ${this.masterType}: ${error.message}`);
        }
    }
    async getStatistics() {
        try {
            const [totalCount, activeCount, inactiveCount, popularCount, defaultCount, categoryStats, statusStats] = await Promise.all([
                this.model.countDocuments({
                    masterType: this.masterType,
                    status: { $ne: master_types_enum_1.MasterStatus.ARCHIVED }
                }),
                this.model.countDocuments({
                    masterType: this.masterType,
                    status: master_types_enum_1.MasterStatus.ACTIVE
                }),
                this.model.countDocuments({
                    masterType: this.masterType,
                    status: master_types_enum_1.MasterStatus.INACTIVE
                }),
                this.model.countDocuments({
                    masterType: this.masterType,
                    isPopular: true,
                    status: { $ne: master_types_enum_1.MasterStatus.ARCHIVED }
                }),
                this.model.countDocuments({
                    masterType: this.masterType,
                    isDefault: true,
                    status: { $ne: master_types_enum_1.MasterStatus.ARCHIVED }
                }),
                this.model.aggregate([
                    {
                        $match: {
                            masterType: this.masterType,
                            status: { $ne: master_types_enum_1.MasterStatus.ARCHIVED },
                            category: { $exists: true, $ne: null }
                        }
                    },
                    { $group: { _id: '$category', count: { $sum: 1 } } }
                ]),
                this.model.aggregate([
                    {
                        $match: {
                            masterType: this.masterType,
                            status: { $ne: master_types_enum_1.MasterStatus.ARCHIVED }
                        }
                    },
                    { $group: { _id: '$status', count: { $sum: 1 } } }
                ])
            ]);
            const byCategory = categoryStats.reduce((acc, item) => {
                acc[item._id] = item.count;
                return acc;
            }, {});
            const byStatus = statusStats.reduce((acc, item) => {
                acc[item._id] = item.count;
                return acc;
            }, {});
            return {
                totalCount,
                activeCount,
                inactiveCount,
                popularCount,
                defaultCount,
                byCategory,
                byStatus
            };
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to get ${this.masterType} statistics: ${error.message}`);
        }
    }
    async checkUsageBeforeDelete(id) {
    }
    async findByParentId(parentId) {
        try {
            return await this.model
                .find({
                parentId: new mongoose_1.Types.ObjectId(parentId),
                masterType: this.masterType,
                status: { $ne: master_types_enum_1.MasterStatus.ARCHIVED }
            })
                .sort({ sortOrder: 1, name: 1 })
                .exec();
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch ${this.masterType} by parent: ${error.message}`);
        }
    }
    async findByCategory(category) {
        try {
            return await this.model
                .find({
                category,
                masterType: this.masterType,
                status: { $ne: master_types_enum_1.MasterStatus.ARCHIVED }
            })
                .sort({ sortOrder: 1, name: 1 })
                .exec();
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch ${this.masterType} by category: ${error.message}`);
        }
    }
};
exports.BaseMasterService = BaseMasterService;
exports.BaseMasterService = BaseMasterService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [mongoose_1.Model, String])
], BaseMasterService);
//# sourceMappingURL=base-master.service.js.map