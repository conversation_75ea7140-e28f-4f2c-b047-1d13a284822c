"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateFloorDto = exports.FloorAmenitiesDto = exports.FloorSpecificationsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const base_master_dto_1 = require("../../common/dto/base-master.dto");
const master_types_enum_1 = require("../../common/enums/master-types.enum");
class FloorSpecificationsDto {
}
exports.FloorSpecificationsDto = FloorSpecificationsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Ceiling height in feet',
        example: 10
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(6),
    (0, class_validator_1.Max)(30),
    __metadata("design:type", Number)
], FloorSpecificationsDto.prototype, "ceilingHeight", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Load capacity in kg/sq meter',
        example: 500
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], FloorSpecificationsDto.prototype, "loadCapacity", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Fire safety rating',
        example: 'Class A'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(50),
    __metadata("design:type", String)
], FloorSpecificationsDto.prototype, "fireRating", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Wheelchair accessible',
        example: true
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], FloorSpecificationsDto.prototype, "accessibility", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'HVAC system type',
        example: 'Central Air Conditioning'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], FloorSpecificationsDto.prototype, "hvacType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Electrical load capacity in KW',
        example: 100
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], FloorSpecificationsDto.prototype, "electricalLoad", void 0);
class FloorAmenitiesDto {
}
exports.FloorAmenitiesDto = FloorAmenitiesDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Elevator access available',
        example: true
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], FloorAmenitiesDto.prototype, "elevator", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Escalator access available',
        example: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], FloorAmenitiesDto.prototype, "escalator", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Fire exit available',
        example: true
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], FloorAmenitiesDto.prototype, "fireExit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Emergency staircase available',
        example: true
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], FloorAmenitiesDto.prototype, "emergencyStaircase", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Restrooms available',
        example: true
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], FloorAmenitiesDto.prototype, "restrooms", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Power backup available',
        example: true
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], FloorAmenitiesDto.prototype, "powerBackup", void 0);
class CreateFloorDto extends base_master_dto_1.MasterWithNumericValueDto {
    constructor() {
        super(...arguments);
        this.unit = 'Floor';
        this.isAvailable = true;
        this.masterType = master_types_enum_1.MasterType.FLOOR;
    }
}
exports.CreateFloorDto = CreateFloorDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Floor name',
        example: '1st Floor',
        maxLength: 100
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CreateFloorDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Floor description',
        example: 'First floor with residential units',
        maxLength: 500
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(500),
    __metadata("design:type", String)
], CreateFloorDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Unique floor code',
        example: '1F',
        maxLength: 20
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(20),
    __metadata("design:type", String)
], CreateFloorDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Floor number (0 for ground, negative for basement)',
        example: 1
    }),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], CreateFloorDto.prototype, "numericValue", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Unit type',
        example: 'Floor',
        maxLength: 20,
        default: 'Floor'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(20),
    __metadata("design:type", String)
], CreateFloorDto.prototype, "unit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Floor type',
        example: 'regular',
        enum: ['basement', 'ground', 'mezzanine', 'regular', 'penthouse', 'rooftop', 'parking', 'mechanical', 'terrace']
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['basement', 'ground', 'mezzanine', 'regular', 'penthouse', 'rooftop', 'parking', 'mechanical', 'terrace']),
    __metadata("design:type", String)
], CreateFloorDto.prototype, "floorType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Primary usage of the floor',
        example: 'residential',
        enum: ['residential', 'commercial', 'mixed', 'parking', 'amenity', 'mechanical', 'retail', 'office']
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['residential', 'commercial', 'mixed', 'parking', 'amenity', 'mechanical', 'retail', 'office']),
    __metadata("design:type", String)
], CreateFloorDto.prototype, "usage", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Floor height in feet',
        example: 10
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(6),
    (0, class_validator_1.Max)(30),
    __metadata("design:type", Number)
], CreateFloorDto.prototype, "height", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Floor area in square feet',
        example: 5000
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateFloorDto.prototype, "area", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Floor specifications',
        type: FloorSpecificationsDto
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => FloorSpecificationsDto),
    __metadata("design:type", FloorSpecificationsDto)
], CreateFloorDto.prototype, "specifications", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Floor amenities',
        type: FloorAmenitiesDto
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => FloorAmenitiesDto),
    __metadata("design:type", FloorAmenitiesDto)
], CreateFloorDto.prototype, "amenities", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Usage restrictions',
        example: ['No heavy machinery', 'Noise restrictions after 10 PM']
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateFloorDto.prototype, "restrictions", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Special features',
        example: ['High ceilings', 'Natural lighting', 'Balcony access']
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateFloorDto.prototype, "features", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Price multiplier compared to base floor',
        example: 1.2
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0.1),
    (0, class_validator_1.Max)(10),
    __metadata("design:type", Number)
], CreateFloorDto.prototype, "priceMultiplier", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Premium percentage for this floor',
        example: 15
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], CreateFloorDto.prototype, "premiumPercentage", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Floor is available for booking',
        example: true,
        default: true
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateFloorDto.prototype, "isAvailable", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Maximum units per floor',
        example: 8
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], CreateFloorDto.prototype, "maxUnitsPerFloor", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Display name for UI',
        example: '1st Floor',
        maxLength: 100
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CreateFloorDto.prototype, "displayName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Short description for listings',
        example: 'Premium residential floor with city views',
        maxLength: 200
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(200),
    __metadata("design:type", String)
], CreateFloorDto.prototype, "shortDescription", void 0);
//# sourceMappingURL=create-floor.dto.js.map