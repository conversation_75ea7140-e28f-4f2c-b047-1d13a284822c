"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PropertyTypesService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const property_type_schema_1 = require("./schemas/property-type.schema");
const base_master_service_1 = require("../common/services/base-master.service");
const master_types_enum_1 = require("../common/enums/master-types.enum");
let PropertyTypesService = class PropertyTypesService extends base_master_service_1.BaseMasterService {
    constructor(propertyTypeModel) {
        super(propertyTypeModel, master_types_enum_1.MasterType.PROPERTY_TYPE);
        this.propertyTypeModel = propertyTypeModel;
    }
    async createPropertyType(createPropertyTypeDto) {
        return await this.create(createPropertyTypeDto);
    }
    async findAllPropertyTypes(queryDto = {}) {
        try {
            const { page = 1, limit = 10, search, status, isDefault, isPopular, sortBy = 'sortOrder', sortOrder = 'asc', category } = queryDto;
            const filter = {
                masterType: master_types_enum_1.MasterType.PROPERTY_TYPE,
                status: { $ne: 'archived' }
            };
            if (search) {
                filter.$or = [
                    { name: { $regex: search, $options: 'i' } },
                    { description: { $regex: search, $options: 'i' } },
                    { code: { $regex: search, $options: 'i' } },
                    { features: { $regex: search, $options: 'i' } },
                    { suitableFor: { $regex: search, $options: 'i' } },
                    { targetAudience: { $regex: search, $options: 'i' } }
                ];
            }
            if (status)
                filter.status = status;
            if (typeof isDefault === 'boolean')
                filter.isDefault = isDefault;
            if (typeof isPopular === 'boolean')
                filter.isPopular = isPopular;
            if (category)
                filter.category = category;
            const sort = {};
            sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
            const skip = (page - 1) * limit;
            const [data, total] = await Promise.all([
                this.propertyTypeModel.find(filter).sort(sort).skip(skip).limit(limit).exec(),
                this.propertyTypeModel.countDocuments(filter)
            ]);
            const totalPages = Math.ceil(total / limit);
            return {
                success: true,
                data: data,
                pagination: { page, limit, total, totalPages }
            };
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch property types: ${error.message}`);
        }
    }
    async findPropertyTypeById(id) {
        return await this.findById(id);
    }
    async updatePropertyType(id, updatePropertyTypeDto) {
        return await this.update(id, updatePropertyTypeDto);
    }
    async removePropertyType(id) {
        return await this.remove(id);
    }
    async findPropertyTypesByCategory(category) {
        try {
            return await this.propertyTypeModel
                .find({
                category,
                masterType: master_types_enum_1.MasterType.PROPERTY_TYPE,
                status: { $ne: 'archived' }
            })
                .sort({ sortOrder: 1, name: 1 })
                .exec();
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch property types by category: ${error.message}`);
        }
    }
    async findResidentialPropertyTypes() {
        return this.findPropertyTypesByCategory(master_types_enum_1.PropertyTypeCategory.RESIDENTIAL);
    }
    async findCommercialPropertyTypes() {
        return this.findPropertyTypesByCategory(master_types_enum_1.PropertyTypeCategory.COMMERCIAL);
    }
    async getPropertyTypeStatistics() {
        try {
            const baseStats = await this.getStatistics();
            const [categoryStats, popularityStats, suitabilityStats] = await Promise.all([
                this.propertyTypeModel.aggregate([
                    {
                        $match: {
                            masterType: master_types_enum_1.MasterType.PROPERTY_TYPE,
                            status: { $ne: 'archived' }
                        }
                    },
                    { $group: { _id: '$category', count: { $sum: 1 } } },
                    { $sort: { count: -1 } }
                ]),
                this.propertyTypeModel.aggregate([
                    {
                        $match: {
                            masterType: master_types_enum_1.MasterType.PROPERTY_TYPE,
                            status: { $ne: 'archived' },
                            popularityRating: { $exists: true, $ne: null }
                        }
                    },
                    { $group: { _id: '$popularityRating', count: { $sum: 1 } } },
                    { $sort: { _id: 1 } }
                ]),
                this.propertyTypeModel.aggregate([
                    {
                        $match: {
                            masterType: master_types_enum_1.MasterType.PROPERTY_TYPE,
                            status: { $ne: 'archived' },
                            suitableFor: { $exists: true, $ne: [] }
                        }
                    },
                    { $unwind: '$suitableFor' },
                    { $group: { _id: '$suitableFor', count: { $sum: 1 } } },
                    { $sort: { count: -1 } }
                ])
            ]);
            const byCategory = categoryStats.reduce((acc, item) => {
                acc[item._id] = item.count;
                return acc;
            }, {});
            const byPopularityRating = popularityStats.reduce((acc, item) => {
                acc[`rating_${item._id}`] = item.count;
                return acc;
            }, {});
            const bySuitability = suitabilityStats.reduce((acc, item) => {
                acc[item._id] = item.count;
                return acc;
            }, {});
            return {
                ...baseStats,
                byCategory,
                byPopularityRating,
                bySuitability
            };
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to get property type statistics: ${error.message}`);
        }
    }
    async checkUsageBeforeDelete(id) {
    }
};
exports.PropertyTypesService = PropertyTypesService;
exports.PropertyTypesService = PropertyTypesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(property_type_schema_1.PropertyType.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], PropertyTypesService);
//# sourceMappingURL=property-types.service.js.map