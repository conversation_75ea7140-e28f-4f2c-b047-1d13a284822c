# 🚀 AWS Lambda Deployment Guide for TrelaX Admin Backend

This guide provides comprehensive instructions for deploying your NestJS application to AWS Lambda using the Serverless Framework.

## 📋 Overview

The Lambda deployment setup includes:
- **Serverless Framework** configuration for AWS Lambda
- **@vendia/serverless-express** for NestJS compatibility
- **API Gateway** integration with CORS support
- **S3 bucket** for file uploads
- **CloudWatch** logging and monitoring
- **Multi-stage deployment** (dev, prod)
- **Local testing** with serverless-offline

## 🛠️ Prerequisites

### 1. AWS Account Setup

1. **AWS Account**: Ensure you have an active AWS account
2. **AWS CLI**: Install and configure AWS CLI
   ```bash
   # Install AWS CLI
   pip install awscli
   
   # Configure credentials
   aws configure
   ```

3. **IAM Permissions**: Ensure your AWS user has the following permissions:
   - Lambda full access
   - API Gateway full access
   - S3 full access
   - CloudFormation full access
   - IAM role creation
   - CloudWatch Logs access

### 2. Development Environment

1. **Node.js**: Version 18.x or higher
2. **npm**: Latest version
3. **Serverless Framework**: Installed globally
   ```bash
   npm install -g serverless
   ```

## 📁 Project Structure

```
├── src/
│   ├── lambda.ts              # Lambda entry point
│   ├── main.ts                # Regular NestJS entry point
│   └── ...                    # Your application code
├── scripts/
│   ├── deploy-lambda.sh       # Linux/Mac deployment script
│   └── deploy-lambda.bat      # Windows deployment script
├── serverless.yml             # Main Serverless configuration
├── serverless.dev.yml         # Development configuration
├── serverless.prod.yml        # Production configuration
├── tsconfig.build.json        # TypeScript build configuration
├── webpack.lambda.config.js   # Webpack configuration for Lambda
└── .env.lambda               # Lambda environment variables
```

## ⚙️ Configuration Files

### 1. Lambda Entry Point (`src/lambda.ts`)

The Lambda entry point uses `@vendia/serverless-express` to wrap your NestJS application:

```typescript
import { configure as serverlessExpress } from '@vendia/serverless-express';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';

let cachedServer: any;

async function bootstrap(): Promise<any> {
  if (!cachedServer) {
    const app = await NestFactory.create(AppModule);
    // Configure app...
    await app.init();
    
    cachedServer = serverlessExpress({
      app: app.getHttpAdapter().getInstance(),
    });
  }
  return cachedServer;
}

export const handler = async (event, context) => {
  const server = await bootstrap();
  return server(event, context);
};
```

### 2. Serverless Configuration (`serverless.yml`)

Main configuration with:
- Node.js 18.x runtime
- API Gateway integration
- CORS configuration
- S3 bucket setup
- Environment variables

### 3. Environment-Specific Configurations

- **`serverless.dev.yml`**: Development stage with debug settings
- **`serverless.prod.yml`**: Production stage with optimizations

## 🚀 Deployment Process

### Quick Deployment

```bash
# Install dependencies
npm install

# Deploy to development
npm run sls:deploy

# Deploy to production
serverless deploy --stage prod --config serverless.prod.yml
```

### Using Deployment Scripts

#### Linux/Mac
```bash
# Make script executable
chmod +x scripts/deploy-lambda.sh

# Deploy to development
./scripts/deploy-lambda.sh --stage dev

# Deploy to production
./scripts/deploy-lambda.sh --stage prod

# Dry run (see what would be deployed)
./scripts/deploy-lambda.sh --dry-run

# Skip tests and build
./scripts/deploy-lambda.sh --skip-tests --skip-build
```

#### Windows
```cmd
REM Deploy to development
scripts\deploy-lambda.bat --stage dev

REM Deploy to production
scripts\deploy-lambda.bat --stage prod

REM Dry run
scripts\deploy-lambda.bat --dry-run
```

## 🧪 Local Testing

### 1. Serverless Offline

Test your Lambda function locally:

```bash
# Start local server
npm run sls:offline

# Access your API
curl http://localhost:3000/api/v1/health
```

### 2. Local Development

```bash
# Regular NestJS development
npm run start:dev

# Lambda-specific build
npm run build:lambda
```

## 📊 Monitoring and Debugging

### 1. CloudWatch Logs

```bash
# View logs
npm run sls:logs

# Follow logs in real-time
serverless logs -f main --tail

# View logs for specific stage
serverless logs -f main --stage prod --tail
```

### 2. Function Information

```bash
# Get deployment information
npm run sls:info

# Get function details
serverless info --stage prod
```

### 3. Health Checks

The deployment includes health check endpoints:

```bash
# Health check
curl https://your-api-gateway-url/health

# API documentation (dev only)
curl https://your-api-gateway-url/api/v1/docs
```

## 🔧 Configuration Options

### Environment Variables

Set these in your deployment environment or `.env.lambda`:

```bash
# Database
MONGO_URI=mongodb+srv://...

# JWT
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=7d

# AWS
AWS_REGION=ap-south-1
AWS_S3_BUCKET=your-bucket-name

# CORS
CORS_ORIGIN=https://your-frontend-domain.com
```

### Stage-Specific Settings

#### Development (`serverless.dev.yml`)
- Lower memory allocation (512MB)
- Debug logging enabled
- Relaxed CORS settings
- Shorter log retention (7 days)

#### Production (`serverless.prod.yml`)
- Higher memory allocation (1024MB)
- Error-only logging
- Strict CORS settings
- Longer log retention (30 days)
- CloudWatch alarms
- Reserved concurrency

## 🔒 Security Considerations

### 1. Environment Variables

- Never commit sensitive data to version control
- Use AWS Systems Manager Parameter Store for secrets
- Rotate JWT secrets regularly

### 2. IAM Permissions

The deployment creates minimal IAM roles with only necessary permissions:
- S3 bucket access
- CloudWatch Logs
- Lambda execution

### 3. API Gateway Security

- CORS properly configured
- Request validation enabled
- Rate limiting (can be added)

### 4. S3 Security

- Bucket policies for secure access
- HTTPS-only access in production
- Versioning enabled in production

## 📈 Performance Optimization

### 1. Cold Start Mitigation

- **Warmup function**: Prevents cold starts with scheduled invocation
- **Connection pooling**: Reuse database connections
- **Caching**: Lambda container reuse

### 2. Memory and Timeout Settings

```yaml
# Development
memorySize: 512
timeout: 30

# Production
memorySize: 1024
timeout: 30
reservedConcurrency: 50
```

### 3. Package Optimization

- Tree shaking with Webpack
- Exclude dev dependencies
- Minimize bundle size

## 🚨 Troubleshooting

### Common Issues

1. **Cold Start Timeouts**
   ```bash
   # Increase timeout in serverless.yml
   timeout: 30
   
   # Enable warmup function
   ENABLE_WARMUP=true
   ```

2. **Memory Issues**
   ```bash
   # Increase memory allocation
   memorySize: 1024
   ```

3. **Database Connection Issues**
   ```bash
   # Check connection string
   # Ensure MongoDB Atlas allows Lambda IP ranges
   # Use connection pooling
   ```

4. **CORS Issues**
   ```bash
   # Check CORS configuration in serverless.yml
   # Verify frontend domain in CORS_ORIGIN
   ```

### Debugging Steps

1. **Check CloudWatch Logs**
   ```bash
   serverless logs -f main --tail
   ```

2. **Test Locally**
   ```bash
   npm run sls:offline
   ```

3. **Validate Configuration**
   ```bash
   serverless print --stage dev
   ```

4. **Check AWS Resources**
   ```bash
   aws lambda list-functions
   aws s3 ls
   ```

## 📋 Deployment Checklist

### Pre-Deployment
- [ ] AWS credentials configured
- [ ] Environment variables set
- [ ] Tests passing
- [ ] Build successful
- [ ] Configuration validated

### Post-Deployment
- [ ] Health check endpoint responding
- [ ] API Gateway URL accessible
- [ ] S3 bucket created and accessible
- [ ] CloudWatch logs working
- [ ] Database connectivity verified

### Production Deployment
- [ ] Production environment variables set
- [ ] CORS origins configured for production domain
- [ ] CloudWatch alarms configured
- [ ] Backup and monitoring in place
- [ ] Performance testing completed

## 🔄 CI/CD Integration

### GitHub Actions Example

```yaml
name: Deploy to AWS Lambda

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run tests
        run: npm test
        
      - name: Deploy to Lambda
        run: ./scripts/deploy-lambda.sh --stage prod
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          MONGO_URI_PROD: ${{ secrets.MONGO_URI_PROD }}
          JWT_SECRET_PROD: ${{ secrets.JWT_SECRET_PROD }}
```

## 📚 Additional Resources

- [Serverless Framework Documentation](https://www.serverless.com/framework/docs/)
- [AWS Lambda Documentation](https://docs.aws.amazon.com/lambda/)
- [NestJS Documentation](https://docs.nestjs.com/)
- [@vendia/serverless-express](https://github.com/vendia/serverless-express)

## 🆘 Support

For deployment issues:

1. Check the troubleshooting section above
2. Review CloudWatch logs
3. Validate your AWS permissions
4. Test locally with serverless-offline
5. Check the GitHub issues for known problems

Your NestJS application is now ready for serverless deployment on AWS Lambda! 🎉
