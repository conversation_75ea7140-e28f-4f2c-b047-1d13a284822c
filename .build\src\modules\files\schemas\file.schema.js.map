{"version": 3, "file": "file.schema.js", "sourceRoot": "", "sources": ["../../../../../src/modules/files/schemas/file.schema.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,+CAA+D;AAC/D,uCAA2C;AAepC,IAAM,IAAI,GAAV,MAAM,IAAI;CAyGhB,CAAA;AAzGY,oBAAI;AAKf;IAJC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,IAAI;QACd,IAAI,EAAE,IAAI;KACX,CAAC;;sCACe;AAMjB;IAJC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,IAAI;QACd,IAAI,EAAE,IAAI;KACX,CAAC;;0CACmB;AAMrB;IAJC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,IAAI;QACd,IAAI,EAAE,IAAI;KACX,CAAC;;sCACe;AAMjB;IAJC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,IAAI;QACd,GAAG,EAAE,CAAC;KACP,CAAC;;kCACW;AAMb;IAJC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,IAAI;QACd,IAAI,EAAE,IAAI;KACX,CAAC;;iCACU;AAOZ;IALC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,IAAI;QACd,IAAI,EAAE,IAAI;QACV,MAAM,EAAE,IAAI;KACb,CAAC;;iCACU;AAMZ;IAJC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,IAAI;QACd,IAAI,EAAE,IAAI;KACX,CAAC;;oCACa;AAOf;IALC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,GAAG;KACf,CAAC;;yCACmB;AAOrB;IALC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,CAAC,MAAM,CAAC;QACd,OAAO,EAAE,EAAE;KACZ,CAAC;;kCACc;AAQhB;IANC,IAAA,eAAI,EAAC;QACJ,IAAI,EAAE,gBAAK,CAAC,QAAQ;QACpB,GAAG,EAAE,MAAM;QACX,QAAQ,EAAE,IAAI;QACd,KAAK,EAAE,IAAI;KACZ,CAAC;8BACU,gBAAK,CAAC,QAAQ;wCAAC;AAM3B;IAJC,IAAA,eAAI,EAAC;QACJ,OAAO,EAAE,IAAI;QACb,KAAK,EAAE,IAAI;KACZ,CAAC;;sCACgB;AAKlB;IAHC,IAAA,eAAI,EAAC;QACJ,OAAO,EAAE,KAAK;KACf,CAAC;;sCACgB;AAKlB;IAHC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;KAChB,CAAC;8BACU,IAAI;uCAAC;AAKjB;IAHC,IAAA,eAAI,EAAC;QACJ,OAAO,EAAE,CAAC;KACX,CAAC;;2CACoB;AAKtB;IAHC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;KAChB,CAAC;8BACe,IAAI;4CAAC;AAUtB;IAPC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,IAAI;QACV,IAAI,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC;QACjE,OAAO,EAAE,OAAO;QAChB,KAAK,EAAE,IAAI;KACZ,CAAC;;sCACgB;eApGP,IAAI;IAJhB,IAAA,iBAAM,EAAC;QACN,UAAU,EAAE,IAAI;QAChB,UAAU,EAAE,OAAO;KACpB,CAAC;GACW,IAAI,CAyGhB;AAKY,QAAA,UAAU,GAAG,wBAAa,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;AAG7D,kBAAU,CAAC,KAAK,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC,CAAC;AACpC,kBAAU,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;AAClC,kBAAU,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,CAAC;AAClC,kBAAU,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AACpC,kBAAU,CAAC,KAAK,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;AAG/C,kBAAU,CAAC,GAAG,CAAC,MAAM,EAAE,UAAS,IAAI;IAClC,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;QAChC,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;QAE7C,IAAI,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YAClC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAC1B,CAAC;aAAM,IAAI,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YACzC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAC1B,CAAC;aAAM,IAAI,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;YACzC,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAC1B,CAAC;aAAM,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAClG,IAAI,CAAC,QAAQ,GAAG,UAAU,CAAC;QAC7B,CAAC;aAAM,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YAC5F,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;QAC5B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAC1B,CAAC;IACH,CAAC;IACD,IAAI,EAAE,CAAC;AACT,CAAC,CAAC,CAAC"}