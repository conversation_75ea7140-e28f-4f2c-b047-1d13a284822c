@echo off
setlocal enabledelayedexpansion

REM Docker Manager Script for TrelaX Admin Backend (Windows)
REM This script provides easy commands to manage Docker containers

set PROJECT_NAME=trelax-admin-backend

REM Function to print colored output (Windows doesn't support colors easily, so we'll use plain text)
:print_status
echo [INFO] %~1
goto :eof

:print_warning
echo [WARNING] %~1
goto :eof

:print_error
echo [ERROR] %~1
goto :eof

:print_header
echo ================================
echo %~1
echo ================================
goto :eof

REM Function to check if Docker is running
:check_docker
docker info >nul 2>&1
if errorlevel 1 (
    call :print_error "Docker is not running. Please start Docker and try again."
    exit /b 1
)
goto :eof

REM Function to check if Docker Compose is available
:check_docker_compose
docker-compose --version >nul 2>&1
if errorlevel 1 (
    call :print_error "Docker Compose is not installed. Please install Docker Compose and try again."
    exit /b 1
)
goto :eof

REM Function to create necessary directories
:create_directories
call :print_status "Creating necessary directories..."
if not exist logs\nginx mkdir logs\nginx
if not exist uploads mkdir uploads
if not exist coverage mkdir coverage
if not exist test-results mkdir test-results
if not exist performance-results mkdir performance-results
goto :eof

REM Function to start production environment
:start_production
call :print_header "Starting Production Environment"
call :check_docker
call :check_docker_compose
call :create_directories

call :print_status "Building and starting production containers..."
docker-compose -f docker-compose.yml up -d --build

call :print_status "Waiting for services to be healthy..."
timeout /t 30 /nobreak >nul

call :print_status "Checking service status..."
docker-compose -f docker-compose.yml ps

call :print_status "Production environment started successfully!"
call :print_status "API: http://localhost:3000"
call :print_status "MongoDB Express: http://localhost:8081"
call :print_status "Redis Commander: http://localhost:8082"
goto :eof

REM Function to start development environment
:start_development
call :print_header "Starting Development Environment"
call :check_docker
call :check_docker_compose
call :create_directories

call :print_status "Building and starting development containers..."
docker-compose -f docker-compose.dev.yml up -d --build

call :print_status "Waiting for services to be healthy..."
timeout /t 30 /nobreak >nul

call :print_status "Checking service status..."
docker-compose -f docker-compose.dev.yml ps

call :print_status "Development environment started successfully!"
call :print_status "API: http://localhost:3000 (with hot reload)"
call :print_status "Debug Port: 9229"
call :print_status "MongoDB Express: http://localhost:8083"
call :print_status "Redis Commander: http://localhost:8084"
goto :eof

REM Function to run tests
:run_tests
call :print_header "Running Tests"
call :check_docker
call :check_docker_compose

call :print_status "Starting test environment..."
docker-compose -f docker-compose.test.yml up -d mongodb-test redis-test localstack-test

call :print_status "Waiting for test services to be ready..."
timeout /t 20 /nobreak >nul

call :print_status "Running E2E tests..."
docker-compose -f docker-compose.test.yml --profile testing run --rm e2e-tests

call :print_status "Running unit tests..."
docker-compose -f docker-compose.test.yml --profile testing run --rm unit-tests

call :print_status "Cleaning up test environment..."
docker-compose -f docker-compose.test.yml down -v

call :print_status "Tests completed successfully!"
goto :eof

REM Function to stop all environments
:stop_all
call :print_header "Stopping All Environments"

call :print_status "Stopping production environment..."
docker-compose -f docker-compose.yml down 2>nul

call :print_status "Stopping development environment..."
docker-compose -f docker-compose.dev.yml down 2>nul

call :print_status "Stopping test environment..."
docker-compose -f docker-compose.test.yml down -v 2>nul

call :print_status "All environments stopped successfully!"
goto :eof

REM Function to show status
:show_status
call :print_header "Service Status"

call :print_status "Production Environment:"
docker-compose -f docker-compose.yml ps 2>nul || echo Not running

echo.
call :print_status "Development Environment:"
docker-compose -f docker-compose.dev.yml ps 2>nul || echo Not running

echo.
call :print_status "Docker System Info:"
docker system df
goto :eof

REM Function to show help
:show_help
echo TrelaX Admin Backend Docker Manager (Windows)
echo.
echo Usage: %0 [COMMAND]
echo.
echo Commands:
echo   start-prod              Start production environment
echo   start-dev               Start development environment
echo   stop                    Stop all environments
echo   restart-prod            Restart production environment
echo   restart-dev             Restart development environment
echo   test                    Run all tests
echo   status                  Show service status
echo   help                    Show this help message
echo.
echo Examples:
echo   %0 start-dev            Start development environment
echo   %0 test                 Run all tests
echo   %0 stop                 Stop all environments
goto :eof

REM Main script logic
if "%1"=="start-prod" (
    call :start_production
) else if "%1"=="start-dev" (
    call :start_development
) else if "%1"=="stop" (
    call :stop_all
) else if "%1"=="restart-prod" (
    call :stop_all
    call :start_production
) else if "%1"=="restart-dev" (
    call :stop_all
    call :start_development
) else if "%1"=="test" (
    call :run_tests
) else if "%1"=="status" (
    call :show_status
) else if "%1"=="help" (
    call :show_help
) else if "%1"=="--help" (
    call :show_help
) else if "%1"=="-h" (
    call :show_help
) else if "%1"=="" (
    call :print_error "No command specified."
    echo.
    call :show_help
) else (
    call :print_error "Invalid command: %1"
    echo.
    call :show_help
)
