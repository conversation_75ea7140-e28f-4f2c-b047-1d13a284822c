"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AmenitySchema = exports.Amenity = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const base_master_schema_1 = require("../../common/schemas/base-master.schema");
const master_types_enum_1 = require("../../common/enums/master-types.enum");
let Amenity = class Amenity extends base_master_schema_1.MasterWithCategory {
};
exports.Amenity = Amenity;
__decorate([
    (0, mongoose_1.Prop)({
        required: true,
        type: String,
        enum: Object.values(master_types_enum_1.MasterType),
        default: master_types_enum_1.MasterType.AMENITY,
        immutable: true
    }),
    __metadata("design:type", String)
], Amenity.prototype, "masterType", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: String,
        enum: Object.values(master_types_enum_1.AmenityCategory),
        index: true
    }),
    __metadata("design:type", String)
], Amenity.prototype, "category", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 50
    }),
    __metadata("design:type", String)
], Amenity.prototype, "icon", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 10
    }),
    __metadata("design:type", String)
], Amenity.prototype, "color", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: [String],
        default: []
    }),
    __metadata("design:type", Array)
], Amenity.prototype, "tags", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: Object,
        default: {}
    }),
    __metadata("design:type", Object)
], Amenity.prototype, "specifications", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: Object,
        default: {}
    }),
    __metadata("design:type", Object)
], Amenity.prototype, "availability", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        min: 1,
        max: 5
    }),
    __metadata("design:type", Number)
], Amenity.prototype, "importanceLevel", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        min: 0,
        max: 100
    }),
    __metadata("design:type", Number)
], Amenity.prototype, "popularityScore", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: [String],
        default: []
    }),
    __metadata("design:type", Array)
], Amenity.prototype, "relatedAmenities", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: [String],
        default: []
    }),
    __metadata("design:type", Array)
], Amenity.prototype, "keywords", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 200
    }),
    __metadata("design:type", String)
], Amenity.prototype, "seoTitle", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 500
    }),
    __metadata("design:type", String)
], Amenity.prototype, "seoDescription", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true
    }),
    __metadata("design:type", String)
], Amenity.prototype, "featuredImage", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: [String],
        default: []
    }),
    __metadata("design:type", Array)
], Amenity.prototype, "gallery", void 0);
exports.Amenity = Amenity = __decorate([
    (0, mongoose_1.Schema)({
        timestamps: true,
        collection: 'masters',
        discriminatorKey: 'masterType'
    })
], Amenity);
exports.AmenitySchema = mongoose_1.SchemaFactory.createForClass(Amenity);
exports.AmenitySchema.index({ name: 1 }, { unique: true });
exports.AmenitySchema.index({ code: 1 }, { unique: true, sparse: true });
exports.AmenitySchema.index({ category: 1, status: 1 });
exports.AmenitySchema.index({ isPopular: 1, status: 1 });
exports.AmenitySchema.index({ importanceLevel: 1 });
exports.AmenitySchema.index({ popularityScore: 1 });
exports.AmenitySchema.index({ tags: 1 });
exports.AmenitySchema.index({ 'availability.residential': 1 });
exports.AmenitySchema.index({ 'availability.commercial': 1 });
exports.AmenitySchema.index({ 'availability.luxury': 1 });
exports.AmenitySchema.index({
    name: 'text',
    description: 'text',
    tags: 'text',
    keywords: 'text',
    relatedAmenities: 'text'
}, {
    weights: {
        name: 10,
        tags: 8,
        keywords: 6,
        description: 4,
        relatedAmenities: 2
    },
    name: 'amenity_text_index'
});
exports.AmenitySchema.pre('save', function (next) {
    if (this.isNew) {
        this.masterType = master_types_enum_1.MasterType.AMENITY;
    }
    next();
});
exports.AmenitySchema.virtual('displayName').get(function () {
    return this.category
        ? `${this.name} (${this.category})`
        : this.name;
});
exports.AmenitySchema.virtual('importanceText').get(function () {
    if (!this.importanceLevel)
        return 'Not specified';
    const levels = {
        1: 'Low',
        2: 'Below Average',
        3: 'Average',
        4: 'High',
        5: 'Critical'
    };
    return levels[this.importanceLevel] || 'Not specified';
});
exports.AmenitySchema.virtual('popularityText').get(function () {
    if (!this.popularityScore)
        return 'Not specified';
    if (this.popularityScore >= 80)
        return 'Very Popular';
    if (this.popularityScore >= 60)
        return 'Popular';
    if (this.popularityScore >= 40)
        return 'Moderately Popular';
    if (this.popularityScore >= 20)
        return 'Less Popular';
    return 'Rarely Requested';
});
exports.AmenitySchema.set('toJSON', { virtuals: true });
exports.AmenitySchema.set('toObject', { virtuals: true });
//# sourceMappingURL=amenity.schema.js.map