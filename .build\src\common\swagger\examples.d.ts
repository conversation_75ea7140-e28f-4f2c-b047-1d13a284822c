export declare const SwaggerExamples: {
    auth: {
        loginRequest: {
            email: string;
            password: string;
        };
        loginResponse: {
            success: boolean;
            message: string;
            data: {
                user: {
                    id: string;
                    email: string;
                    firstName: string;
                    lastName: string;
                    role: string;
                    isActive: boolean;
                };
                tokens: {
                    accessToken: string;
                };
            };
            timestamp: string;
        };
        profileResponse: {
            success: boolean;
            message: string;
            data: {
                id: string;
                email: string;
                firstName: string;
                lastName: string;
                role: string;
                isActive: boolean;
            };
            timestamp: string;
        };
    };
    projects: {
        createRequest: {
            projectName: string;
            projectDescription: string;
            builder: {
                name: string;
                description: string;
                website: string;
                email: string;
                phone: string;
            };
            projectStatus: string;
            location: {
                address: string;
                city: string;
                state: string;
                country: string;
                pincode: string;
                landmark: string;
                coordinates: number[];
            };
            reraNumber: string;
            propertyType: string;
            unitConfigurations: {
                type: string;
                name: string;
                bedrooms: number;
                bathrooms: number;
                area: number;
                priceRange: {
                    min: number;
                    max: number;
                };
                totalUnits: number;
                availableUnits: number;
                facing: string[];
            }[];
            amenities: {
                basic: string[];
                security: string[];
                recreational: string[];
            };
            isFeatured: boolean;
            isActive: boolean;
        };
        listResponse: {
            success: boolean;
            message: string;
            data: {
                id: string;
                projectName: string;
                projectDescription: string;
                builder: {
                    name: string;
                };
                projectStatus: string;
                location: {
                    city: string;
                    state: string;
                };
                propertyType: string;
                isFeatured: boolean;
                createdAt: string;
            }[];
            pagination: {
                page: number;
                limit: number;
                total: number;
                totalPages: number;
                hasNext: boolean;
                hasPrev: boolean;
            };
            timestamp: string;
        };
    };
    masters: {
        createRequest: {
            fieldType: string;
            name: string;
            description: string;
            status: string;
            sortOrder: number;
            isDefault: boolean;
        };
        createLocationRequest: {
            fieldType: string;
            name: string;
            description: string;
            parentId: string;
            status: string;
            sortOrder: number;
            isDefault: boolean;
        };
        createAmenityRequest: {
            fieldType: string;
            name: string;
            description: string;
            status: string;
            sortOrder: number;
            isDefault: boolean;
        };
        listResponse: {
            success: boolean;
            message: string;
            data: {
                id: string;
                fieldType: string;
                name: string;
                description: string;
                status: string;
                sortOrder: number;
                isDefault: boolean;
                createdAt: string;
            }[];
            pagination: {
                page: number;
                limit: number;
                total: number;
                totalPages: number;
                hasNext: boolean;
                hasPrev: boolean;
            };
            timestamp: string;
        };
        dropdownResponse: {
            success: boolean;
            message: string;
            data: {
                id: string;
                fieldType: string;
                name: string;
                sortOrder: number;
                isDefault: boolean;
            }[];
            timestamp: string;
        };
    };
    files: {
        uploadResponse: {
            success: boolean;
            message: string;
            data: {
                id: string;
                originalName: string;
                fileName: string;
                mimeType: string;
                size: number;
                category: string;
                s3Key: string;
                s3Url: string;
                uploadedBy: string;
                createdAt: string;
            };
            timestamp: string;
        };
        listResponse: {
            success: boolean;
            message: string;
            data: {
                id: string;
                originalName: string;
                fileName: string;
                mimeType: string;
                size: number;
                category: string;
                s3Url: string;
                createdAt: string;
            }[];
            pagination: {
                page: number;
                limit: number;
                total: number;
                totalPages: number;
                hasNext: boolean;
                hasPrev: boolean;
            };
            timestamp: string;
        };
    };
    errors: {
        unauthorized: {
            success: boolean;
            message: string;
            error: string;
            timestamp: string;
        };
        badRequest: {
            success: boolean;
            message: string;
            error: string;
            timestamp: string;
        };
        notFound: {
            success: boolean;
            message: string;
            error: string;
            timestamp: string;
        };
    };
};
export declare const SwaggerSchemas: {
    StandardResponse: {
        type: string;
        properties: {
            success: {
                type: string;
            };
            message: {
                type: string;
            };
            data: {
                type: string;
            };
            timestamp: {
                type: string;
                format: string;
            };
        };
    };
    PaginatedResponse: {
        type: string;
        properties: {
            success: {
                type: string;
            };
            message: {
                type: string;
            };
            data: {
                type: string;
                items: {
                    type: string;
                };
            };
            pagination: {
                type: string;
                properties: {
                    page: {
                        type: string;
                    };
                    limit: {
                        type: string;
                    };
                    total: {
                        type: string;
                    };
                    totalPages: {
                        type: string;
                    };
                    hasNext: {
                        type: string;
                    };
                    hasPrev: {
                        type: string;
                    };
                };
            };
            timestamp: {
                type: string;
                format: string;
            };
        };
    };
    ErrorResponse: {
        type: string;
        properties: {
            success: {
                type: string;
                example: boolean;
            };
            message: {
                type: string;
            };
            error: {
                type: string;
            };
            timestamp: {
                type: string;
                format: string;
            };
        };
    };
};
