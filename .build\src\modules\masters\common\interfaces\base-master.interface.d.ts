import { Document } from 'mongoose';
import { MasterStatus, MasterType } from '../enums/master-types.enum';
export interface BaseMaster {
    name: string;
    description?: string;
    code?: string;
    masterType: MasterType;
    status: MasterStatus;
    sortOrder?: number;
    isDefault?: boolean;
    isPopular?: boolean;
    metadata?: Record<string, any>;
    createdAt?: Date;
    updatedAt?: Date;
}
export interface BaseMasterDocument extends BaseMaster, Document {
}
export interface MasterWithParent extends BaseMaster {
    parentId?: string;
    parentType?: MasterType;
}
export interface MasterWithCategory extends BaseMaster {
    category?: string;
    icon?: string;
    color?: string;
}
export interface MasterWithNumericValue extends BaseMaster {
    numericValue?: number;
    unit?: string;
    minValue?: number;
    maxValue?: number;
}
export interface MasterWithLocation extends BaseMaster {
    state?: string;
    country?: string;
    coordinates?: [number, number];
    timezone?: string;
    pinCodes?: string[];
}
export interface MasterQueryOptions {
    page?: number;
    limit?: number;
    search?: string;
    status?: MasterStatus;
    isDefault?: boolean;
    isPopular?: boolean;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    parentId?: string;
    category?: string;
}
export interface MasterResponse<T = BaseMaster> {
    success: boolean;
    data: T;
    message?: string;
}
export interface MasterListResponse<T = BaseMaster> {
    success: boolean;
    data: T[];
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
    };
    message?: string;
}
export interface MasterStatistics {
    totalCount: number;
    activeCount: number;
    inactiveCount: number;
    popularCount: number;
    defaultCount: number;
    byCategory?: Record<string, number>;
    byStatus?: Record<string, number>;
}
