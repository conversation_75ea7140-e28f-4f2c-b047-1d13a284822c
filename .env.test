# Test Environment Configuration
NODE_ENV=test

# Test Database Configuration
MONGO_URI_TEST=mongodb://localhost:27017/trelax_test_db

# JWT Configuration for Tests
JWT_SECRET=test_jwt_secret_key_for_e2e_tests_only
JWT_EXPIRES_IN=1h

# AWS S3 Configuration (Mock for tests)
AWS_ACCESS_KEY_ID=test_access_key
AWS_SECRET_ACCESS_KEY=test_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=trelax-test-uploads

# Server Configuration
PORT=3001
API_PREFIX=api/v1

# Test-specific settings
SUPPRESS_LOGS=true
TEST_TIMEOUT=30000
