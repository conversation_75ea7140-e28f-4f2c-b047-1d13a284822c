@echo off
setlocal enabledelayedexpansion

REM Lambda Deployment Script for TrelaX Admin Backend (Windows)
REM This script handles the complete deployment process for AWS Lambda

set STAGE=dev
set REGION=ap-south-1
set VERBOSE=false
set DRY_RUN=false
set SKIP_BUILD=false
set SKIP_TESTS=false

:parse_args
if "%1"=="" goto main
if "%1"=="-s" (
    set STAGE=%2
    shift
    shift
    goto parse_args
)
if "%1"=="--stage" (
    set STAGE=%2
    shift
    shift
    goto parse_args
)
if "%1"=="-r" (
    set REGION=%2
    shift
    shift
    goto parse_args
)
if "%1"=="--region" (
    set REGION=%2
    shift
    shift
    goto parse_args
)
if "%1"=="-v" (
    set VERBOSE=true
    shift
    goto parse_args
)
if "%1"=="--verbose" (
    set VERBOSE=true
    shift
    goto parse_args
)
if "%1"=="-d" (
    set DRY_RUN=true
    shift
    goto parse_args
)
if "%1"=="--dry-run" (
    set DRY_RUN=true
    shift
    goto parse_args
)
if "%1"=="--skip-build" (
    set SKIP_BUILD=true
    shift
    goto parse_args
)
if "%1"=="--skip-tests" (
    set SKIP_TESTS=true
    shift
    goto parse_args
)
if "%1"=="-h" goto show_help
if "%1"=="--help" goto show_help

echo [ERROR] Unknown option: %1
goto show_help

:show_help
echo TrelaX Admin Backend Lambda Deployment Script (Windows)
echo.
echo Usage: %0 [OPTIONS]
echo.
echo Options:
echo   -s, --stage STAGE       Deployment stage (dev, prod) [default: dev]
echo   -r, --region REGION     AWS region [default: ap-south-1]
echo   -v, --verbose           Enable verbose output
echo   -d, --dry-run           Show what would be deployed without deploying
echo   --skip-build            Skip the build process
echo   --skip-tests            Skip running tests
echo   -h, --help              Show this help message
echo.
echo Examples:
echo   %0 --stage dev          Deploy to development stage
echo   %0 --stage prod         Deploy to production stage
echo   %0 --dry-run            Show deployment plan without deploying
goto :eof

:print_status
echo [INFO] %~1
goto :eof

:print_warning
echo [WARNING] %~1
goto :eof

:print_error
echo [ERROR] %~1
goto :eof

:print_header
echo ================================
echo %~1
echo ================================
goto :eof

:check_prerequisites
call :print_status "Checking prerequisites..."

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    call :print_error "Node.js is not installed. Please install Node.js and try again."
    exit /b 1
)

REM Check if npm is installed
npm --version >nul 2>&1
if errorlevel 1 (
    call :print_error "npm is not installed. Please install npm and try again."
    exit /b 1
)

REM Check if Serverless Framework is installed
serverless --version >nul 2>&1
if errorlevel 1 (
    call :print_warning "Serverless Framework is not installed globally. Installing..."
    npm install -g serverless
)

call :print_status "Prerequisites check completed."
goto :eof

:install_dependencies
if "%SKIP_BUILD%"=="false" (
    call :print_status "Installing dependencies..."
    npm ci --production=false
    if errorlevel 1 (
        call :print_error "Failed to install dependencies."
        exit /b 1
    )
    call :print_status "Dependencies installed successfully."
) else (
    call :print_status "Skipping dependency installation."
)
goto :eof

:run_tests
if "%SKIP_TESTS%"=="false" (
    call :print_status "Running tests..."
    npm run test
    if errorlevel 1 (
        call :print_error "Tests failed."
        exit /b 1
    )
    call :print_status "Tests completed successfully."
) else (
    call :print_status "Skipping tests."
)
goto :eof

:build_application
if "%SKIP_BUILD%"=="false" (
    call :print_status "Building application for Lambda..."
    npm run build:lambda
    if errorlevel 1 (
        call :print_error "Build failed."
        exit /b 1
    )
    call :print_status "Application built successfully."
) else (
    call :print_status "Skipping build process."
)
goto :eof

:validate_config
call :print_status "Validating Serverless configuration..."

set CONFIG_FILE=serverless.yml
if "%STAGE%"=="dev" set CONFIG_FILE=serverless.dev.yml
if "%STAGE%"=="prod" set CONFIG_FILE=serverless.prod.yml

if not exist "%CONFIG_FILE%" (
    call :print_error "Serverless configuration file '%CONFIG_FILE%' not found."
    exit /b 1
)

call :print_status "Configuration validation completed."
goto :eof

:deploy_lambda
call :print_header "Deploying to AWS Lambda"

set CONFIG_FILE=serverless.yml
if "%STAGE%"=="dev" set CONFIG_FILE=serverless.dev.yml
if "%STAGE%"=="prod" set CONFIG_FILE=serverless.prod.yml

set DEPLOY_CMD=serverless deploy --stage %STAGE% --region %REGION% --config %CONFIG_FILE%

if "%VERBOSE%"=="true" set DEPLOY_CMD=%DEPLOY_CMD% --verbose

if "%DRY_RUN%"=="true" (
    call :print_status "DRY RUN: Would execute: %DEPLOY_CMD%"
    serverless package --stage %STAGE% --config %CONFIG_FILE%
    call :print_status "Package created successfully. Deployment skipped (dry run)."
    goto :eof
)

call :print_status "Executing deployment..."
%DEPLOY_CMD%
if errorlevel 1 (
    call :print_error "Deployment failed."
    exit /b 1
)

call :print_status "Deployment completed successfully!"
goto :eof

:show_deployment_info
call :print_header "Deployment Information"

set CONFIG_FILE=serverless.yml
if "%STAGE%"=="dev" set CONFIG_FILE=serverless.dev.yml
if "%STAGE%"=="prod" set CONFIG_FILE=serverless.prod.yml

serverless info --stage %STAGE% --config %CONFIG_FILE%
goto :eof

:main
REM Validate stage
if not "%STAGE%"=="dev" if not "%STAGE%"=="prod" (
    call :print_error "Invalid stage: %STAGE%. Must be 'dev' or 'prod'."
    exit /b 1
)

call :print_header "TrelaX Admin Backend Lambda Deployment"
call :print_status "Stage: %STAGE%"
call :print_status "Region: %REGION%"
call :print_status "Dry Run: %DRY_RUN%"
echo.

call :check_prerequisites
if errorlevel 1 exit /b 1

call :install_dependencies
if errorlevel 1 exit /b 1

call :run_tests
if errorlevel 1 exit /b 1

call :build_application
if errorlevel 1 exit /b 1

call :validate_config
if errorlevel 1 exit /b 1

call :deploy_lambda
if errorlevel 1 exit /b 1

if "%DRY_RUN%"=="false" (
    call :show_deployment_info
    
    call :print_header "Deployment Completed Successfully!"
    call :print_status "Your TrelaX Admin Backend is now running on AWS Lambda."
    call :print_status "Stage: %STAGE%"
    call :print_status "Region: %REGION%"
)
