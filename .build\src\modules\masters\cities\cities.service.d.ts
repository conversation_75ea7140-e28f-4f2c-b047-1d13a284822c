import { Model } from 'mongoose';
import { City, CityDocument } from './schemas/city.schema';
import { BaseMasterService } from '../common/services/base-master.service';
import { CreateCityDto } from './dto/create-city.dto';
import { UpdateCityDto } from './dto/update-city.dto';
import { QueryCityDto } from './dto/query-city.dto';
import { MasterListResponse } from '../common/interfaces/base-master.interface';
export declare class CitiesService extends BaseMasterService<City> {
    private cityModel;
    constructor(cityModel: Model<CityDocument>);
    createCity(createCityDto: CreateCityDto): Promise<City>;
    findAllCities(queryDto?: QueryCityDto): Promise<MasterListResponse<City>>;
    findCityById(id: string): Promise<City>;
    updateCity(id: string, updateCityDto: UpdateCityDto): Promise<City>;
    removeCity(id: string): Promise<void>;
    findCitiesByState(state: string): Promise<City[]>;
    findCitiesByCountry(country: string): Promise<City[]>;
    findPopularCities(limit?: number): Promise<City[]>;
    findCitiesNearLocation(longitude: number, latitude: number, maxDistance?: number): Promise<City[]>;
    getCityStatistics(): Promise<{
        popularCitiesCount: number;
        citiesWithRealEstateData: number;
        averagePropertyPrice: any;
        byState: any;
        byCountry: any;
        totalCount: number;
        activeCount: number;
        inactiveCount: number;
        popularCount: number;
        defaultCount: number;
        byCategory?: Record<string, number>;
        byStatus?: Record<string, number>;
    }>;
    protected checkUsageBeforeDelete(id: string): Promise<void>;
}
