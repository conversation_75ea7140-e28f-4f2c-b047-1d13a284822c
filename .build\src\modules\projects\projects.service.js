"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectsService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const project_schema_1 = require("./schemas/project.schema");
const s3_service_1 = require("../files/services/s3.service");
let ProjectsService = class ProjectsService {
    constructor(projectModel, s3Service) {
        this.projectModel = projectModel;
        this.s3Service = s3Service;
    }
    async create(createProjectDto, userId) {
        try {
            const existingProject = await this.projectModel.findOne({
                projectName: createProjectDto.projectName,
                isActive: true,
            });
            if (existingProject) {
                throw new common_1.ConflictException('Project with this name already exists');
            }
            const createdProject = new this.projectModel({
                ...createProjectDto,
                possessionDate: createProjectDto.possessionDate ? new Date(createProjectDto.possessionDate) : undefined,
            });
            return await createdProject.save();
        }
        catch (error) {
            console.error('Error in ProjectsService.create:', error);
            if (error instanceof common_1.ConflictException) {
                throw error;
            }
            throw new common_1.BadRequestException('Failed to create project');
        }
    }
    async findAll(queryDto) {
        try {
            const { page, limit, search, projectStatus, propertyType, city, state, builder, priceMin, priceMax, bedroomsMin, bedroomsMax, unitType, isActive, isFeatured, approvalStatus, amenities, tags, sortBy, sortOrder, latitude, longitude, radius, } = queryDto;
            const filter = {};
            if (search) {
                filter.$or = [
                    { projectName: { $regex: search, $options: 'i' } },
                    { projectDescription: { $regex: search, $options: 'i' } },
                    { 'builder.name': { $regex: search, $options: 'i' } },
                ];
            }
            if (projectStatus) {
                filter.projectStatus = projectStatus;
            }
            if (propertyType) {
                filter.propertyType = propertyType;
            }
            if (city) {
                filter['location.city'] = { $regex: city, $options: 'i' };
            }
            if (state) {
                filter['location.state'] = { $regex: state, $options: 'i' };
            }
            if (builder) {
                filter['builder.name'] = { $regex: builder, $options: 'i' };
            }
            if (priceMin !== undefined || priceMax !== undefined) {
                filter.$or = filter.$or || [];
                const priceFilter = {};
                if (priceMin !== undefined)
                    priceFilter.$gte = priceMin;
                if (priceMax !== undefined)
                    priceFilter.$lte = priceMax;
                filter.$or.push({ priceMin: priceFilter }, { priceMax: priceFilter }, { 'unitConfigurations.priceMin': priceFilter }, { 'unitConfigurations.priceMax': priceFilter });
            }
            if (bedroomsMin !== undefined || bedroomsMax !== undefined) {
                const bedroomFilter = {};
                if (bedroomsMin !== undefined)
                    bedroomFilter.$gte = bedroomsMin;
                if (bedroomsMax !== undefined)
                    bedroomFilter.$lte = bedroomsMax;
                filter['unitConfigurations.bedrooms'] = bedroomFilter;
            }
            if (unitType) {
                filter['unitConfigurations.type'] = unitType;
            }
            if (typeof isActive === 'boolean') {
                filter.isActive = isActive;
            }
            if (typeof isFeatured === 'boolean') {
                filter.isFeatured = isFeatured;
            }
            if (approvalStatus) {
                filter.approvalStatus = approvalStatus;
            }
            if (amenities) {
                const amenityList = amenities.split(',').map(a => a.trim());
                filter.$or = filter.$or || [];
                amenityList.forEach(amenity => {
                    filter.$or.push({ 'amenities.basic': { $regex: amenity, $options: 'i' } }, { 'amenities.security': { $regex: amenity, $options: 'i' } }, { 'amenities.recreational': { $regex: amenity, $options: 'i' } }, { 'amenities.convenience': { $regex: amenity, $options: 'i' } }, { 'amenities.connectivity': { $regex: amenity, $options: 'i' } });
                });
            }
            if (tags) {
                const tagList = tags.split(',').map(t => t.trim());
                filter.tags = { $in: tagList };
            }
            if (latitude !== undefined && longitude !== undefined && radius !== undefined) {
                filter['location.coordinates'] = {
                    $near: {
                        $geometry: {
                            type: 'Point',
                            coordinates: [longitude, latitude],
                        },
                        $maxDistance: radius * 1000,
                    },
                };
            }
            const sort = {};
            sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
            const skip = (page - 1) * limit;
            const [projects, total] = await Promise.all([
                this.projectModel
                    .find(filter)
                    .populate({
                    path: 'location.cityId',
                    select: 'name masterType status',
                    options: { strictPopulate: false }
                })
                    .populate({
                    path: 'location.locationId',
                    select: 'name masterType status',
                    options: { strictPopulate: false }
                })
                    .populate({
                    path: 'amenities.amenityIds',
                    select: 'name masterType status description category',
                    options: { strictPopulate: false }
                })
                    .populate({
                    path: 'createdBy',
                    select: 'firstName lastName email',
                    options: { strictPopulate: false }
                })
                    .populate({
                    path: 'updatedBy',
                    select: 'firstName lastName email',
                    options: { strictPopulate: false }
                })
                    .sort(sort)
                    .skip(skip)
                    .limit(limit)
                    .exec(),
                this.projectModel.countDocuments(filter),
            ]);
            return {
                projects,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages: Math.ceil(total / limit),
                    hasNext: page < Math.ceil(total / limit),
                    hasPrev: page > 1,
                },
            };
        }
        catch (error) {
            console.error('Error in ProjectsService.findAll:', error);
            throw new common_1.BadRequestException('Failed to fetch projects');
        }
    }
    async findById(id) {
        try {
            const project = await this.projectModel
                .findById(id)
                .populate({
                path: 'location.cityId',
                select: 'name masterType status',
                options: { strictPopulate: false }
            })
                .populate({
                path: 'location.locationId',
                select: 'name masterType status',
                options: { strictPopulate: false }
            })
                .populate({
                path: 'amenities.amenityIds',
                select: 'name masterType status description category',
                options: { strictPopulate: false }
            })
                .populate({
                path: 'createdBy',
                select: 'firstName lastName email',
                options: { strictPopulate: false }
            })
                .populate({
                path: 'updatedBy',
                select: 'firstName lastName email',
                options: { strictPopulate: false }
            })
                .exec();
            if (!project) {
                throw new common_1.NotFoundException('Project not found');
            }
            await this.projectModel.findByIdAndUpdate(id, { $inc: { viewCount: 1 } });
            return project;
        }
        catch (error) {
            console.error('Error in ProjectsService.findById:', error);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.BadRequestException('Invalid project ID');
        }
    }
    async update(id, updateProjectDto, userId) {
        try {
            const updateData = {
                ...updateProjectDto,
                possessionDate: updateProjectDto.possessionDate ? new Date(updateProjectDto.possessionDate) : undefined,
            };
            const project = await this.projectModel
                .findByIdAndUpdate(id, updateData, { new: true, runValidators: true })
                .exec();
            if (!project) {
                throw new common_1.NotFoundException('Project not found');
            }
            return project;
        }
        catch (error) {
            console.error('Error in ProjectsService.update:', error);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.BadRequestException('Failed to update project');
        }
    }
    async remove(id) {
        try {
            const project = await this.projectModel
                .findByIdAndUpdate(id, { isActive: false, updatedAt: new Date() }, { new: true })
                .exec();
            if (!project) {
                throw new common_1.NotFoundException('Project not found');
            }
        }
        catch (error) {
            console.error('Error in ProjectsService.remove:', error);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.BadRequestException('Failed to delete project');
        }
    }
    async uploadMedia(projectId, files, mediaType, userId) {
        try {
            const project = await this.projectModel.findById(projectId);
            if (!project) {
                throw new common_1.NotFoundException('Project not found');
            }
            const uploadPromises = files.map(file => this.s3Service.uploadFile(file, `projects/${projectId}/${mediaType}`, false));
            const uploadResults = await Promise.all(uploadPromises);
            const urls = uploadResults.map(result => result.url);
            const updatePath = `media.${mediaType}`;
            await this.projectModel.findByIdAndUpdate(projectId, {
                $push: { [updatePath]: { $each: urls } },
            });
            return urls;
        }
        catch (error) {
            console.error('Error in ProjectsService.uploadMedia:', error);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.BadRequestException('Failed to upload media files');
        }
    }
    async uploadDocuments(projectId, files, documentType, userId) {
        try {
            const project = await this.projectModel.findById(projectId);
            if (!project) {
                throw new common_1.NotFoundException('Project not found');
            }
            const uploadPromises = files.map(file => this.s3Service.uploadFile(file, `projects/${projectId}/documents/${documentType}`, false));
            const uploadResults = await Promise.all(uploadPromises);
            const urls = uploadResults.map(result => result.url);
            const updatePath = `documents.${documentType}`;
            await this.projectModel.findByIdAndUpdate(projectId, {
                $push: { [updatePath]: { $each: urls } },
            });
            return urls;
        }
        catch (error) {
            console.error('Error in ProjectsService.uploadDocuments:', error);
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.BadRequestException('Failed to upload document files');
        }
    }
    async getStatistics() {
        try {
            const [totalProjects, activeProjects, inactiveProjects, featuredProjects, statusStats, propertyTypeStats, cityStats,] = await Promise.all([
                this.projectModel.countDocuments(),
                this.projectModel.countDocuments({ isActive: true }),
                this.projectModel.countDocuments({ isActive: false }),
                this.projectModel.countDocuments({ isFeatured: true }),
                this.projectModel.aggregate([
                    { $group: { _id: '$projectStatus', count: { $sum: 1 } } },
                ]),
                this.projectModel.aggregate([
                    { $group: { _id: '$propertyType', count: { $sum: 1 } } },
                ]),
                this.projectModel.aggregate([
                    { $group: { _id: '$location.city', count: { $sum: 1 } } },
                    { $sort: { count: -1 } },
                    { $limit: 10 },
                ]),
            ]);
            return {
                totalProjects,
                activeProjects,
                inactiveProjects,
                featuredProjects,
                statusDistribution: statusStats.reduce((acc, stat) => {
                    acc[stat._id] = stat.count;
                    return acc;
                }, {}),
                propertyTypeDistribution: propertyTypeStats.reduce((acc, stat) => {
                    acc[stat._id] = stat.count;
                    return acc;
                }, {}),
                topCities: cityStats,
            };
        }
        catch (error) {
            console.error('Error in ProjectsService.getStatistics:', error);
            throw new common_1.BadRequestException('Failed to get project statistics');
        }
    }
    async getFeaturedProjects(limit = 10) {
        try {
            return this.projectModel
                .find({ isFeatured: true, isActive: true })
                .sort({ createdAt: -1 })
                .limit(limit)
                .exec();
        }
        catch (error) {
            console.error('Error in ProjectsService.getFeaturedProjects:', error);
            throw new common_1.BadRequestException('Failed to get featured projects');
        }
    }
};
exports.ProjectsService = ProjectsService;
exports.ProjectsService = ProjectsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(project_schema_1.Project.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        s3_service_1.S3Service])
], ProjectsService);
//# sourceMappingURL=projects.service.js.map