"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const auth_service_1 = require("./auth.service");
const login_dto_1 = require("./dto/login.dto");
const jwt_auth_guard_1 = require("./guards/jwt-auth.guard");
const response_util_1 = require("../../common/utils/response.util");
const api_response_decorator_1 = require("../../common/decorators/api-response.decorator");
let AuthController = class AuthController {
    constructor(authService) {
        this.authService = authService;
    }
    async login(loginDto) {
        const result = await this.authService.login(loginDto);
        return response_util_1.ResponseUtil.success(result, 'Login successful');
    }
    async getProfile(req) {
        const profile = await this.authService.getProfile(req.user.id);
        return response_util_1.ResponseUtil.success(profile, 'Profile retrieved successfully');
    }
    async refreshToken(req) {
        const result = await this.authService.refreshToken(req.user.id);
        return response_util_1.ResponseUtil.success(result, 'Token refreshed successfully');
    }
};
exports.AuthController = AuthController;
__decorate([
    (0, common_1.Post)('login'),
    (0, swagger_1.ApiOperation)({
        summary: '🔑 Admin Login',
        description: `
**Authenticate admin user and get JWT token**

Use one of the predefined admin accounts:
- <EMAIL> / admin123
- <EMAIL> / admin123
- <EMAIL> / admin123

The returned JWT token should be used in the Authorization header for all other API calls.
    `
    }),
    (0, api_response_decorator_1.ApiSuccessResponse)({
        description: 'Login successful - Returns user info and JWT token',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Login successful' },
                data: {
                    type: 'object',
                    properties: {
                        user: {
                            type: 'object',
                            properties: {
                                id: { type: 'string', example: 'admin1' },
                                email: { type: 'string', example: '<EMAIL>' },
                                firstName: { type: 'string', example: 'Admin' },
                                lastName: { type: 'string', example: 'User' },
                                role: { type: 'string', example: 'admin' },
                                isActive: { type: 'boolean', example: true },
                            },
                        },
                        tokens: {
                            type: 'object',
                            properties: {
                                accessToken: {
                                    type: 'string',
                                    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
                                    description: 'JWT token to be used in Authorization header'
                                },
                            },
                        },
                    },
                },
                timestamp: { type: 'string', example: '2024-01-01T00:00:00.000Z' },
            },
        },
    }),
    (0, api_response_decorator_1.ApiBadRequestResponse)('Invalid input data or missing required fields'),
    (0, api_response_decorator_1.ApiUnauthorizedResponse)('Invalid email or password - Check credentials'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [login_dto_1.LoginDto]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "login", null);
__decorate([
    (0, common_1.Get)('profile'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get Admin Profile',
        description: 'Get current authenticated admin profile information'
    }),
    (0, api_response_decorator_1.ApiSuccessResponse)({
        description: 'Profile retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Profile retrieved successfully' },
                data: {
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        email: { type: 'string' },
                        firstName: { type: 'string' },
                        lastName: { type: 'string' },
                        role: { type: 'string' },
                        isActive: { type: 'boolean' },
                    },
                },
                timestamp: { type: 'string' },
            },
        },
    }),
    (0, api_response_decorator_1.ApiUnauthorizedResponse)('Invalid or missing JWT token'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "getProfile", null);
__decorate([
    (0, common_1.Post)('refresh'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({
        summary: 'Refresh Token',
        description: 'Generate new access token for authenticated admin'
    }),
    (0, api_response_decorator_1.ApiSuccessResponse)({
        description: 'Token refreshed successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Token refreshed successfully' },
                data: {
                    type: 'object',
                    properties: {
                        accessToken: { type: 'string' },
                    },
                },
                timestamp: { type: 'string' },
            },
        },
    }),
    (0, api_response_decorator_1.ApiUnauthorizedResponse)('Invalid or missing JWT token'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "refreshToken", null);
exports.AuthController = AuthController = __decorate([
    (0, swagger_1.ApiTags)('🔐 Authentication'),
    (0, common_1.Controller)('auth'),
    __metadata("design:paramtypes", [auth_service_1.AuthService])
], AuthController);
//# sourceMappingURL=auth.controller.js.map