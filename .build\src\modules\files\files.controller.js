"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FilesController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const swagger_1 = require("@nestjs/swagger");
const files_service_1 = require("./files.service");
const upload_file_dto_1 = require("./dto/upload-file.dto");
const query_file_dto_1 = require("./dto/query-file.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const response_util_1 = require("../../common/utils/response.util");
const api_response_decorator_1 = require("../../common/decorators/api-response.decorator");
let FilesController = class FilesController {
    constructor(filesService) {
        this.filesService = filesService;
    }
    async uploadFile(file, uploadFileDto, req) {
        const uploadedFile = await this.filesService.uploadFile(file, uploadFileDto, req.user._id, 'uploads');
        return response_util_1.ResponseUtil.created(uploadedFile, 'File uploaded successfully');
    }
    async uploadMultipleFiles(files, uploadFileDto, req) {
        const uploadedFiles = await this.filesService.uploadMultipleFiles(files, uploadFileDto, req.user._id, 'uploads');
        return response_util_1.ResponseUtil.created(uploadedFiles, 'Files uploaded successfully');
    }
    async findAll(queryDto, req) {
        const result = await this.filesService.findAll(queryDto, req.user._id);
        return response_util_1.ResponseUtil.paginated(result.files, result.pagination.page, result.pagination.limit, result.pagination.total, 'Files retrieved successfully');
    }
    async findOne(id, req) {
        const file = await this.filesService.findById(id, req.user._id);
        return response_util_1.ResponseUtil.success(file, 'File retrieved successfully');
    }
    async downloadFile(id, req) {
        const downloadUrl = await this.filesService.getDownloadUrl(id, req.user._id);
        return response_util_1.ResponseUtil.success({ downloadUrl, expiresIn: 3600 }, 'Download URL generated successfully');
    }
    async remove(id, req) {
        await this.filesService.remove(id, req.user._id);
        return response_util_1.ResponseUtil.successMessage('File deleted successfully');
    }
    async getStatistics(req) {
        const stats = await this.filesService.getStatistics(req.user._id);
        return response_util_1.ResponseUtil.success(stats, 'Statistics retrieved successfully');
    }
};
exports.FilesController = FilesController;
__decorate([
    (0, common_1.Post)('upload'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)('file')),
    (0, swagger_1.ApiConsumes)('multipart/form-data'),
    (0, swagger_1.ApiOperation)({
        summary: 'Upload Single File',
        description: 'Upload a single file to S3 and save metadata',
    }),
    (0, api_response_decorator_1.ApiCreatedResponse)({
        description: 'File uploaded successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'File uploaded successfully' },
                data: {
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        filename: { type: 'string' },
                        originalName: { type: 'string' },
                        mimetype: { type: 'string' },
                        size: { type: 'number' },
                        url: { type: 'string' },
                        key: { type: 'string' },
                        category: { type: 'string' },
                        isPublic: { type: 'boolean' },
                        createdAt: { type: 'string' },
                    },
                },
                timestamp: { type: 'string' },
            },
        },
    }),
    (0, api_response_decorator_1.ApiBadRequestResponse)('Invalid file or upload failed'),
    (0, api_response_decorator_1.ApiUnauthorizedResponse)('Invalid or missing JWT token'),
    __param(0, (0, common_1.UploadedFile)()),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, upload_file_dto_1.UploadFileDto, Object]),
    __metadata("design:returntype", Promise)
], FilesController.prototype, "uploadFile", null);
__decorate([
    (0, common_1.Post)('upload/multiple'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FilesInterceptor)('files', 10)),
    (0, swagger_1.ApiConsumes)('multipart/form-data'),
    (0, swagger_1.ApiOperation)({
        summary: 'Upload Multiple Files',
        description: 'Upload multiple files to S3 and save metadata',
    }),
    (0, api_response_decorator_1.ApiCreatedResponse)({
        description: 'Files uploaded successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Files uploaded successfully' },
                data: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            filename: { type: 'string' },
                            originalName: { type: 'string' },
                            mimetype: { type: 'string' },
                            size: { type: 'number' },
                            url: { type: 'string' },
                            key: { type: 'string' },
                            category: { type: 'string' },
                            isPublic: { type: 'boolean' },
                            createdAt: { type: 'string' },
                        },
                    },
                },
                timestamp: { type: 'string' },
            },
        },
    }),
    (0, api_response_decorator_1.ApiBadRequestResponse)('Invalid files or upload failed'),
    (0, api_response_decorator_1.ApiUnauthorizedResponse)('Invalid or missing JWT token'),
    __param(0, (0, common_1.UploadedFiles)()),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array, upload_file_dto_1.UploadFileDto, Object]),
    __metadata("design:returntype", Promise)
], FilesController.prototype, "uploadMultipleFiles", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get All Files',
        description: 'Retrieve all files with pagination and filtering options',
    }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Page number' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Items per page' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, type: String, description: 'Search term' }),
    (0, swagger_1.ApiQuery)({ name: 'category', required: false, type: String, description: 'Filter by category' }),
    (0, api_response_decorator_1.ApiSuccessResponse)({
        description: 'Files retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Files retrieved successfully' },
                data: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            filename: { type: 'string' },
                            originalName: { type: 'string' },
                            mimetype: { type: 'string' },
                            size: { type: 'number' },
                            url: { type: 'string' },
                            category: { type: 'string' },
                            isPublic: { type: 'boolean' },
                            downloadCount: { type: 'number' },
                            createdAt: { type: 'string' },
                        },
                    },
                },
                pagination: {
                    type: 'object',
                    properties: {
                        page: { type: 'number' },
                        limit: { type: 'number' },
                        total: { type: 'number' },
                        totalPages: { type: 'number' },
                        hasNext: { type: 'boolean' },
                        hasPrev: { type: 'boolean' },
                    },
                },
                timestamp: { type: 'string' },
            },
        },
    }),
    (0, api_response_decorator_1.ApiUnauthorizedResponse)('Invalid or missing JWT token'),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [query_file_dto_1.QueryFileDto, Object]),
    __metadata("design:returntype", Promise)
], FilesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get File by ID',
        description: 'Retrieve a specific file by its ID',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'File ID' }),
    (0, api_response_decorator_1.ApiSuccessResponse)({
        description: 'File retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'File retrieved successfully' },
                data: {
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        filename: { type: 'string' },
                        originalName: { type: 'string' },
                        mimetype: { type: 'string' },
                        size: { type: 'number' },
                        url: { type: 'string' },
                        key: { type: 'string' },
                        category: { type: 'string' },
                        description: { type: 'string' },
                        tags: { type: 'array', items: { type: 'string' } },
                        isPublic: { type: 'boolean' },
                        downloadCount: { type: 'number' },
                        createdAt: { type: 'string' },
                        updatedAt: { type: 'string' },
                    },
                },
                timestamp: { type: 'string' },
            },
        },
    }),
    (0, api_response_decorator_1.ApiNotFoundResponse)('File not found'),
    (0, api_response_decorator_1.ApiUnauthorizedResponse)('Invalid or missing JWT token'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], FilesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)(':id/download'),
    (0, swagger_1.ApiOperation)({
        summary: 'Download File',
        description: 'Get download URL for a file',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'File ID' }),
    (0, api_response_decorator_1.ApiSuccessResponse)({
        description: 'Download URL generated successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Download URL generated successfully' },
                data: {
                    type: 'object',
                    properties: {
                        downloadUrl: { type: 'string', example: 'https://s3.amazonaws.com/bucket/file.jpg?signature=...' },
                        expiresIn: { type: 'number', example: 3600 },
                    },
                },
                timestamp: { type: 'string' },
            },
        },
    }),
    (0, api_response_decorator_1.ApiNotFoundResponse)('File not found'),
    (0, api_response_decorator_1.ApiUnauthorizedResponse)('Invalid or missing JWT token'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], FilesController.prototype, "downloadFile", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Delete File',
        description: 'Delete file from S3 and database',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'File ID' }),
    (0, api_response_decorator_1.ApiSuccessResponse)({
        description: 'File deleted successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'File deleted successfully' },
                timestamp: { type: 'string' },
            },
        },
    }),
    (0, api_response_decorator_1.ApiNotFoundResponse)('File not found'),
    (0, api_response_decorator_1.ApiUnauthorizedResponse)('Invalid or missing JWT token'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], FilesController.prototype, "remove", null);
__decorate([
    (0, common_1.Get)('admin/statistics'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get File Statistics',
        description: 'Get comprehensive file statistics',
    }),
    (0, api_response_decorator_1.ApiSuccessResponse)({
        description: 'Statistics retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Statistics retrieved successfully' },
                data: {
                    type: 'object',
                    properties: {
                        totalFiles: { type: 'number' },
                        activeFiles: { type: 'number' },
                        inactiveFiles: { type: 'number' },
                        publicFiles: { type: 'number' },
                        privateFiles: { type: 'number' },
                        totalSize: { type: 'number' },
                        categoryDistribution: { type: 'object' },
                    },
                },
                timestamp: { type: 'string' },
            },
        },
    }),
    (0, api_response_decorator_1.ApiUnauthorizedResponse)('Invalid or missing JWT token'),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], FilesController.prototype, "getStatistics", null);
exports.FilesController = FilesController = __decorate([
    (0, swagger_1.ApiTags)('📁 Files'),
    (0, common_1.Controller)('files'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [files_service_1.FilesService])
], FilesController);
//# sourceMappingURL=files.controller.js.map