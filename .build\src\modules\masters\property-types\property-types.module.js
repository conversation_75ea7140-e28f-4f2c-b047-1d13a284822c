"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PropertyTypesModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const property_types_controller_1 = require("./property-types.controller");
const property_types_service_1 = require("./property-types.service");
const property_type_schema_1 = require("./schemas/property-type.schema");
let PropertyTypesModule = class PropertyTypesModule {
};
exports.PropertyTypesModule = PropertyTypesModule;
exports.PropertyTypesModule = PropertyTypesModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forFeature([
                { name: property_type_schema_1.PropertyType.name, schema: property_type_schema_1.PropertyTypeSchema }
            ])
        ],
        controllers: [property_types_controller_1.PropertyTypesController],
        providers: [property_types_service_1.PropertyTypesService],
        exports: [property_types_service_1.PropertyTypesService]
    })
], PropertyTypesModule);
//# sourceMappingURL=property-types.module.js.map