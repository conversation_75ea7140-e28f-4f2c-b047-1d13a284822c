import { FloorsService } from './floors.service';
import { CreateFloorDto } from './dto/create-floor.dto';
import { UpdateFloorDto } from './dto/update-floor.dto';
import { QueryMasterWithNumericRangeDto } from '../common/dto/query-master.dto';
export declare class FloorsController {
    private readonly floorsService;
    constructor(floorsService: FloorsService);
    create(createFloorDto: CreateFloorDto): Promise<{
        success: boolean;
        data: import("./schemas/floor.schema").Floor;
        message: string;
    }>;
    findAll(queryDto: QueryMasterWithNumericRangeDto): Promise<import("../common/interfaces/base-master.interface").MasterListResponse<import("./schemas/floor.schema").Floor>>;
    getStatistics(): Promise<{
        success: boolean;
        data: {
            averagePriceMultiplier: any;
            byFloorType: any;
            byUsage: any;
            availability: any;
            floorRange: any;
            totalCount: number;
            activeCount: number;
            inactiveCount: number;
            popularCount: number;
            defaultCount: number;
            byCategory?: Record<string, number>;
            byStatus?: Record<string, number>;
        };
        message: string;
    }>;
    findAvailable(): Promise<{
        success: boolean;
        data: import("./schemas/floor.schema").Floor[];
        message: string;
    }>;
    findByType(type: string): Promise<{
        success: boolean;
        data: import("./schemas/floor.schema").Floor[];
        message: string;
    }>;
    findByUsage(usage: string): Promise<{
        success: boolean;
        data: import("./schemas/floor.schema").Floor[];
        message: string;
    }>;
    findInRange(min: number, max: number): Promise<{
        success: boolean;
        data: import("./schemas/floor.schema").Floor[];
        message: string;
    }>;
    findBasement(): Promise<{
        success: boolean;
        data: import("./schemas/floor.schema").Floor[];
        message: string;
    }>;
    findGround(): Promise<{
        success: boolean;
        data: import("./schemas/floor.schema").Floor;
        message: string;
    }>;
    findUpper(): Promise<{
        success: boolean;
        data: import("./schemas/floor.schema").Floor[];
        message: string;
    }>;
    findPremium(): Promise<{
        success: boolean;
        data: import("./schemas/floor.schema").Floor[];
        message: string;
    }>;
    findOne(id: string): Promise<{
        success: boolean;
        data: import("./schemas/floor.schema").Floor;
        message: string;
    }>;
    update(id: string, updateFloorDto: UpdateFloorDto): Promise<{
        success: boolean;
        data: import("./schemas/floor.schema").Floor;
        message: string;
    }>;
    remove(id: string): Promise<{
        success: boolean;
        message: string;
    }>;
}
