import { FilesService } from './files.service';
import { UploadFileDto } from './dto/upload-file.dto';
import { QueryFileDto } from './dto/query-file.dto';
export declare class FilesController {
    private readonly filesService;
    constructor(filesService: FilesService);
    uploadFile(file: any, uploadFileDto: UploadFileDto, req: any): Promise<import("../../common/interfaces/api-response.interface").ApiResponse<import("./schemas/file.schema").File>>;
    uploadMultipleFiles(files: any[], uploadFileDto: UploadFileDto, req: any): Promise<import("../../common/interfaces/api-response.interface").ApiResponse<import("./schemas/file.schema").File[]>>;
    findAll(queryDto: QueryFileDto, req: any): Promise<import("../../common/interfaces/api-response.interface").PaginatedResponse<import("mongoose").Document<unknown, {}, import("./schemas/file.schema").FileDocument, {}> & import("./schemas/file.schema").File & import("mongoose").Document<unknown, any, any, Record<string, any>> & Required<{
        _id: unknown;
    }> & {
        __v: number;
    }>>;
    findOne(id: string, req: any): Promise<import("../../common/interfaces/api-response.interface").ApiResponse<import("./schemas/file.schema").File>>;
    downloadFile(id: string, req: any): Promise<import("../../common/interfaces/api-response.interface").ApiResponse<{
        downloadUrl: string;
        expiresIn: number;
    }>>;
    remove(id: string, req: any): Promise<import("../../common/interfaces/api-response.interface").ApiResponse<any>>;
    getStatistics(req: any): Promise<import("../../common/interfaces/api-response.interface").ApiResponse<{
        totalFiles: number;
        activeFiles: number;
        inactiveFiles: number;
        publicFiles: number;
        privateFiles: number;
        totalSize: any;
        categoryDistribution: any;
    }>>;
}
