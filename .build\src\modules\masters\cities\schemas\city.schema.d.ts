import { Document } from 'mongoose';
import { MasterWithLocation } from '../../common/schemas/base-master.schema';
import { MasterType } from '../../common/enums/master-types.enum';
export type CityDocument = City & Document;
export declare class City extends MasterWithLocation {
    state?: string;
    country?: string;
    masterType: MasterType.CITY;
    stateCode?: string;
    countryCode?: string;
    population?: number;
    area?: number;
    majorLanguage?: string;
    alternateNames?: string[];
    economicData?: {
        gdp?: number;
        majorIndustries?: string[];
        economicGrowthRate?: number;
    };
    climateData?: {
        averageTemperature?: number;
        rainfall?: number;
        humidity?: number;
        season?: string;
    };
    nearbyAirports?: string[];
    majorRailwayStations?: string[];
    highways?: string[];
    realEstateData?: {
        averagePropertyPrice?: number;
        priceAppreciationRate?: number;
        rentalYield?: number;
        popularAreas?: string[];
        upcomingProjects?: number;
    };
    district?: string;
    division?: string;
    neighboringCities?: string[];
    seoTitle?: string;
    seoDescription?: string;
    seoKeywords?: string[];
    featuredImage?: string;
    gallery?: string[];
    createdAt?: Date;
    updatedAt?: Date;
}
export declare const CitySchema: import("mongoose").Schema<City, import("mongoose").Model<City, any, any, any, Document<unknown, any, City, any> & City & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, City, Document<unknown, {}, import("mongoose").FlatRecord<City>, {}> & import("mongoose").FlatRecord<City> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
