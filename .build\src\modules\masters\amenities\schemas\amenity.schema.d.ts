import { Document } from 'mongoose';
import { MasterWithCategory } from '../../common/schemas/base-master.schema';
import { MasterType, AmenityCategory } from '../../common/enums/master-types.enum';
export type AmenityDocument = Amenity & Document;
export declare class Amenity extends MasterWithCategory {
    masterType: MasterType.AMENITY;
    category: AmenityCategory;
    icon?: string;
    color?: string;
    tags?: string[];
    specifications?: {
        size?: string;
        capacity?: number;
        operatingHours?: string;
        maintenanceFee?: number;
        features?: string[];
    };
    availability?: {
        residential?: boolean;
        commercial?: boolean;
        luxury?: boolean;
        basic?: boolean;
    };
    importanceLevel?: number;
    popularityScore?: number;
    relatedAmenities?: string[];
    keywords?: string[];
    seoTitle?: string;
    seoDescription?: string;
    featuredImage?: string;
    gallery?: string[];
    createdAt?: Date;
    updatedAt?: Date;
}
export declare const AmenitySchema: import("mongoose").Schema<Amenity, import("mongoose").Model<Amenity, any, any, any, Document<unknown, any, Amenity, any> & Amenity & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Amenity, Document<unknown, {}, import("mongoose").FlatRecord<Amenity>, {}> & import("mongoose").FlatRecord<Amenity> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
