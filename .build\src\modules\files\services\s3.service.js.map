{"version": 3, "file": "s3.service.js", "sourceRoot": "", "sources": ["../../../../../src/modules/files/services/s3.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA+F;AAC/F,2CAA+C;AAC/C,+BAA+B;AAC/B,+BAAoC;AAO7B,IAAM,SAAS,GAAf,MAAM,SAAS;IAIpB,YAAoB,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QAE9C,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC;YAChB,WAAW,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,mBAAmB,CAAC;YAChE,eAAe,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,uBAAuB,CAAC;YACxE,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,YAAY,CAAC;SACrD,CAAC,CAAC;QAEH,IAAI,CAAC,EAAE,GAAG,IAAI,GAAG,CAAC,EAAE,EAAE,CAAC;QACvB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,eAAe,CAAC,CAAC;QAElE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACrB,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,UAAU,CACd,IAAyB,EACzB,SAAiB,SAAS,EAC1B,WAAoB,KAAK;QAMzB,IAAI,CAAC;YAEH,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;YACzD,MAAM,QAAQ,GAAG,GAAG,IAAA,SAAM,GAAE,IAAI,aAAa,EAAE,CAAC;YAChD,MAAM,GAAG,GAAG,GAAG,MAAM,IAAI,QAAQ,EAAE,CAAC;YAGpC,MAAM,YAAY,GAA4B;gBAC5C,MAAM,EAAE,IAAI,CAAC,UAAU;gBACvB,GAAG,EAAE,GAAG;gBACR,IAAI,EAAE,IAAI,CAAC,MAAM;gBACjB,WAAW,EAAE,IAAI,CAAC,QAAQ;gBAC1B,kBAAkB,EAAE,QAAQ;gBAC5B,QAAQ,EAAE;oBACR,YAAY,EAAE,IAAI,CAAC,YAAY;oBAC/B,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACrC;aACF,CAAC;YAGF,IAAI,QAAQ,EAAE,CAAC;gBACb,YAAY,CAAC,GAAG,GAAG,aAAa,CAAC;YACnC,CAAC;YAGD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,OAAO,EAAE,CAAC;YAE5D,OAAO;gBACL,GAAG;gBACH,GAAG,EAAE,MAAM,CAAC,QAAQ;gBACpB,MAAM,EAAE,IAAI,CAAC,UAAU;aACxB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;YACzC,MAAM,IAAI,qCAA4B,CAAC,6BAA6B,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,eAAe,CAAC,GAAW,EAAE,YAAoB,IAAI;QACzD,IAAI,CAAC;YACH,MAAM,MAAM,GAAG;gBACb,MAAM,EAAE,IAAI,CAAC,UAAU;gBACvB,GAAG,EAAE,GAAG;gBACR,OAAO,EAAE,SAAS;aACnB,CAAC;YAEF,OAAO,MAAM,IAAI,CAAC,EAAE,CAAC,mBAAmB,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;QAChE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAChD,MAAM,IAAI,qCAA4B,CAAC,iCAAiC,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,UAAU,CAAC,GAAW;QAC1B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG;gBACb,MAAM,EAAE,IAAI,CAAC,UAAU;gBACvB,GAAG,EAAE,GAAG;aACT,CAAC;YAEF,MAAM,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,CAAC;QAC/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;YACzC,MAAM,IAAI,qCAA4B,CAAC,+BAA+B,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,UAAU,CAAC,GAAW;QAC1B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG;gBACb,MAAM,EAAE,IAAI,CAAC,UAAU;gBACvB,GAAG,EAAE,GAAG;aACT,CAAC;YAEF,MAAM,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,CAAC;YAC3C,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;gBAC9B,OAAO,KAAK,CAAC;YACf,CAAC;YACD,MAAM,IAAI,qCAA4B,CAAC,gCAAgC,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,eAAe,CAAC,GAAW;QAC/B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG;gBACb,MAAM,EAAE,IAAI,CAAC,UAAU;gBACvB,GAAG,EAAE,GAAG;aACT,CAAC;YAEF,OAAO,MAAM,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,CAAC;QACpD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;YAC3C,MAAM,IAAI,qCAA4B,CAAC,6BAA6B,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,QAAQ,CAAC,SAAiB,EAAE,cAAsB;QACtD,IAAI,CAAC;YACH,MAAM,MAAM,GAAG;gBACb,MAAM,EAAE,IAAI,CAAC,UAAU;gBACvB,UAAU,EAAE,GAAG,IAAI,CAAC,UAAU,IAAI,SAAS,EAAE;gBAC7C,GAAG,EAAE,cAAc;aACpB,CAAC;YAEF,MAAM,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,CAAC;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;YACvC,MAAM,IAAI,qCAA4B,CAAC,qBAAqB,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,SAAS,CAAC,SAAiB,EAAE,EAAE,UAAkB,IAAI;QACzD,IAAI,CAAC;YACH,MAAM,MAAM,GAAG;gBACb,MAAM,EAAE,IAAI,CAAC,UAAU;gBACvB,MAAM,EAAE,MAAM;gBACd,OAAO,EAAE,OAAO;aACjB,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,CAAC;YAC7D,OAAO,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC;QAC/B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;YACvC,MAAM,IAAI,qCAA4B,CAAC,sBAAsB,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAKD,aAAa;QACX,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;CACF,CAAA;AAzLY,8BAAS;oBAAT,SAAS;IADrB,IAAA,mBAAU,GAAE;qCAKwB,sBAAa;GAJrC,SAAS,CAyLrB"}