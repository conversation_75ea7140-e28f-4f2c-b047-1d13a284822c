"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CombinedMaster = exports.MasterWithLocation = exports.MasterWithNumericValue = exports.MasterWithCategory = exports.MasterWithParent = exports.BaseMaster = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const master_types_enum_1 = require("../enums/master-types.enum");
let BaseMaster = class BaseMaster {
};
exports.BaseMaster = BaseMaster;
__decorate([
    (0, mongoose_1.Prop)({
        required: true,
        trim: true,
        maxlength: 100
    }),
    __metadata("design:type", String)
], BaseMaster.prototype, "name", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 500
    }),
    __metadata("design:type", String)
], BaseMaster.prototype, "description", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 20,
        unique: true,
        sparse: true
    }),
    __metadata("design:type", String)
], BaseMaster.prototype, "code", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: true,
        type: String,
        enum: Object.values(master_types_enum_1.MasterType)
    }),
    __metadata("design:type", String)
], BaseMaster.prototype, "masterType", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: true,
        type: String,
        enum: Object.values(master_types_enum_1.MasterStatus),
        default: master_types_enum_1.MasterStatus.ACTIVE
    }),
    __metadata("design:type", String)
], BaseMaster.prototype, "status", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        min: 0,
        max: 9999,
        default: 0
    }),
    __metadata("design:type", Number)
], BaseMaster.prototype, "sortOrder", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        default: false
    }),
    __metadata("design:type", Boolean)
], BaseMaster.prototype, "isDefault", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        default: false
    }),
    __metadata("design:type", Boolean)
], BaseMaster.prototype, "isPopular", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: Object,
        default: {}
    }),
    __metadata("design:type", Object)
], BaseMaster.prototype, "metadata", void 0);
exports.BaseMaster = BaseMaster = __decorate([
    (0, mongoose_1.Schema)({
        timestamps: true,
        discriminatorKey: 'masterType',
        collection: 'masters'
    })
], BaseMaster);
let MasterWithParent = class MasterWithParent extends BaseMaster {
};
exports.MasterWithParent = MasterWithParent;
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: mongoose_2.Types.ObjectId,
        ref: 'BaseMaster'
    }),
    __metadata("design:type", mongoose_2.Types.ObjectId)
], MasterWithParent.prototype, "parentId", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: String,
        enum: Object.values(master_types_enum_1.MasterType)
    }),
    __metadata("design:type", String)
], MasterWithParent.prototype, "parentType", void 0);
exports.MasterWithParent = MasterWithParent = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], MasterWithParent);
let MasterWithCategory = class MasterWithCategory extends BaseMaster {
};
exports.MasterWithCategory = MasterWithCategory;
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 50
    }),
    __metadata("design:type", String)
], MasterWithCategory.prototype, "category", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 50
    }),
    __metadata("design:type", String)
], MasterWithCategory.prototype, "icon", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 10
    }),
    __metadata("design:type", String)
], MasterWithCategory.prototype, "color", void 0);
exports.MasterWithCategory = MasterWithCategory = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], MasterWithCategory);
let MasterWithNumericValue = class MasterWithNumericValue extends BaseMaster {
};
exports.MasterWithNumericValue = MasterWithNumericValue;
__decorate([
    (0, mongoose_1.Prop)({
        required: false
    }),
    __metadata("design:type", Number)
], MasterWithNumericValue.prototype, "numericValue", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 20
    }),
    __metadata("design:type", String)
], MasterWithNumericValue.prototype, "unit", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false
    }),
    __metadata("design:type", Number)
], MasterWithNumericValue.prototype, "minValue", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false
    }),
    __metadata("design:type", Number)
], MasterWithNumericValue.prototype, "maxValue", void 0);
exports.MasterWithNumericValue = MasterWithNumericValue = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], MasterWithNumericValue);
let MasterWithLocation = class MasterWithLocation extends BaseMaster {
};
exports.MasterWithLocation = MasterWithLocation;
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 100
    }),
    __metadata("design:type", String)
], MasterWithLocation.prototype, "state", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 100
    }),
    __metadata("design:type", String)
], MasterWithLocation.prototype, "country", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: [Number],
        validate: [arrayLimit, 'Coordinates must have exactly 2 elements']
    }),
    __metadata("design:type", Array)
], MasterWithLocation.prototype, "coordinates", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 50
    }),
    __metadata("design:type", String)
], MasterWithLocation.prototype, "timezone", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: [String],
        default: []
    }),
    __metadata("design:type", Array)
], MasterWithLocation.prototype, "pinCodes", void 0);
exports.MasterWithLocation = MasterWithLocation = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], MasterWithLocation);
function arrayLimit(val) {
    return val.length === 2;
}
let CombinedMaster = class CombinedMaster extends BaseMaster {
};
exports.CombinedMaster = CombinedMaster;
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: mongoose_2.Types.ObjectId,
        ref: 'CombinedMaster'
    }),
    __metadata("design:type", mongoose_2.Types.ObjectId)
], CombinedMaster.prototype, "parentId", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: String,
        enum: Object.values(master_types_enum_1.MasterType)
    }),
    __metadata("design:type", String)
], CombinedMaster.prototype, "parentType", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 50
    }),
    __metadata("design:type", String)
], CombinedMaster.prototype, "category", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 50
    }),
    __metadata("design:type", String)
], CombinedMaster.prototype, "icon", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 10
    }),
    __metadata("design:type", String)
], CombinedMaster.prototype, "color", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false
    }),
    __metadata("design:type", Number)
], CombinedMaster.prototype, "numericValue", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 20
    }),
    __metadata("design:type", String)
], CombinedMaster.prototype, "unit", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false
    }),
    __metadata("design:type", Number)
], CombinedMaster.prototype, "minValue", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false
    }),
    __metadata("design:type", Number)
], CombinedMaster.prototype, "maxValue", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 100
    }),
    __metadata("design:type", String)
], CombinedMaster.prototype, "state", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 100
    }),
    __metadata("design:type", String)
], CombinedMaster.prototype, "country", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: [Number],
        validate: [arrayLimit, 'Coordinates must have exactly 2 elements']
    }),
    __metadata("design:type", Array)
], CombinedMaster.prototype, "coordinates", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 50
    }),
    __metadata("design:type", String)
], CombinedMaster.prototype, "timezone", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: [String],
        default: []
    }),
    __metadata("design:type", Array)
], CombinedMaster.prototype, "pinCodes", void 0);
exports.CombinedMaster = CombinedMaster = __decorate([
    (0, mongoose_1.Schema)({
        timestamps: true,
        discriminatorKey: 'masterType',
        collection: 'masters'
    })
], CombinedMaster);
//# sourceMappingURL=base-master.schema.js.map