#!/bin/bash

# Docker Manager Script for TrelaX Admin Backend
# This script provides easy commands to manage Docker containers

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Project name
PROJECT_NAME="trelax-admin-backend"

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

# Function to check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to check if Docker Compose is available
check_docker_compose() {
    if ! command -v docker-compose > /dev/null 2>&1; then
        print_error "Docker Compose is not installed. Please install Docker Compose and try again."
        exit 1
    fi
}

# Function to create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    mkdir -p logs/nginx
    mkdir -p uploads
    mkdir -p coverage
    mkdir -p test-results
    mkdir -p performance-results
    chmod 755 logs uploads coverage test-results performance-results
}

# Function to start production environment
start_production() {
    print_header "Starting Production Environment"
    check_docker
    check_docker_compose
    create_directories
    
    print_status "Building and starting production containers..."
    docker-compose -f docker-compose.yml up -d --build
    
    print_status "Waiting for services to be healthy..."
    sleep 30
    
    print_status "Checking service status..."
    docker-compose -f docker-compose.yml ps
    
    print_status "Production environment started successfully!"
    print_status "API: http://localhost:3000"
    print_status "MongoDB Express: http://localhost:8081"
    print_status "Redis Commander: http://localhost:8082"
}

# Function to start development environment
start_development() {
    print_header "Starting Development Environment"
    check_docker
    check_docker_compose
    create_directories
    
    print_status "Building and starting development containers..."
    docker-compose -f docker-compose.dev.yml up -d --build
    
    print_status "Waiting for services to be healthy..."
    sleep 30
    
    print_status "Checking service status..."
    docker-compose -f docker-compose.dev.yml ps
    
    print_status "Development environment started successfully!"
    print_status "API: http://localhost:3000 (with hot reload)"
    print_status "Debug Port: 9229"
    print_status "MongoDB Express: http://localhost:8083"
    print_status "Redis Commander: http://localhost:8084"
}

# Function to run tests
run_tests() {
    print_header "Running Tests"
    check_docker
    check_docker_compose
    
    print_status "Starting test environment..."
    docker-compose -f docker-compose.test.yml up -d mongodb-test redis-test localstack-test
    
    print_status "Waiting for test services to be ready..."
    sleep 20
    
    print_status "Running E2E tests..."
    docker-compose -f docker-compose.test.yml --profile testing run --rm e2e-tests
    
    print_status "Running unit tests..."
    docker-compose -f docker-compose.test.yml --profile testing run --rm unit-tests
    
    print_status "Cleaning up test environment..."
    docker-compose -f docker-compose.test.yml down -v
    
    print_status "Tests completed successfully!"
}

# Function to run performance tests
run_performance_tests() {
    print_header "Running Performance Tests"
    check_docker
    check_docker_compose
    
    print_status "Starting test environment..."
    docker-compose -f docker-compose.test.yml up -d mongodb-test
    
    print_status "Waiting for test services to be ready..."
    sleep 20
    
    print_status "Running performance tests..."
    docker-compose -f docker-compose.test.yml --profile performance run --rm performance-tests
    
    print_status "Cleaning up test environment..."
    docker-compose -f docker-compose.test.yml down -v
    
    print_status "Performance tests completed!"
}

# Function to generate test coverage
generate_coverage() {
    print_header "Generating Test Coverage"
    check_docker
    check_docker_compose
    
    print_status "Starting test environment..."
    docker-compose -f docker-compose.test.yml up -d mongodb-test redis-test localstack-test
    
    print_status "Waiting for test services to be ready..."
    sleep 20
    
    print_status "Generating coverage report..."
    docker-compose -f docker-compose.test.yml --profile testing run --rm test-coverage
    
    print_status "Cleaning up test environment..."
    docker-compose -f docker-compose.test.yml down -v
    
    print_status "Coverage report generated in ./coverage directory"
}

# Function to stop all environments
stop_all() {
    print_header "Stopping All Environments"
    
    print_status "Stopping production environment..."
    docker-compose -f docker-compose.yml down 2>/dev/null || true
    
    print_status "Stopping development environment..."
    docker-compose -f docker-compose.dev.yml down 2>/dev/null || true
    
    print_status "Stopping test environment..."
    docker-compose -f docker-compose.test.yml down -v 2>/dev/null || true
    
    print_status "All environments stopped successfully!"
}

# Function to clean up Docker resources
cleanup() {
    print_header "Cleaning Up Docker Resources"
    
    print_warning "This will remove all containers, volumes, and images related to this project."
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Stopping all containers..."
        stop_all
        
        print_status "Removing containers..."
        docker container prune -f
        
        print_status "Removing volumes..."
        docker volume prune -f
        
        print_status "Removing images..."
        docker image prune -a -f
        
        print_status "Cleanup completed successfully!"
    else
        print_status "Cleanup cancelled."
    fi
}

# Function to show logs
show_logs() {
    local service=$2
    local environment=${3:-production}
    
    print_header "Showing Logs"
    
    case $environment in
        "production")
            if [ -n "$service" ]; then
                docker-compose -f docker-compose.yml logs -f "$service"
            else
                docker-compose -f docker-compose.yml logs -f
            fi
            ;;
        "development")
            if [ -n "$service" ]; then
                docker-compose -f docker-compose.dev.yml logs -f "$service"
            else
                docker-compose -f docker-compose.dev.yml logs -f
            fi
            ;;
        *)
            print_error "Invalid environment. Use 'production' or 'development'"
            exit 1
            ;;
    esac
}

# Function to show status
show_status() {
    print_header "Service Status"
    
    print_status "Production Environment:"
    docker-compose -f docker-compose.yml ps 2>/dev/null || echo "Not running"
    
    echo
    print_status "Development Environment:"
    docker-compose -f docker-compose.dev.yml ps 2>/dev/null || echo "Not running"
    
    echo
    print_status "Docker System Info:"
    docker system df
}

# Function to show help
show_help() {
    echo "TrelaX Admin Backend Docker Manager"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  start-prod              Start production environment"
    echo "  start-dev               Start development environment"
    echo "  stop                    Stop all environments"
    echo "  restart-prod            Restart production environment"
    echo "  restart-dev             Restart development environment"
    echo "  test                    Run all tests"
    echo "  test-performance        Run performance tests"
    echo "  coverage                Generate test coverage report"
    echo "  logs [service] [env]    Show logs (env: production|development)"
    echo "  status                  Show service status"
    echo "  cleanup                 Clean up Docker resources"
    echo "  help                    Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start-dev            Start development environment"
    echo "  $0 logs app production  Show production app logs"
    echo "  $0 test                 Run all tests"
    echo "  $0 cleanup              Clean up all Docker resources"
}

# Main script logic
case $1 in
    "start-prod")
        start_production
        ;;
    "start-dev")
        start_development
        ;;
    "stop")
        stop_all
        ;;
    "restart-prod")
        stop_all
        start_production
        ;;
    "restart-dev")
        stop_all
        start_development
        ;;
    "test")
        run_tests
        ;;
    "test-performance")
        run_performance_tests
        ;;
    "coverage")
        generate_coverage
        ;;
    "logs")
        show_logs "$@"
        ;;
    "status")
        show_status
        ;;
    "cleanup")
        cleanup
        ;;
    "help"|"--help"|"-h")
        show_help
        ;;
    *)
        print_error "Invalid command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
