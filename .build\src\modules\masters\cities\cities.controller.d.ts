import { CitiesService } from './cities.service';
import { CreateCityDto } from './dto/create-city.dto';
import { UpdateCityDto } from './dto/update-city.dto';
import { QueryCityDto } from './dto/query-city.dto';
export declare class CitiesController {
    private readonly citiesService;
    constructor(citiesService: CitiesService);
    create(createCityDto: CreateCityDto): Promise<{
        success: boolean;
        data: import("./schemas/city.schema").City;
        message: string;
    }>;
    findAll(queryDto: QueryCityDto): Promise<import("../common/interfaces/base-master.interface").MasterListResponse<import("./schemas/city.schema").City>>;
    getStatistics(): Promise<{
        success: boolean;
        data: {
            popularCitiesCount: number;
            citiesWithRealEstateData: number;
            averagePropertyPrice: any;
            byState: any;
            byCountry: any;
            totalCount: number;
            activeCount: number;
            inactiveCount: number;
            popularCount: number;
            defaultCount: number;
            byCategory?: Record<string, number>;
            byStatus?: Record<string, number>;
        };
        message: string;
    }>;
    findPopular(limit?: number): Promise<{
        success: boolean;
        data: import("./schemas/city.schema").City[];
        message: string;
    }>;
    findByState(state: string): Promise<{
        success: boolean;
        data: import("./schemas/city.schema").City[];
        message: string;
    }>;
    findByCountry(country: string): Promise<{
        success: boolean;
        data: import("./schemas/city.schema").City[];
        message: string;
    }>;
    findNearLocation(longitude: number, latitude: number, maxDistance?: number): Promise<{
        success: boolean;
        data: import("./schemas/city.schema").City[];
        message: string;
    }>;
    findOne(id: string): Promise<{
        success: boolean;
        data: import("./schemas/city.schema").City;
        message: string;
    }>;
    update(id: string, updateCityDto: UpdateCityDto): Promise<{
        success: boolean;
        data: import("./schemas/city.schema").City;
        message: string;
    }>;
    remove(id: string): Promise<{
        success: boolean;
        message: string;
    }>;
}
