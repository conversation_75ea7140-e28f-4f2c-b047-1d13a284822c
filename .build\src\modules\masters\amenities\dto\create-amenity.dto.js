"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateAmenityDto = exports.AvailabilityDto = exports.SpecificationsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const base_master_dto_1 = require("../../common/dto/base-master.dto");
const master_types_enum_1 = require("../../common/enums/master-types.enum");
class SpecificationsDto {
}
exports.SpecificationsDto = SpecificationsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Size specification',
        example: 'Olympic size (50m x 25m)'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], SpecificationsDto.prototype, "size", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Capacity in number of people',
        example: 50
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], SpecificationsDto.prototype, "capacity", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Operating hours',
        example: '6 AM - 10 PM'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(50),
    __metadata("design:type", String)
], SpecificationsDto.prototype, "operatingHours", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Monthly maintenance fee',
        example: 500
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], SpecificationsDto.prototype, "maintenanceFee", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Additional features',
        example: ['Air Conditioned', 'Modern Equipment', 'Trainer Available']
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], SpecificationsDto.prototype, "features", void 0);
class AvailabilityDto {
}
exports.AvailabilityDto = AvailabilityDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Available in residential projects',
        example: true
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], AvailabilityDto.prototype, "residential", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Available in commercial projects',
        example: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], AvailabilityDto.prototype, "commercial", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Premium/luxury amenity',
        example: true
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], AvailabilityDto.prototype, "luxury", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Basic amenity',
        example: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], AvailabilityDto.prototype, "basic", void 0);
class CreateAmenityDto extends base_master_dto_1.MasterWithCategoryDto {
    constructor() {
        super(...arguments);
        this.masterType = master_types_enum_1.MasterType.AMENITY;
    }
}
exports.CreateAmenityDto = CreateAmenityDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Amenity name',
        example: 'Swimming Pool',
        maxLength: 100
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CreateAmenityDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Amenity description',
        example: 'Olympic size swimming pool with modern filtration system',
        maxLength: 500
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(500),
    __metadata("design:type", String)
], CreateAmenityDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Unique amenity code',
        example: 'POOL',
        maxLength: 20
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(20),
    __metadata("design:type", String)
], CreateAmenityDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Amenity category',
        enum: master_types_enum_1.AmenityCategory,
        example: master_types_enum_1.AmenityCategory.RECREATIONAL
    }),
    (0, class_validator_1.IsEnum)(master_types_enum_1.AmenityCategory),
    __metadata("design:type", String)
], CreateAmenityDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Icon identifier',
        example: 'swimming-pool',
        maxLength: 50
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(50),
    __metadata("design:type", String)
], CreateAmenityDto.prototype, "icon", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Color code for UI display',
        example: '#0066CC',
        maxLength: 10
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(10),
    __metadata("design:type", String)
], CreateAmenityDto.prototype, "color", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Additional tags for filtering',
        example: ['water', 'sports', 'fitness', 'recreation']
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateAmenityDto.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Amenity specifications',
        type: SpecificationsDto
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => SpecificationsDto),
    __metadata("design:type", SpecificationsDto)
], CreateAmenityDto.prototype, "specifications", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Availability in different project types',
        type: AvailabilityDto
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => AvailabilityDto),
    __metadata("design:type", AvailabilityDto)
], CreateAmenityDto.prototype, "availability", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Importance level (1-5 scale)',
        example: 4,
        minimum: 1,
        maximum: 5
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(5),
    __metadata("design:type", Number)
], CreateAmenityDto.prototype, "importanceLevel", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Popularity score (0-100)',
        example: 85,
        minimum: 0,
        maximum: 100
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], CreateAmenityDto.prototype, "popularityScore", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Related amenity names',
        example: ['Gym', 'Spa', 'Jacuzzi']
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateAmenityDto.prototype, "relatedAmenities", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search keywords',
        example: ['pool', 'swimming', 'water', 'recreation']
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateAmenityDto.prototype, "keywords", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'SEO title',
        example: 'Swimming Pool - Premium Amenity for Residential Projects',
        maxLength: 200
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(200),
    __metadata("design:type", String)
], CreateAmenityDto.prototype, "seoTitle", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'SEO description',
        example: 'Olympic size swimming pool amenity perfect for luxury residential and commercial projects.',
        maxLength: 500
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(500),
    __metadata("design:type", String)
], CreateAmenityDto.prototype, "seoDescription", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Featured image URL',
        example: 'https://s3.amazonaws.com/bucket/swimming-pool.jpg'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreateAmenityDto.prototype, "featuredImage", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Gallery image URLs',
        example: ['https://s3.amazonaws.com/bucket/pool-1.jpg', 'https://s3.amazonaws.com/bucket/pool-2.jpg']
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsUrl)({}, { each: true }),
    __metadata("design:type", Array)
], CreateAmenityDto.prototype, "gallery", void 0);
//# sourceMappingURL=create-amenity.dto.js.map