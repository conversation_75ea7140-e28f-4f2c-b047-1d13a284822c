"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoomSchema = exports.Room = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const base_master_schema_1 = require("../../common/schemas/base-master.schema");
const master_types_enum_1 = require("../../common/enums/master-types.enum");
let Room = class Room extends base_master_schema_1.MasterWithNumericValue {
};
exports.Room = Room;
__decorate([
    (0, mongoose_1.Prop)({
        required: true,
        type: String,
        enum: Object.values(master_types_enum_1.MasterType),
        default: master_types_enum_1.MasterType.ROOM,
        immutable: true
    }),
    __metadata("design:type", String)
], Room.prototype, "masterType", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        index: true
    }),
    __metadata("design:type", Number)
], Room.prototype, "numericValue", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 20,
        default: 'BHK'
    }),
    __metadata("design:type", String)
], Room.prototype, "unit", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 50,
        enum: ['studio', '1bhk', '2bhk', '3bhk', '4bhk', '5bhk', 'penthouse']
    }),
    __metadata("design:type", String)
], Room.prototype, "roomType", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: [String],
        default: []
    }),
    __metadata("design:type", Array)
], Room.prototype, "features", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false
    }),
    __metadata("design:type", Number)
], Room.prototype, "typicalArea", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        min: 1,
        max: 5
    }),
    __metadata("design:type", Number)
], Room.prototype, "popularityRating", void 0);
exports.Room = Room = __decorate([
    (0, mongoose_1.Schema)({
        timestamps: true,
        collection: 'masters',
        discriminatorKey: 'masterType'
    })
], Room);
exports.RoomSchema = mongoose_1.SchemaFactory.createForClass(Room);
exports.RoomSchema.index({ numericValue: 1 }, { unique: true });
exports.RoomSchema.index({ code: 1 }, { unique: true, sparse: true });
exports.RoomSchema.index({ roomType: 1, status: 1 });
exports.RoomSchema.pre('save', function (next) {
    if (this.isNew) {
        this.masterType = master_types_enum_1.MasterType.ROOM;
    }
    next();
});
exports.RoomSchema.virtual('displayName').get(function () {
    return this.numericValue
        ? `${this.numericValue} ${this.unit || 'BHK'}`
        : this.name;
});
exports.RoomSchema.set('toJSON', { virtuals: true });
exports.RoomSchema.set('toObject', { virtuals: true });
//# sourceMappingURL=room.schema.js.map