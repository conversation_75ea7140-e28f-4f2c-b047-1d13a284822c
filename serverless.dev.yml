# Development stage configuration
# Usage: serverless deploy --stage dev --config serverless.dev.yml

service: trelax-admin-backend

frameworkVersion: '3'

provider:
  name: aws
  runtime: nodejs18.x
  region: ap-south-1
  stage: dev
  memorySize: 512
  timeout: 30
  logRetentionInDays: 7
  
  environment:
    NODE_ENV: development
    STAGE: dev
    REGION: ap-south-1
    # Development database
    MONGO_URI: ${env:MONGO_URI_DEV, 'mongodb+srv://sj317772:<EMAIL>/trelax_dev_db?retryWrites=true&w=majority&appName=Cluster0'}
    # Development JWT
    JWT_SECRET: ${env:JWT_SECRET_DEV, 'dev_jwt_secret_key_2024'}
    JWT_EXPIRES_IN: 24h
    # Development S3
    AWS_S3_BUCKET: trelax-admin-uploads-dev
    # Development CORS
    CORS_ORIGIN: 'http://localhost:3000,http://localhost:3001'
    # Enable debug logging
    DEBUG: 'true'
    LOG_LEVEL: debug

  iam:
    role:
      statements:
        - Effect: Allow
          Action:
            - s3:*
          Resource:
            - arn:aws:s3:::trelax-admin-uploads-dev
            - arn:aws:s3:::trelax-admin-uploads-dev/*
        - Effect: Allow
          Action:
            - logs:*
          Resource: arn:aws:logs:ap-south-1:*:*

plugins:
  - serverless-plugin-typescript
  - serverless-offline

custom:
  serverless-offline:
    httpPort: 3000
    host: 0.0.0.0
    stage: dev
    prefix: api/v1
    printOutput: true
    noAuth: true
    corsAllowOrigin: '*'
    corsAllowHeaders: 'accept,content-type,x-api-key,authorization'

functions:
  main:
    handler: src/lambda.handler
    name: trelax-admin-backend-dev-main
    memorySize: 512
    timeout: 30
    events:
      - http:
          path: /{proxy+}
          method: ANY
          cors: true
      - http:
          path: /
          method: ANY
          cors: true

  health:
    handler: src/lambda.healthCheck
    name: trelax-admin-backend-dev-health
    memorySize: 128
    timeout: 10
    events:
      - http:
          path: /health
          method: GET
          cors: true

resources:
  Resources:
    S3BucketUploads:
      Type: AWS::S3::Bucket
      Properties:
        BucketName: trelax-admin-uploads-dev
        PublicAccessBlockConfiguration:
          BlockPublicAcls: false
          BlockPublicPolicy: false
          IgnorePublicAcls: false
          RestrictPublicBuckets: false
        CorsConfiguration:
          CorsRules:
            - AllowedHeaders: ['*']
              AllowedMethods: [GET, PUT, POST, DELETE, HEAD]
              AllowedOrigins: ['*']
              ExposedHeaders: [ETag]
              MaxAge: 3000
