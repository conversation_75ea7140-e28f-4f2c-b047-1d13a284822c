import { AppService } from './app.service';
export declare class AppController {
    private readonly appService;
    constructor(appService: AppService);
    getHello(): {
        message: string;
        timestamp: string;
        version: string;
        environment: any;
    };
    getInfo(): {
        name: string;
        version: string;
        description: string;
        endpoints: {
            auth: string;
            projects: string;
            masters: string;
            builders: string;
            agents: string;
            files: string;
            docs: string;
        };
        modules: {
            authentication: string;
            projects: string;
            masters: string;
            builders: string;
            agents: string;
            files: string;
        };
        features: string[];
    };
}
