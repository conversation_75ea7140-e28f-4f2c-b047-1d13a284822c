"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocationsService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const location_schema_1 = require("./schemas/location.schema");
const base_master_service_1 = require("../common/services/base-master.service");
const master_types_enum_1 = require("../common/enums/master-types.enum");
let LocationsService = class LocationsService extends base_master_service_1.BaseMasterService {
    constructor(locationModel) {
        super(locationModel, master_types_enum_1.MasterType.LOCATION);
        this.locationModel = locationModel;
    }
    async createLocation(createLocationDto) {
        const parentCityId = createLocationDto.parentId || createLocationDto.cityId;
        await this.validateParentCity(parentCityId);
        const locationData = {
            ...createLocationDto,
            parentId: new mongoose_2.Types.ObjectId(parentCityId),
            parentType: master_types_enum_1.MasterType.CITY,
            masterType: master_types_enum_1.MasterType.LOCATION
        };
        delete locationData.cityId;
        return await this.create(locationData);
    }
    async findAllLocations(queryDto = {}) {
        try {
            const { page = 1, limit = 10, search, status, isDefault, isPopular, sortBy = 'sortOrder', sortOrder = 'asc', parentId, cityId, locationType, locationCategory, pincode, minPropertyPrice, maxPropertyPrice, minAppreciationRate, maxAppreciationRate, minRentalYield, maxRentalYield, nearbyMetroStation, maxMetroDistance, nearbyRailwayStation, maxRailwayDistance, maxAirportDistance, propertyType, hasConnectivity, hasRealEstateData, hasNearbyFacilities, hasFeaturedImage, hasGallery } = queryDto;
            const filter = {
                masterType: master_types_enum_1.MasterType.LOCATION,
                status: { $ne: 'archived' }
            };
            if (search) {
                filter.$or = [
                    { name: { $regex: search, $options: 'i' } },
                    { description: { $regex: search, $options: 'i' } },
                    { code: { $regex: search, $options: 'i' } },
                    { address: { $regex: search, $options: 'i' } },
                    { landmark: { $regex: search, $options: 'i' } },
                    { alternateNames: { $regex: search, $options: 'i' } }
                ];
            }
            if (status)
                filter.status = status;
            if (typeof isDefault === 'boolean')
                filter.isDefault = isDefault;
            if (typeof isPopular === 'boolean')
                filter.isPopular = isPopular;
            if (parentId || cityId) {
                filter.parentId = new mongoose_2.Types.ObjectId(parentId || cityId);
            }
            if (locationType)
                filter.locationType = locationType;
            if (locationCategory)
                filter.locationCategory = locationCategory;
            if (pincode)
                filter.pincode = pincode;
            if (minPropertyPrice !== undefined || maxPropertyPrice !== undefined) {
                filter['realEstateData.averagePropertyPrice'] = {};
                if (minPropertyPrice !== undefined) {
                    filter['realEstateData.averagePropertyPrice'].$gte = minPropertyPrice;
                }
                if (maxPropertyPrice !== undefined) {
                    filter['realEstateData.averagePropertyPrice'].$lte = maxPropertyPrice;
                }
            }
            if (minAppreciationRate !== undefined || maxAppreciationRate !== undefined) {
                filter['realEstateData.priceAppreciationRate'] = {};
                if (minAppreciationRate !== undefined) {
                    filter['realEstateData.priceAppreciationRate'].$gte = minAppreciationRate;
                }
                if (maxAppreciationRate !== undefined) {
                    filter['realEstateData.priceAppreciationRate'].$lte = maxAppreciationRate;
                }
            }
            if (minRentalYield !== undefined || maxRentalYield !== undefined) {
                filter['realEstateData.rentalYield'] = {};
                if (minRentalYield !== undefined) {
                    filter['realEstateData.rentalYield'].$gte = minRentalYield;
                }
                if (maxRentalYield !== undefined) {
                    filter['realEstateData.rentalYield'].$lte = maxRentalYield;
                }
            }
            if (nearbyMetroStation) {
                filter['connectivity.nearestMetroStation'] = { $regex: nearbyMetroStation, $options: 'i' };
            }
            if (maxMetroDistance !== undefined) {
                filter['connectivity.metroDistance'] = { $lte: maxMetroDistance };
            }
            if (nearbyRailwayStation) {
                filter['connectivity.nearestRailwayStation'] = { $regex: nearbyRailwayStation, $options: 'i' };
            }
            if (maxRailwayDistance !== undefined) {
                filter['connectivity.railwayDistance'] = { $lte: maxRailwayDistance };
            }
            if (maxAirportDistance !== undefined) {
                filter['connectivity.airportDistance'] = { $lte: maxAirportDistance };
            }
            if (propertyType) {
                filter['realEstateData.popularPropertyTypes'] = { $in: [propertyType] };
            }
            if (hasConnectivity) {
                filter.connectivity = { $exists: true, $ne: null };
            }
            if (hasRealEstateData) {
                filter.realEstateData = { $exists: true, $ne: null };
            }
            if (hasNearbyFacilities) {
                filter.nearbyFacilities = { $exists: true, $ne: null };
            }
            if (hasFeaturedImage) {
                filter.featuredImage = { $exists: true, $nin: [null, ''] };
            }
            if (hasGallery) {
                filter.gallery = { $exists: true, $not: { $size: 0 } };
            }
            const sort = {};
            sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
            const skip = (page - 1) * limit;
            const [data, total] = await Promise.all([
                this.locationModel
                    .find(filter)
                    .populate('parentId', 'name masterType state country')
                    .sort(sort)
                    .skip(skip)
                    .limit(limit)
                    .exec(),
                this.locationModel.countDocuments(filter)
            ]);
            const totalPages = Math.ceil(total / limit);
            return {
                success: true,
                data: data,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages
                }
            };
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch locations: ${error.message}`);
        }
    }
    async findLocationById(id) {
        try {
            const location = await this.locationModel
                .findOne({
                _id: id,
                masterType: master_types_enum_1.MasterType.LOCATION,
                status: { $ne: 'archived' }
            })
                .populate('parentId', 'name masterType state country')
                .exec();
            if (!location) {
                throw new common_1.NotFoundException(`Location with ID ${id} not found`);
            }
            return location;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.BadRequestException(`Invalid location ID: ${error.message}`);
        }
    }
    async updateLocation(id, updateLocationDto) {
        const parentId = updateLocationDto.parentId || updateLocationDto.cityId;
        if (parentId) {
            await this.validateParentCity(parentId);
        }
        const updateData = { ...updateLocationDto };
        if (parentId) {
            updateData.parentId = new mongoose_2.Types.ObjectId(parentId);
            updateData.parentType = master_types_enum_1.MasterType.CITY;
        }
        delete updateData.cityId;
        return await this.update(id, updateData);
    }
    async removeLocation(id) {
        return await this.remove(id);
    }
    async findLocationsByCity(cityId) {
        try {
            return await this.locationModel
                .find({
                parentId: new mongoose_2.Types.ObjectId(cityId),
                masterType: master_types_enum_1.MasterType.LOCATION,
                status: { $ne: 'archived' }
            })
                .sort({ sortOrder: 1, name: 1 })
                .exec();
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch locations by city: ${error.message}`);
        }
    }
    async findPopularLocations(limit = 10) {
        try {
            return await this.locationModel
                .find({
                isPopular: true,
                masterType: master_types_enum_1.MasterType.LOCATION,
                status: 'active'
            })
                .populate('parentId', 'name masterType')
                .sort({ sortOrder: 1, name: 1 })
                .limit(limit)
                .exec();
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch popular locations: ${error.message}`);
        }
    }
    async findLocationsByType(locationType) {
        try {
            return await this.locationModel
                .find({
                locationType,
                masterType: master_types_enum_1.MasterType.LOCATION,
                status: { $ne: 'archived' }
            })
                .populate('parentId', 'name masterType')
                .sort({ sortOrder: 1, name: 1 })
                .exec();
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch locations by type: ${error.message}`);
        }
    }
    async findLocationsNearLocation(longitude, latitude, maxDistance = 50000) {
        try {
            return await this.locationModel
                .find({
                coordinates: {
                    $near: {
                        $geometry: {
                            type: 'Point',
                            coordinates: [longitude, latitude]
                        },
                        $maxDistance: maxDistance
                    }
                },
                masterType: master_types_enum_1.MasterType.LOCATION,
                status: { $ne: 'archived' }
            })
                .populate('parentId', 'name masterType')
                .exec();
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to find locations near location: ${error.message}`);
        }
    }
    async getLocationStatistics() {
        try {
            const baseStats = await this.getStatistics();
            const [locationTypeStats, locationCategoryStats, cityStats, locationsWithRealEstateData, averagePropertyPrice] = await Promise.all([
                this.locationModel.aggregate([
                    {
                        $match: {
                            masterType: master_types_enum_1.MasterType.LOCATION,
                            status: { $ne: 'archived' },
                            locationType: { $exists: true, $ne: null }
                        }
                    },
                    { $group: { _id: '$locationType', count: { $sum: 1 } } },
                    { $sort: { count: -1 } }
                ]),
                this.locationModel.aggregate([
                    {
                        $match: {
                            masterType: master_types_enum_1.MasterType.LOCATION,
                            status: { $ne: 'archived' },
                            locationCategory: { $exists: true, $ne: null }
                        }
                    },
                    { $group: { _id: '$locationCategory', count: { $sum: 1 } } },
                    { $sort: { count: -1 } }
                ]),
                this.locationModel.aggregate([
                    {
                        $match: {
                            masterType: master_types_enum_1.MasterType.LOCATION,
                            status: { $ne: 'archived' }
                        }
                    },
                    { $group: { _id: '$parentId', count: { $sum: 1 } } },
                    { $sort: { count: -1 } },
                    { $limit: 10 }
                ]),
                this.locationModel.countDocuments({
                    masterType: master_types_enum_1.MasterType.LOCATION,
                    'realEstateData.averagePropertyPrice': { $exists: true, $ne: null },
                    status: { $ne: 'archived' }
                }),
                this.locationModel.aggregate([
                    {
                        $match: {
                            masterType: master_types_enum_1.MasterType.LOCATION,
                            'realEstateData.averagePropertyPrice': { $exists: true, $ne: null },
                            status: { $ne: 'archived' }
                        }
                    },
                    {
                        $group: {
                            _id: null,
                            avgPrice: { $avg: '$realEstateData.averagePropertyPrice' }
                        }
                    }
                ])
            ]);
            const byLocationType = locationTypeStats.reduce((acc, item) => {
                acc[item._id] = item.count;
                return acc;
            }, {});
            const byLocationCategory = locationCategoryStats.reduce((acc, item) => {
                acc[item._id] = item.count;
                return acc;
            }, {});
            const topCitiesByLocationCount = cityStats.map(item => ({
                cityId: item._id,
                locationCount: item.count
            }));
            return {
                ...baseStats,
                locationsWithRealEstateData,
                averagePropertyPrice: averagePropertyPrice[0]?.avgPrice || 0,
                byLocationType,
                byLocationCategory,
                topCitiesByLocationCount
            };
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to get location statistics: ${error.message}`);
        }
    }
    async validateParentCity(cityId) {
        try {
            const cityExists = await this.locationModel.db.collection('masters').findOne({
                _id: new mongoose_2.Types.ObjectId(cityId),
                masterType: master_types_enum_1.MasterType.CITY,
                status: { $ne: 'archived' }
            });
            if (!cityExists) {
                throw new common_1.BadRequestException(`Parent city with ID ${cityId} not found or inactive`);
            }
        }
        catch (error) {
            if (error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.BadRequestException(`Failed to validate parent city: ${error.message}`);
        }
    }
    async checkUsageBeforeDelete(id) {
    }
};
exports.LocationsService = LocationsService;
exports.LocationsService = LocationsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(location_schema_1.Location.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], LocationsService);
//# sourceMappingURL=locations.service.js.map