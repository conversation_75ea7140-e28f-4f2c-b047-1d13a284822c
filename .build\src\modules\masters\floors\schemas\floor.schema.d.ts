import { Document } from 'mongoose';
import { MasterWithNumericValue } from '../../common/schemas/base-master.schema';
import { MasterType } from '../../common/enums/master-types.enum';
export type FloorDocument = Floor & Document;
export declare class Floor extends MasterWithNumericValue {
    masterType: MasterType.FLOOR;
    numericValue?: number;
    unit?: string;
    floorType?: string;
    usage?: string;
    height?: number;
    area?: number;
    specifications?: {
        ceilingHeight?: number;
        loadCapacity?: number;
        fireRating?: string;
        accessibility?: boolean;
        hvacType?: string;
        electricalLoad?: number;
    };
    amenities?: {
        elevator?: boolean;
        escalator?: boolean;
        fireExit?: boolean;
        emergencyStaircase?: boolean;
        restrooms?: boolean;
        powerBackup?: boolean;
    };
    restrictions?: string[];
    features?: string[];
    priceMultiplier?: number;
    premiumPercentage?: number;
    isAvailable?: boolean;
    maxUnitsPerFloor?: number;
    displayName?: string;
    shortDescription?: string;
    createdAt?: Date;
    updatedAt?: Date;
}
export declare const FloorSchema: import("mongoose").Schema<Floor, import("mongoose").Model<Floor, any, any, any, Document<unknown, any, Floor, any> & Floor & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Floor, Document<unknown, {}, import("mongoose").FlatRecord<Floor>, {}> & import("mongoose").FlatRecord<Floor> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
