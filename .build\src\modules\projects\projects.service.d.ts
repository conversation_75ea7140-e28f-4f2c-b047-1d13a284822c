import { Model } from 'mongoose';
import { Project, ProjectDocument } from './schemas/project.schema';
import { CreateProjectDto } from './dto/create-project.dto';
import { UpdateProjectDto } from './dto/update-project.dto';
import { QueryProjectDto } from './dto/query-project.dto';
import { S3Service } from '../files/services/s3.service';
export declare class ProjectsService {
    private projectModel;
    private readonly s3Service;
    constructor(projectModel: Model<ProjectDocument>, s3Service: S3Service);
    create(createProjectDto: CreateProjectDto, userId: string): Promise<Project>;
    findAll(queryDto: QueryProjectDto): Promise<{
        projects: (import("mongoose").Document<unknown, {}, ProjectDocument, {}> & Project & import("mongoose").Document<unknown, any, any, Record<string, any>> & Required<{
            _id: unknown;
        }> & {
            __v: number;
        })[];
        pagination: {
            page: number;
            limit: number;
            total: number;
            totalPages: number;
            hasNext: boolean;
            hasPrev: boolean;
        };
    }>;
    findById(id: string): Promise<Project>;
    update(id: string, updateProjectDto: UpdateProjectDto, userId: string): Promise<Project>;
    remove(id: string): Promise<void>;
    uploadMedia(projectId: string, files: Express.Multer.File[], mediaType: 'images' | 'videos' | 'brochures' | 'floorPlans' | 'masterPlan' | 'locationMap', userId: string): Promise<string[]>;
    uploadDocuments(projectId: string, files: Express.Multer.File[], documentType: 'approvals' | 'legalDocuments' | 'certificates' | 'others', userId: string): Promise<string[]>;
    getStatistics(): Promise<{
        totalProjects: number;
        activeProjects: number;
        inactiveProjects: number;
        featuredProjects: number;
        statusDistribution: any;
        propertyTypeDistribution: any;
        topCities: any[];
    }>;
    getFeaturedProjects(limit?: number): Promise<(import("mongoose").Document<unknown, {}, ProjectDocument, {}> & Project & import("mongoose").Document<unknown, any, any, Record<string, any>> & Required<{
        _id: unknown;
    }> & {
        __v: number;
    })[]>;
}
