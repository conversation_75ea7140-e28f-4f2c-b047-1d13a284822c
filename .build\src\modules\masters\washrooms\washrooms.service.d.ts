import { Model } from 'mongoose';
import { Washroom, WashroomDocument } from './schemas/washroom.schema';
import { BaseMasterService } from '../common/services/base-master.service';
import { CreateWashroomDto } from './dto/create-washroom.dto';
import { UpdateWashroomDto } from './dto/update-washroom.dto';
import { QueryMasterWithNumericRangeDto } from '../common/dto/query-master.dto';
import { MasterListResponse } from '../common/interfaces/base-master.interface';
export declare class WashroomsService extends BaseMasterService<Washroom> {
    private washroomModel;
    constructor(washroomModel: Model<WashroomDocument>);
    createWashroom(createWashroomDto: CreateWashroomDto): Promise<Washroom>;
    findAllWashrooms(queryDto?: QueryMasterWithNumericRangeDto): Promise<MasterListResponse<Washroom>>;
    findWashroomById(id: string): Promise<Washroom>;
    updateWashroom(id: string, updateWashroomDto: UpdateWashroomDto): Promise<Washroom>;
    removeWashroom(id: string): Promise<void>;
    findWashroomsByType(washroomType: string): Promise<Washroom[]>;
    getWashroomStatistics(): Promise<{
        byWashroomType: any;
        byPopularityRating: any;
        totalCount: number;
        activeCount: number;
        inactiveCount: number;
        popularCount: number;
        defaultCount: number;
        byCategory?: Record<string, number>;
        byStatus?: Record<string, number>;
    }>;
    protected checkUsageBeforeDelete(id: string): Promise<void>;
}
