"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoomsService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const room_schema_1 = require("./schemas/room.schema");
const base_master_service_1 = require("../common/services/base-master.service");
const master_types_enum_1 = require("../common/enums/master-types.enum");
let RoomsService = class RoomsService extends base_master_service_1.BaseMasterService {
    constructor(roomModel) {
        super(roomModel, master_types_enum_1.MasterType.ROOM);
        this.roomModel = roomModel;
    }
    async createRoom(createRoomDto) {
        return await this.create(createRoomDto);
    }
    async findAllRooms(queryDto = {}) {
        try {
            const { page = 1, limit = 10, search, status, isDefault, isPopular, sortBy = 'numericValue', sortOrder = 'asc', minValue, maxValue, unit } = queryDto;
            const filter = {
                masterType: master_types_enum_1.MasterType.ROOM,
                status: { $ne: 'archived' }
            };
            if (search) {
                filter.$or = [
                    { name: { $regex: search, $options: 'i' } },
                    { description: { $regex: search, $options: 'i' } },
                    { code: { $regex: search, $options: 'i' } },
                    { roomType: { $regex: search, $options: 'i' } },
                    { features: { $regex: search, $options: 'i' } }
                ];
            }
            if (status)
                filter.status = status;
            if (typeof isDefault === 'boolean')
                filter.isDefault = isDefault;
            if (typeof isPopular === 'boolean')
                filter.isPopular = isPopular;
            if (unit)
                filter.unit = unit;
            if (minValue !== undefined || maxValue !== undefined) {
                filter.numericValue = {};
                if (minValue !== undefined)
                    filter.numericValue.$gte = minValue;
                if (maxValue !== undefined)
                    filter.numericValue.$lte = maxValue;
            }
            const sort = {};
            sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
            const skip = (page - 1) * limit;
            const [data, total] = await Promise.all([
                this.roomModel.find(filter).sort(sort).skip(skip).limit(limit).exec(),
                this.roomModel.countDocuments(filter)
            ]);
            const totalPages = Math.ceil(total / limit);
            return {
                success: true,
                data: data,
                pagination: { page, limit, total, totalPages }
            };
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch rooms: ${error.message}`);
        }
    }
    async findRoomById(id) {
        return await this.findById(id);
    }
    async updateRoom(id, updateRoomDto) {
        return await this.update(id, updateRoomDto);
    }
    async removeRoom(id) {
        return await this.remove(id);
    }
    async findRoomsByType(roomType) {
        try {
            return await this.roomModel
                .find({
                roomType,
                masterType: master_types_enum_1.MasterType.ROOM,
                status: { $ne: 'archived' }
            })
                .sort({ numericValue: 1 })
                .exec();
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch rooms by type: ${error.message}`);
        }
    }
    async getRoomStatistics() {
        try {
            const baseStats = await this.getStatistics();
            const [roomTypeStats, popularityStats] = await Promise.all([
                this.roomModel.aggregate([
                    {
                        $match: {
                            masterType: master_types_enum_1.MasterType.ROOM,
                            status: { $ne: 'archived' },
                            roomType: { $exists: true, $ne: null }
                        }
                    },
                    { $group: { _id: '$roomType', count: { $sum: 1 } } },
                    { $sort: { count: -1 } }
                ]),
                this.roomModel.aggregate([
                    {
                        $match: {
                            masterType: master_types_enum_1.MasterType.ROOM,
                            popularityRating: { $exists: true, $ne: null },
                            status: { $ne: 'archived' }
                        }
                    },
                    { $group: { _id: '$popularityRating', count: { $sum: 1 } } },
                    { $sort: { _id: 1 } }
                ])
            ]);
            const byRoomType = roomTypeStats.reduce((acc, item) => {
                acc[item._id] = item.count;
                return acc;
            }, {});
            const byPopularityRating = popularityStats.reduce((acc, item) => {
                acc[`rating_${item._id}`] = item.count;
                return acc;
            }, {});
            return {
                ...baseStats,
                byRoomType,
                byPopularityRating
            };
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to get room statistics: ${error.message}`);
        }
    }
    async checkUsageBeforeDelete(id) {
    }
};
exports.RoomsService = RoomsService;
exports.RoomsService = RoomsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(room_schema_1.Room.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], RoomsService);
//# sourceMappingURL=rooms.service.js.map