import { MasterWithNumericValueDto } from '../../common/dto/base-master.dto';
import { MasterType } from '../../common/enums/master-types.enum';
export declare class TowerSpecificationsDto {
    structure?: string;
    elevators?: number;
    staircases?: number;
    parkingLevels?: number;
    totalParkingSpots?: number;
    powerBackup?: boolean;
    waterStorage?: number;
    sewageTreatment?: boolean;
    rainwaterHarvesting?: boolean;
    solarPanels?: boolean;
    greenBuilding?: boolean;
}
export declare class CreateTowerDto extends MasterWithNumericValueDto {
    name: string;
    description?: string;
    code?: string;
    numericValue: number;
    unit?: string;
    towerType?: string;
    totalFloors?: number;
    height?: number;
    totalUnits?: number;
    unitsPerFloor?: number;
    specifications?: TowerSpecificationsDto;
    amenities?: string[];
    features?: string[];
    isActive?: boolean;
    constructionYear?: number;
    architect?: string;
    contractor?: string;
    masterType: MasterType.TOWER;
}
