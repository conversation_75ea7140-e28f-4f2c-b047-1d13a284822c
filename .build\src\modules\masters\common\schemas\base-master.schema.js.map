{"version": 3, "file": "base-master.schema.js", "sourceRoot": "", "sources": ["../../../../../../src/modules/masters/common/schemas/base-master.schema.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,+CAAgD;AAChD,uCAA2C;AAC3C,kEAAsE;AAW/D,IAAM,UAAU,GAAhB,MAAM,UAAU;CA4EtB,CAAA;AA5EY,gCAAU;AAOrB;IANC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,IAAI;QACd,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,GAAG;KAEf,CAAC;;wCACW;AAOb;IALC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,GAAG;KACf,CAAC;;+CACmB;AAUrB;IARC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,EAAE;QACb,MAAM,EAAE,IAAI;QACZ,MAAM,EAAE,IAAI;KAEb,CAAC;;wCACY;AAQd;IANC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,IAAI;QACd,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,8BAAU,CAAC;KAEhC,CAAC;;8CACqB;AASvB;IAPC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,IAAI;QACd,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,gCAAY,CAAC;QACjC,OAAO,EAAE,gCAAY,CAAC,MAAM;KAE7B,CAAC;;0CACmB;AASrB;IAPC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;QACf,GAAG,EAAE,CAAC;QACN,GAAG,EAAE,IAAI;QACT,OAAO,EAAE,CAAC;KAEX,CAAC;;6CACiB;AAOnB;IALC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;QACf,OAAO,EAAE,KAAK;KAEf,CAAC;;6CACkB;AAOpB;IALC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;QACf,OAAO,EAAE,KAAK;KAEf,CAAC;;6CACkB;AAOpB;IALC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,EAAE;KACZ,CAAC;;4CAC6B;qBAvEpB,UAAU;IALtB,IAAA,iBAAM,EAAC;QACN,UAAU,EAAE,IAAI;QAChB,gBAAgB,EAAE,YAAY;QAC9B,UAAU,EAAE,SAAS;KACtB,CAAC;GACW,UAAU,CA4EtB;AAYM,IAAM,gBAAgB,GAAtB,MAAM,gBAAiB,SAAQ,UAAU;CAe/C,CAAA;AAfY,4CAAgB;AAO3B;IANC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,gBAAK,CAAC,QAAQ;QACpB,GAAG,EAAE,YAAY;KAElB,CAAC;8BACS,gBAAK,CAAC,QAAQ;kDAAC;AAO1B;IALC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,8BAAU,CAAC;KAChC,CAAC;;oDACsB;2BAdb,gBAAgB;IAD5B,IAAA,iBAAM,EAAC,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;GACV,gBAAgB,CAe5B;AAOM,IAAM,kBAAkB,GAAxB,MAAM,kBAAmB,SAAQ,UAAU;CAsBjD,CAAA;AAtBY,gDAAkB;AAO7B;IANC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,EAAE;KAEd,CAAC;;oDACgB;AAOlB;IALC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,EAAE;KACd,CAAC;;gDACY;AAOd;IALC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,EAAE;KACd,CAAC;;iDACa;6BArBJ,kBAAkB;IAD9B,IAAA,iBAAM,EAAC,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;GACV,kBAAkB,CAsB9B;AAOM,IAAM,sBAAsB,GAA5B,MAAM,sBAAuB,SAAQ,UAAU;CAuBrD,CAAA;AAvBY,wDAAsB;AAKjC;IAJC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;KAEhB,CAAC;;4DACoB;AAOtB;IALC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,EAAE;KACd,CAAC;;oDACY;AAKd;IAHC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;KAChB,CAAC;;wDACgB;AAKlB;IAHC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;KAChB,CAAC;;wDACgB;iCAtBP,sBAAsB;IADlC,IAAA,iBAAM,EAAC,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;GACV,sBAAsB,CAuBlC;AAOM,IAAM,kBAAkB,GAAxB,MAAM,kBAAmB,SAAQ,UAAU;CAsCjD,CAAA;AAtCY,gDAAkB;AAO7B;IANC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,GAAG;KAEf,CAAC;;iDACa;AAQf;IANC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,GAAG;KAEf,CAAC;;mDACe;AAQjB;IANC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,CAAC,MAAM,CAAC;QACd,QAAQ,EAAE,CAAC,UAAU,EAAE,0CAA0C,CAAC;KAEnE,CAAC;;uDAC6B;AAO/B;IALC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,EAAE;KACd,CAAC;;oDACgB;AAOlB;IALC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,CAAC,MAAM,CAAC;QACd,OAAO,EAAE,EAAE;KACZ,CAAC;;oDACkB;6BArCT,kBAAkB;IAD9B,IAAA,iBAAM,EAAC,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;GACV,kBAAkB,CAsC9B;AAGD,SAAS,UAAU,CAAC,GAAa;IAC/B,OAAO,GAAG,CAAC,MAAM,KAAK,CAAC,CAAC;AAC1B,CAAC;AAWM,IAAM,cAAc,GAApB,MAAM,cAAe,SAAQ,UAAU;CAsG7C,CAAA;AAtGY,wCAAc;AAQzB;IANC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,gBAAK,CAAC,QAAQ;QACpB,GAAG,EAAE,gBAAgB;KAEtB,CAAC;8BACS,gBAAK,CAAC,QAAQ;gDAAC;AAO1B;IALC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,MAAM;QACZ,IAAI,EAAE,MAAM,CAAC,MAAM,CAAC,8BAAU,CAAC;KAChC,CAAC;;kDACsB;AASxB;IANC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,EAAE;KAEd,CAAC;;gDACgB;AAOlB;IALC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,EAAE;KACd,CAAC;;4CACY;AAOd;IALC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,EAAE;KACd,CAAC;;6CACa;AAOf;IAJC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;KAEhB,CAAC;;oDACoB;AAOtB;IALC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,EAAE;KACd,CAAC;;4CACY;AAKd;IAHC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;KAChB,CAAC;;gDACgB;AAKlB;IAHC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;KAChB,CAAC;;gDACgB;AASlB;IANC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,GAAG;KAEf,CAAC;;6CACa;AAQf;IANC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,GAAG;KAEf,CAAC;;+CACe;AAQjB;IANC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,CAAC,MAAM,CAAC;QACd,QAAQ,EAAE,CAAC,UAAU,EAAE,0CAA0C,CAAC;KAEnE,CAAC;;mDAC6B;AAO/B;IALC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,IAAI;QACV,SAAS,EAAE,EAAE;KACd,CAAC;;gDACgB;AAOlB;IALC,IAAA,eAAI,EAAC;QACJ,QAAQ,EAAE,KAAK;QACf,IAAI,EAAE,CAAC,MAAM,CAAC;QACd,OAAO,EAAE,EAAE;KACZ,CAAC;;gDACkB;yBArGT,cAAc;IAL1B,IAAA,iBAAM,EAAC;QACN,UAAU,EAAE,IAAI;QAChB,gBAAgB,EAAE,YAAY;QAC9B,UAAU,EAAE,SAAS;KACtB,CAAC;GACW,cAAc,CAsG1B"}