"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
let AppService = class AppService {
    constructor(configService) {
        this.configService = configService;
    }
    getHello() {
        return {
            message: 'TrelaX Core Admin Backend API is running successfully! 🚀',
            timestamp: new Date().toISOString(),
            version: '1.0.0',
            environment: this.configService.get('NODE_ENV', 'development'),
        };
    }
    getApiInfo() {
        const apiPrefix = this.configService.get('API_PREFIX', 'api/v1');
        const port = this.configService.get('PORT', 3000);
        const baseUrl = `http://localhost:${port}/${apiPrefix}`;
        return {
            name: 'TrelaX Core Admin Backend API',
            version: '1.0.0',
            description: 'Complete Real Estate Management Backend with comprehensive modules',
            endpoints: {
                auth: `${baseUrl}/auth`,
                projects: `${baseUrl}/projects`,
                masters: `${baseUrl}/masters`,
                builders: `${baseUrl}/builders`,
                agents: `${baseUrl}/agents`,
                files: `${baseUrl}/files`,
                docs: `${baseUrl}/docs`,
            },
            modules: {
                authentication: 'JWT-based admin authentication',
                projects: 'Real estate project management with media uploads',
                masters: 'Master data management (cities, locations, amenities, etc.)',
                builders: 'Builder profile and information management',
                agents: 'Real estate agent management',
                files: 'AWS S3 file upload and management',
            },
            features: [
                'JWT Authentication with Role-based Access Control',
                'Real Estate Project Management',
                'Master Data Management for Form Fields',
                'Builder and Agent Profile Management',
                'AWS S3 File Upload with Multiple File Types',
                'Advanced Search and Filtering',
                'Pagination and Statistics',
                'MongoDB Database Integration',
                'Comprehensive Swagger API Documentation',
                'Input Validation and Error Handling',
                'CORS Support',
                'Environment Configuration',
            ],
        };
    }
};
exports.AppService = AppService;
exports.AppService = AppService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], AppService);
//# sourceMappingURL=app.service.js.map