"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FloorsService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const floor_schema_1 = require("./schemas/floor.schema");
const base_master_service_1 = require("../common/services/base-master.service");
const master_types_enum_1 = require("../common/enums/master-types.enum");
let FloorsService = class FloorsService extends base_master_service_1.BaseMasterService {
    constructor(floorModel) {
        super(floorModel, master_types_enum_1.MasterType.FLOOR);
        this.floorModel = floorModel;
    }
    async createFloor(createFloorDto) {
        return await this.create(createFloorDto);
    }
    async findAllFloors(queryDto = {}) {
        try {
            const { page = 1, limit = 10, search, status, isDefault, isPopular, sortBy = 'numericValue', sortOrder = 'asc', minValue, maxValue, unit } = queryDto;
            const filter = {
                masterType: master_types_enum_1.MasterType.FLOOR,
                status: { $ne: 'archived' }
            };
            if (search) {
                filter.$or = [
                    { name: { $regex: search, $options: 'i' } },
                    { description: { $regex: search, $options: 'i' } },
                    { code: { $regex: search, $options: 'i' } },
                    { displayName: { $regex: search, $options: 'i' } },
                    { shortDescription: { $regex: search, $options: 'i' } },
                    { features: { $regex: search, $options: 'i' } }
                ];
            }
            if (status)
                filter.status = status;
            if (typeof isDefault === 'boolean')
                filter.isDefault = isDefault;
            if (typeof isPopular === 'boolean')
                filter.isPopular = isPopular;
            if (unit)
                filter.unit = unit;
            if (minValue !== undefined || maxValue !== undefined) {
                filter.numericValue = {};
                if (minValue !== undefined)
                    filter.numericValue.$gte = minValue;
                if (maxValue !== undefined)
                    filter.numericValue.$lte = maxValue;
            }
            const sort = {};
            sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
            const skip = (page - 1) * limit;
            const [data, total] = await Promise.all([
                this.floorModel
                    .find(filter)
                    .sort(sort)
                    .skip(skip)
                    .limit(limit)
                    .exec(),
                this.floorModel.countDocuments(filter)
            ]);
            const totalPages = Math.ceil(total / limit);
            return {
                success: true,
                data: data,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages
                }
            };
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch floors: ${error.message}`);
        }
    }
    async findFloorById(id) {
        return await this.findById(id);
    }
    async updateFloor(id, updateFloorDto) {
        return await this.update(id, updateFloorDto);
    }
    async removeFloor(id) {
        return await this.remove(id);
    }
    async findFloorsByType(floorType) {
        try {
            return await this.floorModel
                .find({
                floorType,
                masterType: master_types_enum_1.MasterType.FLOOR,
                status: { $ne: 'archived' }
            })
                .sort({ numericValue: 1 })
                .exec();
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch floors by type: ${error.message}`);
        }
    }
    async findFloorsByUsage(usage) {
        try {
            return await this.floorModel
                .find({
                usage,
                masterType: master_types_enum_1.MasterType.FLOOR,
                status: { $ne: 'archived' }
            })
                .sort({ numericValue: 1 })
                .exec();
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch floors by usage: ${error.message}`);
        }
    }
    async findAvailableFloors() {
        try {
            return await this.floorModel
                .find({
                isAvailable: true,
                masterType: master_types_enum_1.MasterType.FLOOR,
                status: 'active'
            })
                .sort({ numericValue: 1 })
                .exec();
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch available floors: ${error.message}`);
        }
    }
    async findFloorsInRange(minFloor, maxFloor) {
        try {
            return await this.floorModel
                .find({
                numericValue: { $gte: minFloor, $lte: maxFloor },
                masterType: master_types_enum_1.MasterType.FLOOR,
                status: { $ne: 'archived' }
            })
                .sort({ numericValue: 1 })
                .exec();
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch floors in range: ${error.message}`);
        }
    }
    async findBasementFloors() {
        try {
            return await this.floorModel
                .find({
                numericValue: { $lt: 0 },
                masterType: master_types_enum_1.MasterType.FLOOR,
                status: { $ne: 'archived' }
            })
                .sort({ numericValue: -1 })
                .exec();
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch basement floors: ${error.message}`);
        }
    }
    async findGroundFloor() {
        try {
            return await this.floorModel
                .findOne({
                numericValue: 0,
                masterType: master_types_enum_1.MasterType.FLOOR,
                status: { $ne: 'archived' }
            })
                .exec();
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch ground floor: ${error.message}`);
        }
    }
    async findUpperFloors() {
        try {
            return await this.floorModel
                .find({
                numericValue: { $gt: 0 },
                masterType: master_types_enum_1.MasterType.FLOOR,
                status: { $ne: 'archived' }
            })
                .sort({ numericValue: 1 })
                .exec();
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch upper floors: ${error.message}`);
        }
    }
    async findPremiumFloors() {
        try {
            return await this.floorModel
                .find({
                priceMultiplier: { $gt: 1 },
                masterType: master_types_enum_1.MasterType.FLOOR,
                status: { $ne: 'archived' }
            })
                .sort({ priceMultiplier: -1, numericValue: 1 })
                .exec();
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch premium floors: ${error.message}`);
        }
    }
    async getFloorStatistics() {
        try {
            const baseStats = await this.getStatistics();
            const [floorTypeStats, usageStats, availabilityStats, floorRangeStats, averagePriceMultiplier] = await Promise.all([
                this.floorModel.aggregate([
                    {
                        $match: {
                            masterType: master_types_enum_1.MasterType.FLOOR,
                            status: { $ne: 'archived' },
                            floorType: { $exists: true, $ne: null }
                        }
                    },
                    { $group: { _id: '$floorType', count: { $sum: 1 } } },
                    { $sort: { count: -1 } }
                ]),
                this.floorModel.aggregate([
                    {
                        $match: {
                            masterType: master_types_enum_1.MasterType.FLOOR,
                            status: { $ne: 'archived' },
                            usage: { $exists: true, $ne: null }
                        }
                    },
                    { $group: { _id: '$usage', count: { $sum: 1 } } },
                    { $sort: { count: -1 } }
                ]),
                this.floorModel.aggregate([
                    {
                        $match: {
                            masterType: master_types_enum_1.MasterType.FLOOR,
                            status: { $ne: 'archived' }
                        }
                    },
                    {
                        $group: {
                            _id: null,
                            available: { $sum: { $cond: ['$isAvailable', 1, 0] } },
                            unavailable: { $sum: { $cond: ['$isAvailable', 0, 1] } }
                        }
                    }
                ]),
                this.floorModel.aggregate([
                    {
                        $match: {
                            masterType: master_types_enum_1.MasterType.FLOOR,
                            status: { $ne: 'archived' },
                            numericValue: { $exists: true, $ne: null }
                        }
                    },
                    {
                        $group: {
                            _id: null,
                            minFloor: { $min: '$numericValue' },
                            maxFloor: { $max: '$numericValue' },
                            basementCount: { $sum: { $cond: [{ $lt: ['$numericValue', 0] }, 1, 0] } },
                            groundCount: { $sum: { $cond: [{ $eq: ['$numericValue', 0] }, 1, 0] } },
                            upperCount: { $sum: { $cond: [{ $gt: ['$numericValue', 0] }, 1, 0] } }
                        }
                    }
                ]),
                this.floorModel.aggregate([
                    {
                        $match: {
                            masterType: master_types_enum_1.MasterType.FLOOR,
                            priceMultiplier: { $exists: true, $ne: null },
                            status: { $ne: 'archived' }
                        }
                    },
                    {
                        $group: {
                            _id: null,
                            avgMultiplier: { $avg: '$priceMultiplier' }
                        }
                    }
                ])
            ]);
            const byFloorType = floorTypeStats.reduce((acc, item) => {
                acc[item._id] = item.count;
                return acc;
            }, {});
            const byUsage = usageStats.reduce((acc, item) => {
                acc[item._id] = item.count;
                return acc;
            }, {});
            return {
                ...baseStats,
                averagePriceMultiplier: averagePriceMultiplier[0]?.avgMultiplier || 1,
                byFloorType,
                byUsage,
                availability: availabilityStats[0] || { available: 0, unavailable: 0 },
                floorRange: floorRangeStats[0] || {
                    minFloor: 0,
                    maxFloor: 0,
                    basementCount: 0,
                    groundCount: 0,
                    upperCount: 0
                }
            };
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to get floor statistics: ${error.message}`);
        }
    }
    async checkUsageBeforeDelete(id) {
    }
};
exports.FloorsService = FloorsService;
exports.FloorsService = FloorsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(floor_schema_1.Floor.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], FloorsService);
//# sourceMappingURL=floors.service.js.map