"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryCityDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const query_master_dto_1 = require("../../common/dto/query-master.dto");
class QueryCityDto extends query_master_dto_1.QueryMasterWithLocationDto {
}
exports.QueryCityDto = QueryCityDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by state code',
        example: 'MH'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryCityDto.prototype, "stateCode", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by country code',
        example: 'IN'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryCityDto.prototype, "countryCode", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Minimum population',
        example: 1000000
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], QueryCityDto.prototype, "minPopulation", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Maximum population',
        example: 20000000
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], QueryCityDto.prototype, "maxPopulation", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Minimum area in sq km',
        example: 100
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], QueryCityDto.prototype, "minArea", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Maximum area in sq km',
        example: 1000
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], QueryCityDto.prototype, "maxArea", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by major language',
        example: 'Hindi'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryCityDto.prototype, "majorLanguage", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by district',
        example: 'Mumbai Suburban'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryCityDto.prototype, "district", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by division',
        example: 'Konkan Division'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryCityDto.prototype, "division", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Minimum average property price',
        example: 5000
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], QueryCityDto.prototype, "minPropertyPrice", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Maximum average property price',
        example: 15000
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], QueryCityDto.prototype, "maxPropertyPrice", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Minimum price appreciation rate',
        example: 5
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(-100),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], QueryCityDto.prototype, "minAppreciationRate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Maximum price appreciation rate',
        example: 20
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(-100),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], QueryCityDto.prototype, "maxAppreciationRate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by nearby airport code',
        example: 'BOM'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryCityDto.prototype, "nearbyAirport", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by highway',
        example: 'NH-8'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryCityDto.prototype, "highway", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter cities with featured images only',
        example: true
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], QueryCityDto.prototype, "hasFeaturedImage", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter cities with gallery images only',
        example: true
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], QueryCityDto.prototype, "hasGallery", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter cities with real estate data only',
        example: true
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], QueryCityDto.prototype, "hasRealEstateData", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter cities with economic data only',
        example: true
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], QueryCityDto.prototype, "hasEconomicData", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter cities with climate data only',
        example: true
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], QueryCityDto.prototype, "hasClimateData", void 0);
//# sourceMappingURL=query-city.dto.js.map