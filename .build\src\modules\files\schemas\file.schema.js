"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileSchema = exports.File = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
let File = class File {
};
exports.File = File;
__decorate([
    (0, mongoose_1.Prop)({
        required: true,
        trim: true,
    }),
    __metadata("design:type", String)
], File.prototype, "filename", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: true,
        trim: true,
    }),
    __metadata("design:type", String)
], File.prototype, "originalName", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: true,
        trim: true,
    }),
    __metadata("design:type", String)
], File.prototype, "mimetype", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: true,
        min: 0,
    }),
    __metadata("design:type", Number)
], File.prototype, "size", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: true,
        trim: true,
    }),
    __metadata("design:type", String)
], File.prototype, "url", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: true,
        trim: true,
        unique: true,
    }),
    __metadata("design:type", String)
], File.prototype, "key", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: true,
        trim: true,
    }),
    __metadata("design:type", String)
], File.prototype, "bucket", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 500,
    }),
    __metadata("design:type", String)
], File.prototype, "description", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: [String],
        default: [],
    }),
    __metadata("design:type", Array)
], File.prototype, "tags", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        type: mongoose_2.Types.ObjectId,
        ref: 'User',
        required: true,
        index: true,
    }),
    __metadata("design:type", mongoose_2.Types.ObjectId)
], File.prototype, "uploadedBy", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        default: true,
        index: true,
    }),
    __metadata("design:type", Boolean)
], File.prototype, "isActive", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        default: false,
    }),
    __metadata("design:type", Boolean)
], File.prototype, "isPublic", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
    }),
    __metadata("design:type", Date)
], File.prototype, "expiresAt", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        default: 0,
    }),
    __metadata("design:type", Number)
], File.prototype, "downloadCount", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
    }),
    __metadata("design:type", Date)
], File.prototype, "lastAccessedAt", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        enum: ['image', 'document', 'video', 'audio', 'archive', 'other'],
        default: 'other',
        index: true,
    }),
    __metadata("design:type", String)
], File.prototype, "category", void 0);
exports.File = File = __decorate([
    (0, mongoose_1.Schema)({
        timestamps: true,
        collection: 'files',
    })
], File);
exports.FileSchema = mongoose_1.SchemaFactory.createForClass(File);
exports.FileSchema.index({ uploadedBy: 1 });
exports.FileSchema.index({ isActive: 1 });
exports.FileSchema.index({ category: 1 });
exports.FileSchema.index({ createdAt: -1 });
exports.FileSchema.index({ key: 1 }, { unique: true });
exports.FileSchema.pre('save', function (next) {
    if (this.isModified('mimetype')) {
        const mimetype = this.mimetype.toLowerCase();
        if (mimetype.startsWith('image/')) {
            this.category = 'image';
        }
        else if (mimetype.startsWith('video/')) {
            this.category = 'video';
        }
        else if (mimetype.startsWith('audio/')) {
            this.category = 'audio';
        }
        else if (mimetype.includes('pdf') || mimetype.includes('document') || mimetype.includes('text')) {
            this.category = 'document';
        }
        else if (mimetype.includes('zip') || mimetype.includes('rar') || mimetype.includes('tar')) {
            this.category = 'archive';
        }
        else {
            this.category = 'other';
        }
    }
    next();
});
//# sourceMappingURL=file.schema.js.map