# 🐳 Docker Setup Guide for TrelaX Admin Backend

This guide provides comprehensive instructions for setting up and running the TrelaX Admin Backend using Docker and Docker Compose.

## 📋 Overview

The Docker setup includes:
- **NestJS Application** with hot reload for development
- **MongoDB** database with initialization scripts
- **Redis** for caching and sessions
- **LocalStack** for AWS S3 simulation
- **Nginx** reverse proxy with load balancing
- **Admin UIs** for MongoDB and Redis
- **Comprehensive testing environment**

## 🚀 Quick Start

### Prerequisites

1. **Docker** (version 20.10 or higher)
2. **Docker Compose** (version 2.0 or higher)
3. **Git** for cloning the repository

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd TrelaXCoreAdminBackend

# Make scripts executable (Linux/Mac)
chmod +x docker-manager.sh
chmod +x docker/localstack/init-scripts/01-create-s3-bucket.sh

# Start development environment
./docker-manager.sh start-dev

# Or on Windows
docker-manager.bat start-dev
```

## 🏗️ Architecture

### Services Overview

| Service | Port | Description | Environment |
|---------|------|-------------|-------------|
| **app** | 3000 | NestJS Application | Production |
| **app-dev** | 3000, 9229 | NestJS with hot reload & debug | Development |
| **mongodb** | 27017 | MongoDB Database | Production |
| **mongodb-dev** | 27018 | MongoDB Database | Development |
| **mongodb-test** | 27019 | MongoDB Database | Testing |
| **redis** | 6379 | Redis Cache | Production |
| **redis-dev** | 6380 | Redis Cache | Development |
| **localstack** | 4566 | AWS S3 Simulation | Production |
| **localstack-dev** | 4567 | AWS S3 Simulation | Development |
| **nginx** | 80, 443 | Reverse Proxy | Production |
| **mongo-express** | 8081 | MongoDB Admin UI | Production |
| **mongo-express-dev** | 8083 | MongoDB Admin UI | Development |
| **redis-commander** | 8082 | Redis Admin UI | Production |
| **redis-commander-dev** | 8084 | Redis Admin UI | Development |

### Network Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│     Client      │───▶│      Nginx      │───▶│   NestJS App    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                       ┌─────────────────┐             │
                       │     MongoDB     │◀────────────┤
                       └─────────────────┘             │
                                                        │
                       ┌─────────────────┐             │
                       │      Redis      │◀────────────┤
                       └─────────────────┘             │
                                                        │
                       ┌─────────────────┐             │
                       │   LocalStack    │◀────────────┘
                       │   (AWS S3)      │
                       └─────────────────┘
```

## 🛠️ Docker Compose Files

### 1. `docker-compose.yml` - Production Environment

**Services:**
- Production-ready NestJS application
- MongoDB with persistent storage
- Redis for caching
- LocalStack for S3 simulation
- Nginx reverse proxy
- Admin UIs for database management

**Features:**
- Health checks for all services
- Persistent volumes
- Security configurations
- Performance optimizations

### 2. `docker-compose.dev.yml` - Development Environment

**Services:**
- NestJS with hot reload and debugging
- Separate development databases
- Volume mounting for live code changes
- Debug port exposure (9229)

**Features:**
- Live code reloading
- Debug support
- Separate data volumes
- Development-specific configurations

### 3. `docker-compose.test.yml` - Testing Environment

**Services:**
- Isolated test databases
- E2E test runner
- Unit test runner
- Coverage reporter
- Performance test runner

**Features:**
- Isolated test environment
- Automated test execution
- Coverage reporting
- Performance testing

## 📝 Environment Configuration

### Production Environment (`.env.docker`)

```bash
# Database
MONGO_URI=mongodb://mongodb:27017/trelax_admin_db

# JWT
JWT_SECRET=docker_jwt_secret_key_change_in_production_2024
JWT_EXPIRES_IN=7d

# AWS S3 (LocalStack)
AWS_ACCESS_KEY_ID=test
AWS_SECRET_ACCESS_KEY=test
AWS_REGION=us-east-1
AWS_S3_BUCKET=trelax-admin-uploads
AWS_ENDPOINT_URL=http://localstack:4566

# Server
PORT=3000
NODE_ENV=production
```

### Development Environment

Development uses the same `.env.docker` file but with different service names and ports.

### Test Environment (`.env.test`)

```bash
NODE_ENV=test
MONGO_URI_TEST=mongodb://localhost:27017/trelax_test_db
JWT_SECRET=test_jwt_secret_key_for_e2e_tests_only
```

## 🚀 Usage Commands

### Using Docker Manager Scripts

#### Linux/Mac (`docker-manager.sh`)

```bash
# Start environments
./docker-manager.sh start-prod      # Production environment
./docker-manager.sh start-dev       # Development environment

# Stop environments
./docker-manager.sh stop            # Stop all environments

# Restart environments
./docker-manager.sh restart-prod    # Restart production
./docker-manager.sh restart-dev     # Restart development

# Testing
./docker-manager.sh test            # Run all tests
./docker-manager.sh test-performance # Performance tests
./docker-manager.sh coverage       # Generate coverage

# Monitoring
./docker-manager.sh logs app production  # Show app logs
./docker-manager.sh status         # Show service status

# Maintenance
./docker-manager.sh cleanup        # Clean up Docker resources
```

#### Windows (`docker-manager.bat`)

```cmd
REM Start environments
docker-manager.bat start-prod      REM Production environment
docker-manager.bat start-dev       REM Development environment

REM Stop environments
docker-manager.bat stop            REM Stop all environments

REM Testing
docker-manager.bat test            REM Run all tests

REM Monitoring
docker-manager.bat status          REM Show service status
```

### Direct Docker Compose Commands

```bash
# Production
docker-compose -f docker-compose.yml up -d --build
docker-compose -f docker-compose.yml down

# Development
docker-compose -f docker-compose.dev.yml up -d --build
docker-compose -f docker-compose.dev.yml down

# Testing
docker-compose -f docker-compose.test.yml --profile testing up --build
docker-compose -f docker-compose.test.yml down -v
```

## 🔧 Development Workflow

### 1. Start Development Environment

```bash
./docker-manager.sh start-dev
```

This will:
- Build the development Docker image
- Start all development services
- Mount your source code for live reloading
- Expose debug port 9229

### 2. Access Services

- **API**: http://localhost:3000
- **API Documentation**: http://localhost:3000/api/v1/docs
- **MongoDB Express**: http://localhost:8083 (admin/admin123)
- **Redis Commander**: http://localhost:8084 (admin/admin123)

### 3. Debug the Application

```bash
# Attach debugger to port 9229
# In VS Code, use the provided debug configuration
```

### 4. Run Tests

```bash
# Run all tests
./docker-manager.sh test

# Run specific test types
docker-compose -f docker-compose.test.yml --profile testing run --rm unit-tests
docker-compose -f docker-compose.test.yml --profile testing run --rm e2e-tests
```

### 5. View Logs

```bash
# All services
./docker-manager.sh logs

# Specific service
./docker-manager.sh logs app-dev development
```

## 🧪 Testing

### E2E Testing

```bash
# Run E2E tests
./docker-manager.sh test

# Run with coverage
./docker-manager.sh coverage

# Run specific test suite
docker-compose -f docker-compose.test.yml --profile testing run --rm e2e-tests npm run test:e2e:single auth
```

### Performance Testing

```bash
# Run performance tests
./docker-manager.sh test-performance

# Results will be in ./performance-results directory
```

## 📊 Monitoring and Debugging

### Health Checks

All services include health checks:

```bash
# Check service health
docker-compose -f docker-compose.yml ps

# View health check logs
docker inspect --format='{{json .State.Health}}' trelax-admin-backend
```

### Logs

```bash
# View all logs
docker-compose -f docker-compose.yml logs -f

# View specific service logs
docker-compose -f docker-compose.yml logs -f app

# View logs with timestamps
docker-compose -f docker-compose.yml logs -f -t app
```

### Resource Usage

```bash
# View resource usage
docker stats

# View system information
docker system df
docker system info
```

## 🔒 Security Considerations

### Production Security

1. **Change Default Passwords**
   ```bash
   # Update .env.docker with secure passwords
   MONGO_INITDB_ROOT_PASSWORD=your_secure_password
   JWT_SECRET=your_secure_jwt_secret
   ```

2. **Enable HTTPS**
   - Add SSL certificates to `docker/nginx/ssl/`
   - Uncomment HTTPS server block in nginx configuration

3. **Network Security**
   - Services communicate through internal Docker network
   - Only necessary ports are exposed

4. **Database Security**
   - MongoDB authentication enabled
   - Redis password protection (optional)

### Development Security

- Development environment uses default credentials
- LocalStack simulates AWS services locally
- Debug ports should not be exposed in production

## 🚨 Troubleshooting

### Common Issues

1. **Port Conflicts**
   ```bash
   # Check port usage
   netstat -tulpn | grep :3000
   
   # Stop conflicting services
   sudo systemctl stop apache2  # Example
   ```

2. **Permission Issues**
   ```bash
   # Fix file permissions
   sudo chown -R $USER:$USER uploads logs
   chmod +x docker-manager.sh
   ```

3. **Docker Daemon Issues**
   ```bash
   # Restart Docker
   sudo systemctl restart docker
   
   # Check Docker status
   sudo systemctl status docker
   ```

4. **Memory Issues**
   ```bash
   # Increase Docker memory limit
   # Docker Desktop: Settings > Resources > Memory
   
   # Clean up unused resources
   docker system prune -a
   ```

### Service-Specific Issues

1. **MongoDB Connection Issues**
   ```bash
   # Check MongoDB logs
   docker-compose logs mongodb
   
   # Connect to MongoDB directly
   docker exec -it trelax-mongodb mongosh
   ```

2. **Redis Connection Issues**
   ```bash
   # Check Redis logs
   docker-compose logs redis
   
   # Test Redis connection
   docker exec -it trelax-redis redis-cli ping
   ```

3. **LocalStack Issues**
   ```bash
   # Check LocalStack logs
   docker-compose logs localstack
   
   # Test S3 connection
   docker exec -it trelax-localstack awslocal s3 ls
   ```

## 📈 Performance Optimization

### Production Optimizations

1. **Resource Limits**
   ```yaml
   # Add to docker-compose.yml
   deploy:
     resources:
       limits:
         cpus: '0.5'
         memory: 512M
   ```

2. **Health Check Optimization**
   ```yaml
   healthcheck:
     interval: 30s
     timeout: 10s
     retries: 3
     start_period: 40s
   ```

3. **Volume Optimization**
   - Use named volumes for better performance
   - Avoid bind mounts in production

### Development Optimizations

1. **Node Modules Caching**
   ```yaml
   volumes:
     - .:/app
     - /app/node_modules  # Anonymous volume for node_modules
   ```

2. **Hot Reload Optimization**
   - Use nodemon for efficient file watching
   - Exclude unnecessary files from watching

## 🔄 CI/CD Integration

### GitHub Actions Example

```yaml
name: Docker Build and Test

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Build and test
        run: |
          chmod +x docker-manager.sh
          ./docker-manager.sh test
          
      - name: Upload coverage
        uses: codecov/codecov-action@v1
        with:
          file: ./coverage/lcov.info
```

## 📚 Additional Resources

- [Docker Documentation](https://docs.docker.com/)
- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [NestJS Docker Guide](https://docs.nestjs.com/recipes/docker)
- [MongoDB Docker Hub](https://hub.docker.com/_/mongo)
- [Redis Docker Hub](https://hub.docker.com/_/redis)
- [LocalStack Documentation](https://docs.localstack.cloud/)

## 🆘 Support

If you encounter issues:

1. Check the troubleshooting section above
2. Review service logs: `./docker-manager.sh logs`
3. Check service status: `./docker-manager.sh status`
4. Clean up and restart: `./docker-manager.sh cleanup && ./docker-manager.sh start-dev`

For additional support, please refer to the project documentation or contact the development team.
