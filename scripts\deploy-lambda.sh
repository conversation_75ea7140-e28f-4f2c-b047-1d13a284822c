#!/bin/bash

# Lambda Deployment Script for TrelaX Admin Backend
# This script handles the complete deployment process for AWS Lambda

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
STAGE="dev"
REGION="ap-south-1"
VERBOSE=false
DRY_RUN=false
SKIP_BUILD=false
SKIP_TESTS=false

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

# Function to show help
show_help() {
    echo "TrelaX Admin Backend Lambda Deployment Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -s, --stage STAGE       Deployment stage (dev, prod) [default: dev]"
    echo "  -r, --region REGION     AWS region [default: ap-south-1]"
    echo "  -v, --verbose           Enable verbose output"
    echo "  -d, --dry-run           Show what would be deployed without deploying"
    echo "  --skip-build            Skip the build process"
    echo "  --skip-tests            Skip running tests"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 --stage dev          Deploy to development stage"
    echo "  $0 --stage prod         Deploy to production stage"
    echo "  $0 --dry-run            Show deployment plan without deploying"
    echo "  $0 --skip-tests         Deploy without running tests"
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if Node.js is installed
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js and try again."
        exit 1
    fi
    
    # Check if npm is installed
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm and try again."
        exit 1
    fi
    
    # Check if Serverless Framework is installed
    if ! command -v serverless &> /dev/null; then
        print_warning "Serverless Framework is not installed globally. Installing..."
        npm install -g serverless
    fi
    
    # Check if AWS CLI is installed and configured
    if ! command -v aws &> /dev/null; then
        print_warning "AWS CLI is not installed. Please install and configure AWS CLI."
        print_warning "You can still deploy using Serverless Framework with AWS credentials."
    else
        print_status "AWS CLI found. Checking configuration..."
        if ! aws sts get-caller-identity &> /dev/null; then
            print_warning "AWS CLI is not configured or credentials are invalid."
            print_warning "Please run 'aws configure' to set up your credentials."
        else
            print_status "AWS credentials are configured."
        fi
    fi
    
    print_status "Prerequisites check completed."
}

# Function to install dependencies
install_dependencies() {
    if [ "$SKIP_BUILD" = false ]; then
        print_status "Installing dependencies..."
        npm ci --production=false
        print_status "Dependencies installed successfully."
    else
        print_status "Skipping dependency installation."
    fi
}

# Function to run tests
run_tests() {
    if [ "$SKIP_TESTS" = false ]; then
        print_status "Running tests..."
        npm run test
        print_status "Tests completed successfully."
    else
        print_status "Skipping tests."
    fi
}

# Function to build the application
build_application() {
    if [ "$SKIP_BUILD" = false ]; then
        print_status "Building application for Lambda..."
        npm run build:lambda
        print_status "Application built successfully."
    else
        print_status "Skipping build process."
    fi
}

# Function to validate serverless configuration
validate_config() {
    print_status "Validating Serverless configuration..."
    
    local config_file="serverless.yml"
    if [ "$STAGE" = "dev" ]; then
        config_file="serverless.dev.yml"
    elif [ "$STAGE" = "prod" ]; then
        config_file="serverless.prod.yml"
    fi
    
    if [ ! -f "$config_file" ]; then
        print_error "Serverless configuration file '$config_file' not found."
        exit 1
    fi
    
    # Validate configuration
    if [ "$VERBOSE" = true ]; then
        serverless print --stage "$STAGE" --config "$config_file"
    fi
    
    print_status "Configuration validation completed."
}

# Function to deploy to AWS Lambda
deploy_lambda() {
    print_header "Deploying to AWS Lambda"
    
    local config_file="serverless.yml"
    if [ "$STAGE" = "dev" ]; then
        config_file="serverless.dev.yml"
    elif [ "$STAGE" = "prod" ]; then
        config_file="serverless.prod.yml"
    fi
    
    local deploy_cmd="serverless deploy --stage $STAGE --region $REGION --config $config_file"
    
    if [ "$VERBOSE" = true ]; then
        deploy_cmd="$deploy_cmd --verbose"
    fi
    
    if [ "$DRY_RUN" = true ]; then
        print_status "DRY RUN: Would execute: $deploy_cmd"
        serverless package --stage "$STAGE" --config "$config_file"
        print_status "Package created successfully. Deployment skipped (dry run)."
        return
    fi
    
    print_status "Executing deployment..."
    eval "$deploy_cmd"
    
    print_status "Deployment completed successfully!"
}

# Function to show deployment info
show_deployment_info() {
    print_header "Deployment Information"
    
    local config_file="serverless.yml"
    if [ "$STAGE" = "dev" ]; then
        config_file="serverless.dev.yml"
    elif [ "$STAGE" = "prod" ]; then
        config_file="serverless.prod.yml"
    fi
    
    serverless info --stage "$STAGE" --config "$config_file"
}

# Function to run post-deployment tests
run_post_deployment_tests() {
    print_status "Running post-deployment health checks..."
    
    # Get the API Gateway URL
    local api_url=$(serverless info --stage "$STAGE" --verbose | grep -o 'https://[^[:space:]]*')
    
    if [ -n "$api_url" ]; then
        print_status "Testing health endpoint: $api_url/health"
        
        # Test health endpoint
        if curl -f -s "$api_url/health" > /dev/null; then
            print_status "Health check passed!"
        else
            print_warning "Health check failed. The API might still be initializing."
        fi
    else
        print_warning "Could not determine API Gateway URL for health check."
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -s|--stage)
            STAGE="$2"
            shift 2
            ;;
        -r|--region)
            REGION="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -d|--dry-run)
            DRY_RUN=true
            shift
            ;;
        --skip-build)
            SKIP_BUILD=true
            shift
            ;;
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate stage
if [[ "$STAGE" != "dev" && "$STAGE" != "prod" ]]; then
    print_error "Invalid stage: $STAGE. Must be 'dev' or 'prod'."
    exit 1
fi

# Main deployment process
main() {
    print_header "TrelaX Admin Backend Lambda Deployment"
    print_status "Stage: $STAGE"
    print_status "Region: $REGION"
    print_status "Dry Run: $DRY_RUN"
    echo ""
    
    check_prerequisites
    install_dependencies
    run_tests
    build_application
    validate_config
    deploy_lambda
    
    if [ "$DRY_RUN" = false ]; then
        show_deployment_info
        run_post_deployment_tests
        
        print_header "Deployment Completed Successfully!"
        print_status "Your TrelaX Admin Backend is now running on AWS Lambda."
        print_status "Stage: $STAGE"
        print_status "Region: $REGION"
    fi
}

# Run main function
main
