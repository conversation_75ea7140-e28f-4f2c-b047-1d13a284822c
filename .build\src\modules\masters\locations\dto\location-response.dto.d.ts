import { MasterStatus, MasterType } from '../../common/enums/master-types.enum';
export declare class LocationResponseDto {
    _id: string;
    name: string;
    description?: string;
    code?: string;
    masterType: MasterType.LOCATION;
    status: MasterStatus;
    parentId: string;
    parentType: MasterType.CITY;
    locationCode?: string;
    coordinates?: [number, number];
    address?: string;
    pincode?: string;
    landmark?: string;
    alternateNames?: string[];
    locationType?: string;
    locationCategory?: string;
    connectivity?: {
        nearestMetroStation?: string;
        metroDistance?: number;
        nearestRailwayStation?: string;
        railwayDistance?: number;
        nearestAirport?: string;
        airportDistance?: number;
        majorRoads?: string[];
        busConnectivity?: string[];
    };
    realEstateData?: {
        averagePropertyPrice?: number;
        priceRange?: {
            min: number;
            max: number;
        };
        priceAppreciationRate?: number;
        rentalYield?: number;
        popularPropertyTypes?: string[];
        upcomingProjects?: number;
        totalProjects?: number;
        occupancyRate?: number;
    };
    nearbyFacilities?: {
        schools?: string[];
        hospitals?: string[];
        shoppingMalls?: string[];
        restaurants?: string[];
        banks?: string[];
        parks?: string[];
        gyms?: string[];
        temples?: string[];
    };
    sortOrder?: number;
    isDefault?: boolean;
    isPopular?: boolean;
    seoTitle?: string;
    seoDescription?: string;
    seoKeywords?: string[];
    featuredImage?: string;
    gallery?: string[];
    metadata?: Record<string, any>;
    createdAt: Date;
    updatedAt: Date;
    fullName?: string;
    geoLocation?: {
        type: 'Point';
        coordinates: [number, number];
    };
    parentCity?: {
        _id: string;
        name: string;
        masterType: string;
    };
}
export declare class LocationListResponseDto {
    success: boolean;
    data: LocationResponseDto[];
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
    };
    message?: string;
}
export declare class SingleLocationResponseDto {
    success: boolean;
    data: LocationResponseDto;
    message?: string;
}
