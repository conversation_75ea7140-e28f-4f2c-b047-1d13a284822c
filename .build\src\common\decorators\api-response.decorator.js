"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApiInternalServerErrorResponse = exports.ApiNotFoundResponse = exports.ApiForbiddenResponse = exports.ApiUnauthorizedResponse = exports.ApiBadRequestResponse = exports.ApiCreatedResponse = exports.ApiSuccessResponse = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const ApiSuccessResponse = (options) => (0, common_1.applyDecorators)((0, swagger_1.ApiResponse)({
    status: 200,
    description: 'Operation successful',
    ...options,
}));
exports.ApiSuccessResponse = ApiSuccessResponse;
const ApiCreatedResponse = (options) => (0, common_1.applyDecorators)((0, swagger_1.ApiResponse)({
    status: 201,
    description: 'Resource created successfully',
    ...options,
}));
exports.ApiCreatedResponse = ApiCreatedResponse;
const ApiBadRequestResponse = (description) => (0, common_1.applyDecorators)((0, swagger_1.ApiResponse)({
    status: 400,
    description: description || 'Bad request - Invalid input data',
    schema: {
        type: 'object',
        properties: {
            statusCode: { type: 'number', example: 400 },
            message: {
                oneOf: [
                    { type: 'string' },
                    { type: 'array', items: { type: 'string' } }
                ]
            },
            error: { type: 'string', example: 'Bad Request' },
        },
    },
}));
exports.ApiBadRequestResponse = ApiBadRequestResponse;
const ApiUnauthorizedResponse = (description) => (0, common_1.applyDecorators)((0, swagger_1.ApiResponse)({
    status: 401,
    description: description || 'Unauthorized - Invalid or missing authentication',
    schema: {
        type: 'object',
        properties: {
            statusCode: { type: 'number', example: 401 },
            message: { type: 'string', example: 'Unauthorized' },
            error: { type: 'string', example: 'Unauthorized' },
        },
    },
}));
exports.ApiUnauthorizedResponse = ApiUnauthorizedResponse;
const ApiForbiddenResponse = (description) => (0, common_1.applyDecorators)((0, swagger_1.ApiResponse)({
    status: 403,
    description: description || 'Forbidden - Insufficient permissions',
    schema: {
        type: 'object',
        properties: {
            statusCode: { type: 'number', example: 403 },
            message: { type: 'string', example: 'Forbidden resource' },
            error: { type: 'string', example: 'Forbidden' },
        },
    },
}));
exports.ApiForbiddenResponse = ApiForbiddenResponse;
const ApiNotFoundResponse = (description) => (0, common_1.applyDecorators)((0, swagger_1.ApiResponse)({
    status: 404,
    description: description || 'Resource not found',
    schema: {
        type: 'object',
        properties: {
            statusCode: { type: 'number', example: 404 },
            message: { type: 'string', example: 'Resource not found' },
            error: { type: 'string', example: 'Not Found' },
        },
    },
}));
exports.ApiNotFoundResponse = ApiNotFoundResponse;
const ApiInternalServerErrorResponse = (description) => (0, common_1.applyDecorators)((0, swagger_1.ApiResponse)({
    status: 500,
    description: description || 'Internal server error',
    schema: {
        type: 'object',
        properties: {
            statusCode: { type: 'number', example: 500 },
            message: { type: 'string', example: 'Internal server error' },
            error: { type: 'string', example: 'Internal Server Error' },
        },
    },
}));
exports.ApiInternalServerErrorResponse = ApiInternalServerErrorResponse;
//# sourceMappingURL=api-response.decorator.js.map