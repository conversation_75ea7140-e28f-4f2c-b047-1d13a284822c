import { Model } from 'mongoose';
import { Builder, BuilderDocument } from './schemas/builder.schema';
import { CreateBuilderDto } from './dto/create-builder.dto';
import { UpdateBuilderDto } from './dto/update-builder.dto';
export declare class BuildersService {
    private builderModel;
    constructor(builderModel: Model<BuilderDocument>);
    create(createBuilderDto: CreateBuilderDto): Promise<Builder>;
    findAll(): Promise<Builder[]>;
    findById(id: string): Promise<Builder>;
    update(id: string, updateBuilderDto: UpdateBuilderDto): Promise<Builder>;
    remove(id: string): Promise<void>;
}
