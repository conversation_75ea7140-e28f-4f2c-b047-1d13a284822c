// MongoDB Initialization Script
// This script runs when the MongoDB container starts for the first time

// Switch to the admin database
db = db.getSiblingDB('admin');

// Create application user
db.createUser({
  user: 'trelax_user',
  pwd: 'trelax_password_2024',
  roles: [
    {
      role: 'readWrite',
      db: 'trelax_admin_db'
    },
    {
      role: 'readWrite',
      db: 'trelax_dev_db'
    },
    {
      role: 'readWrite',
      db: 'trelax_test_db'
    }
  ]
});

// Switch to the application database
db = db.getSiblingDB('trelax_admin_db');

// Create initial admin users
db.users.insertMany([
  {
    email: '<EMAIL>',
    password: '$2b$10$8K1p/a0dFGwAO4TtVV4My.6qvtqHdqHDUiHY7P6ztMjMQm8TtqHDU', // admin123
    role: 'ADMIN',
    firstName: 'Admin',
    lastName: 'User',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    email: '<EMAIL>',
    password: '$2b$10$8K1p/a0dFGwAO4TtVV4My.6qvtqHdqHDUiHY7P6ztMjMQm8TtqHDU', // admin123
    role: 'SUPERADMIN',
    firstName: 'Super',
    lastName: 'Admin',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    email: '<EMAIL>',
    password: '$2b$10$8K1p/a0dFGwAO4TtVV4My.6qvtqHdqHDUiHY7P6ztMjMQm8TtqHDU', // admin123
    role: 'MANAGER',
    firstName: 'Manager',
    lastName: 'User',
    isActive: true,
    createdAt: new Date(),
    updatedAt: new Date()
  }
]);

// Create indexes for better performance
db.users.createIndex({ email: 1 }, { unique: true });
db.users.createIndex({ role: 1 });
db.users.createIndex({ isActive: 1 });

// Create masters collection with indexes
db.masters.createIndex({ masterType: 1 });
db.masters.createIndex({ status: 1 });
db.masters.createIndex({ name: 1 });
db.masters.createIndex({ parentId: 1 });
db.masters.createIndex({ locationCode: 1, parentId: 1 }, { unique: true, sparse: true });
db.masters.createIndex({ coordinates: '2dsphere' });

// Create projects collection with indexes
db.projects.createIndex({ projectName: 1 });
db.projects.createIndex({ projectStatus: 1 });
db.projects.createIndex({ propertyType: 1 });
db.projects.createIndex({ 'location.cityId': 1 });
db.projects.createIndex({ 'location.locationId': 1 });
db.projects.createIndex({ 'location.coordinates': '2dsphere' });
db.projects.createIndex({ reraNumber: 1 }, { unique: true, sparse: true });
db.projects.createIndex({ isActive: 1 });
db.projects.createIndex({ isFeatured: 1 });
db.projects.createIndex({ priceMin: 1, priceMax: 1 });

// Create builders collection with indexes
db.builders.createIndex({ name: 1 });
db.builders.createIndex({ contactEmail: 1 }, { unique: true });
db.builders.createIndex({ isActive: 1 });

// Create agents collection with indexes
db.agents.createIndex({ email: 1 }, { unique: true });
db.agents.createIndex({ licenseNumber: 1 }, { unique: true });
db.agents.createIndex({ firstName: 1, lastName: 1 });
db.agents.createIndex({ experience: 1 });
db.agents.createIndex({ isActive: 1 });

// Create files collection with indexes
db.files.createIndex({ key: 1 }, { unique: true });
db.files.createIndex({ folder: 1 });
db.files.createIndex({ mimetype: 1 });
db.files.createIndex({ uploadedAt: 1 });

print('✅ Database initialization completed successfully');
print('📊 Created collections: users, masters, projects, builders, agents, files');
print('🔐 Created admin users with default credentials');
print('📈 Created performance indexes');
print('🎉 TrelaX Admin Database is ready!');
