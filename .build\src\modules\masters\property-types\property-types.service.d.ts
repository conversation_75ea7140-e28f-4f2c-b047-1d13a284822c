import { Model } from 'mongoose';
import { PropertyType, PropertyTypeDocument } from './schemas/property-type.schema';
import { BaseMasterService } from '../common/services/base-master.service';
import { PropertyTypeCategory } from '../common/enums/master-types.enum';
import { CreatePropertyTypeDto } from './dto/create-property-type.dto';
import { UpdatePropertyTypeDto } from './dto/update-property-type.dto';
import { QueryMasterWithCategoryDto } from '../common/dto/query-master.dto';
import { MasterListResponse } from '../common/interfaces/base-master.interface';
export declare class PropertyTypesService extends BaseMasterService<PropertyType> {
    private propertyTypeModel;
    constructor(propertyTypeModel: Model<PropertyTypeDocument>);
    createPropertyType(createPropertyTypeDto: CreatePropertyTypeDto): Promise<PropertyType>;
    findAllPropertyTypes(queryDto?: QueryMasterWithCategoryDto): Promise<MasterListResponse<PropertyType>>;
    findPropertyTypeById(id: string): Promise<PropertyType>;
    updatePropertyType(id: string, updatePropertyTypeDto: UpdatePropertyTypeDto): Promise<PropertyType>;
    removePropertyType(id: string): Promise<void>;
    findPropertyTypesByCategory(category: PropertyTypeCategory): Promise<PropertyType[]>;
    findResidentialPropertyTypes(): Promise<PropertyType[]>;
    findCommercialPropertyTypes(): Promise<PropertyType[]>;
    getPropertyTypeStatistics(): Promise<{
        byCategory: any;
        byPopularityRating: any;
        bySuitability: any;
        totalCount: number;
        activeCount: number;
        inactiveCount: number;
        popularCount: number;
        defaultCount: number;
        byStatus?: Record<string, number>;
    }>;
    protected checkUsageBeforeDelete(id: string): Promise<void>;
}
