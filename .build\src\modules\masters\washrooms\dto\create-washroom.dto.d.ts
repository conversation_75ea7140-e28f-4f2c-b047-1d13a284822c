import { MasterWithNumericValueDto } from '../../common/dto/base-master.dto';
import { MasterType } from '../../common/enums/master-types.enum';
export declare class WashroomSpecificationsDto {
    hasShower?: boolean;
    hasBathtub?: boolean;
    hasGeyser?: boolean;
    hasExhaustFan?: boolean;
    hasWindow?: boolean;
    fittingsQuality?: string;
}
export declare class CreateWashroomDto extends MasterWithNumericValueDto {
    name: string;
    description?: string;
    code?: string;
    numericValue: number;
    unit?: string;
    washroomType?: string;
    features?: string[];
    typicalArea?: number;
    popularityRating?: number;
    specifications?: WashroomSpecificationsDto;
    masterType: MasterType.WASHROOM;
}
