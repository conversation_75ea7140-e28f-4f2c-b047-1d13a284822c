"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PropertyTypesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const property_types_service_1 = require("./property-types.service");
const create_property_type_dto_1 = require("./dto/create-property-type.dto");
const update_property_type_dto_1 = require("./dto/update-property-type.dto");
const query_master_dto_1 = require("../common/dto/query-master.dto");
const master_types_enum_1 = require("../common/enums/master-types.enum");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let PropertyTypesController = class PropertyTypesController {
    constructor(propertyTypesService) {
        this.propertyTypesService = propertyTypesService;
    }
    async create(createPropertyTypeDto) {
        const propertyType = await this.propertyTypesService.createPropertyType(createPropertyTypeDto);
        return {
            success: true,
            data: propertyType,
            message: 'Property type created successfully'
        };
    }
    async findAll(queryDto) {
        return await this.propertyTypesService.findAllPropertyTypes(queryDto);
    }
    async getStatistics() {
        const statistics = await this.propertyTypesService.getPropertyTypeStatistics();
        return {
            success: true,
            data: statistics,
            message: 'Property type statistics retrieved successfully'
        };
    }
    async findByCategory(category) {
        const propertyTypes = await this.propertyTypesService.findPropertyTypesByCategory(category);
        return {
            success: true,
            data: propertyTypes,
            message: `${category} property types retrieved successfully`
        };
    }
    async findResidential() {
        const propertyTypes = await this.propertyTypesService.findResidentialPropertyTypes();
        return {
            success: true,
            data: propertyTypes,
            message: 'Residential property types retrieved successfully'
        };
    }
    async findCommercial() {
        const propertyTypes = await this.propertyTypesService.findCommercialPropertyTypes();
        return {
            success: true,
            data: propertyTypes,
            message: 'Commercial property types retrieved successfully'
        };
    }
    async findOne(id) {
        const propertyType = await this.propertyTypesService.findPropertyTypeById(id);
        return {
            success: true,
            data: propertyType,
            message: 'Property type retrieved successfully'
        };
    }
    async update(id, updatePropertyTypeDto) {
        const propertyType = await this.propertyTypesService.updatePropertyType(id, updatePropertyTypeDto);
        return {
            success: true,
            data: propertyType,
            message: 'Property type updated successfully'
        };
    }
    async remove(id) {
        await this.propertyTypesService.removePropertyType(id);
        return {
            success: true,
            message: 'Property type deleted successfully'
        };
    }
};
exports.PropertyTypesController = PropertyTypesController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new property type' }),
    (0, swagger_1.ApiBody)({ type: create_property_type_dto_1.CreatePropertyTypeDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.CREATED, description: 'Property type created successfully' }),
    __param(0, (0, common_1.Body)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_property_type_dto_1.CreatePropertyTypeDto]),
    __metadata("design:returntype", Promise)
], PropertyTypesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all property types' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: 'Property types retrieved successfully' }),
    __param(0, (0, common_1.Query)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [query_master_dto_1.QueryMasterWithCategoryDto]),
    __metadata("design:returntype", Promise)
], PropertyTypesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('statistics'),
    (0, swagger_1.ApiOperation)({ summary: 'Get property type statistics' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: 'Property type statistics retrieved successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PropertyTypesController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)('category/:category'),
    (0, swagger_1.ApiOperation)({ summary: 'Get property types by category' }),
    (0, swagger_1.ApiParam)({ name: 'category', enum: master_types_enum_1.PropertyTypeCategory, example: master_types_enum_1.PropertyTypeCategory.RESIDENTIAL }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: 'Property types retrieved successfully' }),
    __param(0, (0, common_1.Param)('category')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PropertyTypesController.prototype, "findByCategory", null);
__decorate([
    (0, common_1.Get)('residential'),
    (0, swagger_1.ApiOperation)({ summary: 'Get residential property types' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: 'Residential property types retrieved successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PropertyTypesController.prototype, "findResidential", null);
__decorate([
    (0, common_1.Get)('commercial'),
    (0, swagger_1.ApiOperation)({ summary: 'Get commercial property types' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: 'Commercial property types retrieved successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], PropertyTypesController.prototype, "findCommercial", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get property type by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Property type ID' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: 'Property type retrieved successfully' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PropertyTypesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update property type by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Property type ID' }),
    (0, swagger_1.ApiBody)({ type: update_property_type_dto_1.UpdatePropertyTypeDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: 'Property type updated successfully' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_property_type_dto_1.UpdatePropertyTypeDto]),
    __metadata("design:returntype", Promise)
], PropertyTypesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete property type by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Property type ID' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: 'Property type deleted successfully' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], PropertyTypesController.prototype, "remove", null);
exports.PropertyTypesController = PropertyTypesController = __decorate([
    (0, swagger_1.ApiTags)('Masters - Property Types'),
    (0, common_1.Controller)('masters/property-types'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [property_types_service_1.PropertyTypesService])
], PropertyTypesController);
//# sourceMappingURL=property-types.controller.js.map