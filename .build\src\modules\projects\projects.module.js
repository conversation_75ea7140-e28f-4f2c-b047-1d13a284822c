"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectsModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const projects_controller_1 = require("./projects.controller");
const projects_service_1 = require("./projects.service");
const project_schema_1 = require("./schemas/project.schema");
const s3_service_1 = require("../files/services/s3.service");
const city_schema_1 = require("../masters/cities/schemas/city.schema");
const location_schema_1 = require("../masters/locations/schemas/location.schema");
const amenity_schema_1 = require("../masters/amenities/schemas/amenity.schema");
let ProjectsModule = class ProjectsModule {
};
exports.ProjectsModule = ProjectsModule;
exports.ProjectsModule = ProjectsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forFeature([
                { name: project_schema_1.Project.name, schema: project_schema_1.ProjectSchema },
                { name: city_schema_1.City.name, schema: city_schema_1.CitySchema },
                { name: location_schema_1.Location.name, schema: location_schema_1.LocationSchema },
                { name: amenity_schema_1.Amenity.name, schema: amenity_schema_1.AmenitySchema }
            ]),
        ],
        controllers: [projects_controller_1.ProjectsController],
        providers: [projects_service_1.ProjectsService, s3_service_1.S3Service],
        exports: [projects_service_1.ProjectsService],
    })
], ProjectsModule);
//# sourceMappingURL=projects.module.js.map