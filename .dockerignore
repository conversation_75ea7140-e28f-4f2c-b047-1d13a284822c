# Docker ignore file for TrelaX Admin Backend
# This file specifies which files and directories should be excluded from the Docker build context

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.docker.local

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov/

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Test results
test-results/
performance-results/

# Uploads directory (will be mounted as volume)
uploads/

# Git
.git/
.gitignore
.gitattributes

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Documentation
*.md
docs/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
tmp/
temp/

# Build artifacts
dist/
build/

# Package manager lock files (keep package-lock.json for reproducible builds)
yarn.lock

# Local development files
.env.local
.env.development.local
.env.test.local
.env.production.local

# Database files
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup

# Archive files
*.zip
*.tar.gz
*.rar

# Docker volumes data
mongodb_data/
redis_data/
localstack_data/

# Scripts (will be copied separately if needed)
docker-manager.sh
docker-manager.bat

# Configuration files that shouldn't be in container
docker/

# Test files (will be copied in development stage)
# test/ (commented out as we need tests in development)

# Seeder files (will be copied separately if needed)
# src/database/seeders/ (commented out as we might need seeders)

# Local configuration overrides
config/local.js
config/local.json

# Certificates and keys
*.pem
*.key
*.crt
*.csr

# Temporary directories
.tmp/
.temp/

# Editor directories and files
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# MacOS
.DS_Store

# Windows
Thumbs.db
ehthumbs.db
Desktop.ini

# Linux
*~

# Vim
*.swp
*.swo

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# JetBrains IDEs
.idea/
*.iml
*.ipr
*.iws

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Sublime Text
*.sublime-project
*.sublime-workspace

# Atom
.atom/

# Local History for Visual Studio Code
.history/

# Built Visual Studio Code Extensions
*.vsix
