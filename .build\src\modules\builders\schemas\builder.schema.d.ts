import { Document } from 'mongoose';
export type BuilderDocument = Builder & Document;
export declare class Builder {
    name: string;
    description?: string;
    website?: string;
    contactEmail?: string;
    contactPhone?: string;
    logo?: string;
    createdAt?: Date;
    updatedAt?: Date;
}
export declare const BuilderSchema: import("mongoose").Schema<Builder, import("mongoose").Model<Builder, any, any, any, Document<unknown, any, Builder, any> & Builder & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Builder, Document<unknown, {}, import("mongoose").FlatRecord<Builder>, {}> & import("mongoose").FlatRecord<Builder> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
