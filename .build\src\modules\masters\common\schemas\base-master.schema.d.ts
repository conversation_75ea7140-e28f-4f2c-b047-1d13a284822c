import { Document, Types } from 'mongoose';
import { MasterStatus, MasterType } from '../enums/master-types.enum';
export declare class BaseMaster {
    name: string;
    description?: string;
    code?: string;
    masterType: MasterType;
    status: MasterStatus;
    sortOrder?: number;
    isDefault?: boolean;
    isPopular?: boolean;
    metadata?: Record<string, any>;
    createdAt?: Date;
    updatedAt?: Date;
}
export type BaseMasterDocument = BaseMaster & Document;
export declare class MasterWithParent extends BaseMaster {
    parentId?: Types.ObjectId;
    parentType?: MasterType;
}
export declare class MasterWithCategory extends BaseMaster {
    category?: string;
    icon?: string;
    color?: string;
}
export declare class MasterWithNumericValue extends BaseMaster {
    numericValue?: number;
    unit?: string;
    minValue?: number;
    maxValue?: number;
}
export declare class MasterWithLocation extends BaseMaster {
    state?: string;
    country?: string;
    coordinates?: [number, number];
    timezone?: string;
    pinCodes?: string[];
}
export declare class CombinedMaster extends BaseMaster {
    parentId?: Types.ObjectId;
    parentType?: MasterType;
    category?: string;
    icon?: string;
    color?: string;
    numericValue?: number;
    unit?: string;
    minValue?: number;
    maxValue?: number;
    state?: string;
    country?: string;
    coordinates?: [number, number];
    timezone?: string;
    pinCodes?: string[];
}
export type CombinedMasterDocument = CombinedMaster & Document;
