"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WashroomsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const washrooms_service_1 = require("./washrooms.service");
const create_washroom_dto_1 = require("./dto/create-washroom.dto");
const update_washroom_dto_1 = require("./dto/update-washroom.dto");
const query_master_dto_1 = require("../common/dto/query-master.dto");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let WashroomsController = class WashroomsController {
    constructor(washroomsService) {
        this.washroomsService = washroomsService;
    }
    async create(createWashroomDto) {
        const washroom = await this.washroomsService.createWashroom(createWashroomDto);
        return {
            success: true,
            data: washroom,
            message: 'Washroom configuration created successfully'
        };
    }
    async findAll(queryDto) {
        return await this.washroomsService.findAllWashrooms(queryDto);
    }
    async getStatistics() {
        const statistics = await this.washroomsService.getWashroomStatistics();
        return {
            success: true,
            data: statistics,
            message: 'Washroom statistics retrieved successfully'
        };
    }
    async findByType(type) {
        const washrooms = await this.washroomsService.findWashroomsByType(type);
        return {
            success: true,
            data: washrooms,
            message: `${type} washrooms retrieved successfully`
        };
    }
    async findOne(id) {
        const washroom = await this.washroomsService.findWashroomById(id);
        return {
            success: true,
            data: washroom,
            message: 'Washroom configuration retrieved successfully'
        };
    }
    async update(id, updateWashroomDto) {
        const washroom = await this.washroomsService.updateWashroom(id, updateWashroomDto);
        return {
            success: true,
            data: washroom,
            message: 'Washroom configuration updated successfully'
        };
    }
    async remove(id) {
        await this.washroomsService.removeWashroom(id);
        return {
            success: true,
            message: 'Washroom configuration deleted successfully'
        };
    }
};
exports.WashroomsController = WashroomsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new washroom configuration' }),
    (0, swagger_1.ApiBody)({ type: create_washroom_dto_1.CreateWashroomDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.CREATED, description: 'Washroom configuration created successfully' }),
    __param(0, (0, common_1.Body)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_washroom_dto_1.CreateWashroomDto]),
    __metadata("design:returntype", Promise)
], WashroomsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all washroom configurations' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: 'Washroom configurations retrieved successfully' }),
    __param(0, (0, common_1.Query)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [query_master_dto_1.QueryMasterWithNumericRangeDto]),
    __metadata("design:returntype", Promise)
], WashroomsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('statistics'),
    (0, swagger_1.ApiOperation)({ summary: 'Get washroom statistics' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: 'Washroom statistics retrieved successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], WashroomsController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)('type/:type'),
    (0, swagger_1.ApiOperation)({ summary: 'Get washrooms by type' }),
    (0, swagger_1.ApiParam)({ name: 'type', description: 'Washroom type', example: 'attached' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: 'Washrooms retrieved successfully' }),
    __param(0, (0, common_1.Param)('type')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], WashroomsController.prototype, "findByType", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get washroom configuration by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Washroom ID' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: 'Washroom configuration retrieved successfully' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], WashroomsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update washroom configuration by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Washroom ID' }),
    (0, swagger_1.ApiBody)({ type: update_washroom_dto_1.UpdateWashroomDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: 'Washroom configuration updated successfully' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_washroom_dto_1.UpdateWashroomDto]),
    __metadata("design:returntype", Promise)
], WashroomsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete washroom configuration by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Washroom ID' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: 'Washroom configuration deleted successfully' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], WashroomsController.prototype, "remove", null);
exports.WashroomsController = WashroomsController = __decorate([
    (0, swagger_1.ApiTags)('Masters - Washrooms'),
    (0, common_1.Controller)('masters/washrooms'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [washrooms_service_1.WashroomsService])
], WashroomsController);
//# sourceMappingURL=washrooms.controller.js.map