{"AWSTemplateFormatVersion": "2010-09-09", "Description": "The AWS CloudFormation template for this Serverless application", "Resources": {"ServerlessDeploymentBucket": {"Type": "AWS::S3::<PERSON><PERSON>", "Properties": {"BucketEncryption": {"ServerSideEncryptionConfiguration": [{"ServerSideEncryptionByDefault": {"SSEAlgorithm": "AES256"}}]}}}, "ServerlessDeploymentBucketPolicy": {"Type": "AWS::S3::BucketPolicy", "Properties": {"Bucket": {"Ref": "ServerlessDeploymentBucket"}, "PolicyDocument": {"Statement": [{"Action": "s3:*", "Effect": "<PERSON><PERSON>", "Principal": "*", "Resource": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":s3:::", {"Ref": "ServerlessDeploymentBucket"}, "/*"]]}, {"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":s3:::", {"Ref": "ServerlessDeploymentBucket"}]]}], "Condition": {"Bool": {"aws:SecureTransport": false}}}]}}}}, "Outputs": {"ServerlessDeploymentBucketName": {"Value": {"Ref": "ServerlessDeploymentBucket"}}}}