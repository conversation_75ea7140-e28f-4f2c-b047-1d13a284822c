{"version": 3, "file": "files.service.js", "sourceRoot": "", "sources": ["../../../../src/modules/files/files.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAoF;AACpF,+CAA+C;AAC/C,uCAAwC;AACxC,uDAA2D;AAG3D,sDAAkD;AAO3C,IAAM,YAAY,GAAlB,MAAM,YAAY;IACvB,YACkC,SAA8B,EAC7C,SAAoB;QADL,cAAS,GAAT,SAAS,CAAqB;QAC7C,cAAS,GAAT,SAAS,CAAW;IACpC,CAAC;IAKJ,KAAK,CAAC,UAAU,CACd,IAAS,EACT,aAA4B,EAC5B,MAAc,EACd,SAAiB,SAAS;QAE1B,IAAI,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,CAC9C,IAAI,EACJ,MAAM,EACN,aAAa,CAAC,QAAQ,IAAI,KAAK,CAChC,CAAC;YAGF,MAAM,YAAY,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC;gBACtC,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE;gBACvC,YAAY,EAAE,IAAI,CAAC,YAAY;gBAC/B,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,GAAG,EAAE,QAAQ,CAAC,GAAG;gBACjB,GAAG,EAAE,QAAQ,CAAC,GAAG;gBACjB,MAAM,EAAE,QAAQ,CAAC,MAAM;gBACvB,WAAW,EAAE,aAAa,CAAC,WAAW;gBACtC,IAAI,EAAE,aAAa,CAAC,IAAI,IAAI,EAAE;gBAC9B,UAAU,EAAE,IAAI,gBAAK,CAAC,QAAQ,CAAC,MAAM,CAAC;gBACtC,QAAQ,EAAE,aAAa,CAAC,QAAQ,IAAI,KAAK;aAC1C,CAAC,CAAC;YAEH,OAAO,MAAM,YAAY,CAAC,IAAI,EAAE,CAAC;QACnC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,4BAAmB,CAAC,uBAAuB,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,mBAAmB,CACvB,KAAY,EACZ,aAA4B,EAC5B,MAAc,EACd,SAAiB,SAAS;QAE1B,MAAM,cAAc,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CACtC,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,aAAa,EAAE,MAAM,EAAE,MAAM,CAAC,CACrD,CAAC;QAEF,OAAO,MAAM,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IAC3C,CAAC;IAKD,KAAK,CAAC,OAAO,CAAC,QAAsB,EAAE,MAAe;QACnD,MAAM,EACJ,IAAI,EACJ,KAAK,EACL,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,MAAM,EACN,SAAS,GACV,GAAG,QAAQ,CAAC;QAGb,MAAM,MAAM,GAAQ,EAAE,CAAC;QAEvB,IAAI,MAAM,EAAE,CAAC;YACX,MAAM,CAAC,GAAG,GAAG;gBACX,EAAE,QAAQ,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,EAAE;gBAC/C,EAAE,YAAY,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,EAAE;gBACnD,EAAE,WAAW,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,EAAE;aACnD,CAAC;QACJ,CAAC;QAED,IAAI,QAAQ,EAAE,CAAC;YACb,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC7B,CAAC;QAED,IAAI,OAAO,QAAQ,KAAK,SAAS,EAAE,CAAC;YAClC,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC7B,CAAC;QAED,IAAI,OAAO,QAAQ,KAAK,SAAS,EAAE,CAAC;YAClC,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC7B,CAAC;QAED,IAAI,UAAU,EAAE,CAAC;YACf,MAAM,CAAC,UAAU,GAAG,IAAI,gBAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QACrD,CAAC;QAGD,IAAI,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;YAC1B,MAAM,CAAC,GAAG,GAAG;gBACX,EAAE,UAAU,EAAE,IAAI,gBAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;gBAC1C,EAAE,QAAQ,EAAE,IAAI,EAAE;aACnB,CAAC;QACJ,CAAC;QAGD,MAAM,IAAI,GAAQ,EAAE,CAAC;QACrB,IAAI,CAAC,MAAM,CAAC,GAAG,SAAS,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAG5C,MAAM,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QAGhC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACvC,IAAI,CAAC,SAAS;iBACX,IAAI,CAAC,MAAM,CAAC;iBACZ,QAAQ,CAAC,YAAY,EAAE,0BAA0B,CAAC;iBAClD,IAAI,CAAC,IAAI,CAAC;iBACV,IAAI,CAAC,IAAI,CAAC;iBACV,KAAK,CAAC,KAAK,CAAC;iBACZ,IAAI,EAAE;YACT,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,MAAM,CAAC;SACtC,CAAC,CAAC;QAEH,OAAO;YACL,KAAK;YACL,UAAU,EAAE;gBACV,IAAI;gBACJ,KAAK;gBACL,KAAK;gBACL,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;gBACpC,OAAO,EAAE,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;gBACxC,OAAO,EAAE,IAAI,GAAG,CAAC;aAClB;SACF,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,QAAQ,CAAC,EAAU,EAAE,MAAe;QACxC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,SAAS;iBAC9B,QAAQ,CAAC,EAAE,CAAC;iBACZ,QAAQ,CAAC,YAAY,EAAE,0BAA0B,CAAC;iBAClD,IAAI,EAAE,CAAC;YAEV,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,0BAAiB,CAAC,gBAAgB,CAAC,CAAC;YAChD,CAAC;YAGD,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,KAAK,MAAM,EAAE,CAAC;gBACtE,MAAM,IAAI,0BAAiB,CAAC,gBAAgB,CAAC,CAAC;YAChD,CAAC;YAGD,MAAM,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAAE,EAAE;gBACzC,cAAc,EAAE,IAAI,IAAI,EAAE;gBAC1B,IAAI,EAAE,EAAE,aAAa,EAAE,CAAC,EAAE;aAC3B,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,0BAAiB,EAAE,CAAC;gBACvC,MAAM,KAAK,CAAC;YACd,CAAC;YACD,MAAM,IAAI,4BAAmB,CAAC,iBAAiB,CAAC,CAAC;QACnD,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,cAAc,CAAC,EAAU,EAAE,MAAe;QAC9C,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;QAC7C,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IACxD,CAAC;IAKD,KAAK,CAAC,MAAM,CAAC,EAAU,EAAE,MAAc;QACrC,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YAE/C,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,0BAAiB,CAAC,gBAAgB,CAAC,CAAC;YAChD,CAAC;YAGD,IAAI,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,KAAK,MAAM,EAAE,CAAC;gBAC1C,MAAM,IAAI,4BAAmB,CAAC,oCAAoC,CAAC,CAAC;YACtE,CAAC;YAGD,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAG1C,MAAM,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,0BAAiB,IAAI,KAAK,YAAY,4BAAmB,EAAE,CAAC;gBAC/E,MAAM,KAAK,CAAC;YACd,CAAC;YACD,MAAM,IAAI,4BAAmB,CAAC,uBAAuB,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,aAAa,CAAC,MAAe;QACjC,MAAM,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,IAAI,gBAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAExE,MAAM,CACJ,UAAU,EACV,WAAW,EACX,aAAa,EACb,WAAW,EACX,YAAY,EACZ,aAAa,EACb,SAAS,EACV,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACpB,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,MAAM,CAAC;YACrC,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE,GAAG,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;YAC5D,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE,GAAG,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;YAC7D,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE,GAAG,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;YAC5D,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,EAAE,GAAG,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,CAAC;YAC7D,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;gBACvB,EAAE,MAAM,EAAE,MAAM,EAAE;gBAClB,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE,WAAW,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE;aACrD,CAAC;YACF,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;gBACvB,EAAE,MAAM,EAAE,MAAM,EAAE;gBAClB,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE;aACxD,CAAC;SACH,CAAC,CAAC;QAEH,OAAO;YACL,UAAU;YACV,WAAW;YACX,aAAa;YACb,WAAW;YACX,YAAY;YACZ,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,SAAS,IAAI,CAAC;YACvC,oBAAoB,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;gBACvD,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;gBAC3B,OAAO,GAAG,CAAC;YACb,CAAC,EAAE,EAAE,CAAC;SACP,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,kBAAkB,CAAC,QAAgB,EAAE,QAAgB,EAAE;QAC3D,OAAO,MAAM,IAAI,CAAC,SAAS;aACxB,IAAI,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;aAClD,QAAQ,CAAC,YAAY,EAAE,0BAA0B,CAAC;aAClD,IAAI,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC;aACvB,KAAK,CAAC,KAAK,CAAC;aACZ,IAAI,EAAE,CAAC;IACZ,CAAC;IAKD,KAAK,CAAC,YAAY,CAAC,IAAc,EAAE,QAAgB,EAAE;QACnD,OAAO,MAAM,IAAI,CAAC,SAAS;aACxB,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC;aAC7D,QAAQ,CAAC,YAAY,EAAE,0BAA0B,CAAC;aAClD,IAAI,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC;aACvB,KAAK,CAAC,KAAK,CAAC;aACZ,IAAI,EAAE,CAAC;IACZ,CAAC;CACF,CAAA;AAzRY,oCAAY;uBAAZ,YAAY;IADxB,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,sBAAW,EAAC,kBAAI,CAAC,IAAI,CAAC,CAAA;qCAAoB,gBAAK;QACpB,sBAAS;GAH5B,YAAY,CAyRxB"}