"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreatePropertyTypeDto = exports.PriceRangeDto = exports.PropertySpecificationsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const base_master_dto_1 = require("../../common/dto/base-master.dto");
const master_types_enum_1 = require("../../common/enums/master-types.enum");
class PropertySpecificationsDto {
}
exports.PropertySpecificationsDto = PropertySpecificationsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Minimum area in sq ft', example: 500 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], PropertySpecificationsDto.prototype, "minArea", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Maximum area in sq ft', example: 2000 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], PropertySpecificationsDto.prototype, "maxArea", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Typical number of floors', example: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], PropertySpecificationsDto.prototype, "typicalFloors", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of parking spaces', example: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], PropertySpecificationsDto.prototype, "parkingSpaces", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of balconies', example: 2 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], PropertySpecificationsDto.prototype, "balconies", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of bathrooms', example: 2 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], PropertySpecificationsDto.prototype, "bathrooms", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of bedrooms', example: 2 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], PropertySpecificationsDto.prototype, "bedrooms", void 0);
class PriceRangeDto {
    constructor() {
        this.currency = 'INR';
    }
}
exports.PriceRangeDto = PriceRangeDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Minimum price', example: 5000000 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], PriceRangeDto.prototype, "min", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Maximum price', example: 15000000 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], PriceRangeDto.prototype, "max", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Currency', example: 'INR', default: 'INR' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(10),
    __metadata("design:type", String)
], PriceRangeDto.prototype, "currency", void 0);
class CreatePropertyTypeDto extends base_master_dto_1.MasterWithCategoryDto {
    constructor() {
        super(...arguments);
        this.masterType = master_types_enum_1.MasterType.PROPERTY_TYPE;
    }
}
exports.CreatePropertyTypeDto = CreatePropertyTypeDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Property type name', example: '2 BHK Apartment' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CreatePropertyTypeDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Property type description' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(500),
    __metadata("design:type", String)
], CreatePropertyTypeDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Property type code', example: '2BHK' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(20),
    __metadata("design:type", String)
], CreatePropertyTypeDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Property type category',
        enum: master_types_enum_1.PropertyTypeCategory,
        example: master_types_enum_1.PropertyTypeCategory.RESIDENTIAL
    }),
    (0, class_validator_1.IsEnum)(master_types_enum_1.PropertyTypeCategory),
    __metadata("design:type", String)
], CreatePropertyTypeDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Icon identifier', example: 'apartment' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(50),
    __metadata("design:type", String)
], CreatePropertyTypeDto.prototype, "icon", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Color code', example: '#4CAF50' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(10),
    __metadata("design:type", String)
], CreatePropertyTypeDto.prototype, "color", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Suitable for',
        example: ['investment', 'residence', 'rental']
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreatePropertyTypeDto.prototype, "suitableFor", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ type: PropertySpecificationsDto }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => PropertySpecificationsDto),
    __metadata("design:type", PropertySpecificationsDto)
], CreatePropertyTypeDto.prototype, "specifications", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Property features',
        example: ['balcony', 'parking', 'garden', 'security']
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreatePropertyTypeDto.prototype, "features", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ type: PriceRangeDto }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => PriceRangeDto),
    __metadata("design:type", PriceRangeDto)
], CreatePropertyTypeDto.prototype, "priceRange", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Popularity rating (1-5)',
        example: 4,
        minimum: 1,
        maximum: 5
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(5),
    __metadata("design:type", Number)
], CreatePropertyTypeDto.prototype, "popularityRating", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Target audience',
        example: ['families', 'young_professionals', 'investors']
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreatePropertyTypeDto.prototype, "targetAudience", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Legal requirements',
        example: ['RERA registration', 'Occupancy certificate']
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreatePropertyTypeDto.prototype, "legalRequirements", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'SEO title' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(200),
    __metadata("design:type", String)
], CreatePropertyTypeDto.prototype, "seoTitle", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'SEO description' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(500),
    __metadata("design:type", String)
], CreatePropertyTypeDto.prototype, "seoDescription", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Featured image URL' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreatePropertyTypeDto.prototype, "featuredImage", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Gallery image URLs' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsUrl)({}, { each: true }),
    __metadata("design:type", Array)
], CreatePropertyTypeDto.prototype, "gallery", void 0);
//# sourceMappingURL=create-property-type.dto.js.map