"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CitySchema = exports.City = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const base_master_schema_1 = require("../../common/schemas/base-master.schema");
const master_types_enum_1 = require("../../common/enums/master-types.enum");
let City = class City extends base_master_schema_1.MasterWithLocation {
};
exports.City = City;
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 100,
        index: true
    }),
    __metadata("design:type", String)
], City.prototype, "state", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 100,
        index: true,
        default: 'India'
    }),
    __metadata("design:type", String)
], City.prototype, "country", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: true,
        type: String,
        enum: Object.values(master_types_enum_1.MasterType),
        default: master_types_enum_1.MasterType.CITY,
        immutable: true
    }),
    __metadata("design:type", String)
], City.prototype, "masterType", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 20
    }),
    __metadata("design:type", String)
], City.prototype, "stateCode", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 10
    }),
    __metadata("design:type", String)
], City.prototype, "countryCode", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        min: 0
    }),
    __metadata("design:type", Number)
], City.prototype, "population", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        min: 0
    }),
    __metadata("design:type", Number)
], City.prototype, "area", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 100
    }),
    __metadata("design:type", String)
], City.prototype, "majorLanguage", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: [String],
        default: []
    }),
    __metadata("design:type", Array)
], City.prototype, "alternateNames", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: Object,
        default: {}
    }),
    __metadata("design:type", Object)
], City.prototype, "economicData", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: Object,
        default: {}
    }),
    __metadata("design:type", Object)
], City.prototype, "climateData", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: [String],
        default: []
    }),
    __metadata("design:type", Array)
], City.prototype, "nearbyAirports", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: [String],
        default: []
    }),
    __metadata("design:type", Array)
], City.prototype, "majorRailwayStations", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: [String],
        default: []
    }),
    __metadata("design:type", Array)
], City.prototype, "highways", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: Object,
        default: {}
    }),
    __metadata("design:type", Object)
], City.prototype, "realEstateData", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 100
    }),
    __metadata("design:type", String)
], City.prototype, "district", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 100
    }),
    __metadata("design:type", String)
], City.prototype, "division", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: [String],
        default: []
    }),
    __metadata("design:type", Array)
], City.prototype, "neighboringCities", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 200
    }),
    __metadata("design:type", String)
], City.prototype, "seoTitle", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 500
    }),
    __metadata("design:type", String)
], City.prototype, "seoDescription", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: [String],
        default: []
    }),
    __metadata("design:type", Array)
], City.prototype, "seoKeywords", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true
    }),
    __metadata("design:type", String)
], City.prototype, "featuredImage", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: [String],
        default: []
    }),
    __metadata("design:type", Array)
], City.prototype, "gallery", void 0);
exports.City = City = __decorate([
    (0, mongoose_1.Schema)({
        timestamps: true,
        collection: 'masters',
        discriminatorKey: 'masterType'
    })
], City);
exports.CitySchema = mongoose_1.SchemaFactory.createForClass(City);
exports.CitySchema.index({ name: 1, state: 1 }, { unique: true });
exports.CitySchema.index({ code: 1 }, { unique: true, sparse: true });
exports.CitySchema.index({ state: 1, country: 1 });
exports.CitySchema.index({ isPopular: 1, status: 1 });
exports.CitySchema.index({ coordinates: '2dsphere' });
exports.CitySchema.index({ 'realEstateData.averagePropertyPrice': 1 });
exports.CitySchema.index({
    name: 'text',
    description: 'text',
    state: 'text',
    alternateNames: 'text',
    seoKeywords: 'text'
}, {
    weights: {
        name: 10,
        alternateNames: 8,
        state: 5,
        description: 3,
        seoKeywords: 2
    },
    name: 'city_text_index'
});
exports.CitySchema.pre('save', function (next) {
    if (this.isNew) {
        this.masterType = master_types_enum_1.MasterType.CITY;
    }
    next();
});
exports.CitySchema.virtual('fullName').get(function () {
    return `${this.name}, ${this.state}, ${this.country}`;
});
exports.CitySchema.virtual('geoLocation').get(function () {
    if (this.coordinates && this.coordinates.length === 2) {
        return {
            type: 'Point',
            coordinates: this.coordinates
        };
    }
    return null;
});
exports.CitySchema.set('toJSON', { virtuals: true });
exports.CitySchema.set('toObject', { virtuals: true });
//# sourceMappingURL=city.schema.js.map