import { MasterStatus, MasterType } from '../../common/enums/master-types.enum';
export declare class EconomicDataResponseDto {
    gdp?: number;
    majorIndustries?: string[];
    economicGrowthRate?: number;
}
export declare class ClimateDataResponseDto {
    averageTemperature?: number;
    rainfall?: number;
    humidity?: number;
    season?: string;
}
export declare class RealEstateDataResponseDto {
    averagePropertyPrice?: number;
    priceAppreciationRate?: number;
    rentalYield?: number;
    popularAreas?: string[];
    upcomingProjects?: number;
}
export declare class CityResponseDto {
    _id: string;
    name: string;
    description?: string;
    code?: string;
    masterType: MasterType.CITY;
    status: MasterStatus;
    state: string;
    country: string;
    stateCode?: string;
    countryCode?: string;
    coordinates?: [number, number];
    timezone?: string;
    pinCodes?: string[];
    population?: number;
    area?: number;
    majorLanguage?: string;
    alternateNames?: string[];
    economicData?: EconomicDataResponseDto;
    climateData?: ClimateDataResponseDto;
    nearbyAirports?: string[];
    majorRailwayStations?: string[];
    highways?: string[];
    realEstateData?: RealEstateDataResponseDto;
    district?: string;
    division?: string;
    neighboringCities?: string[];
    sortOrder?: number;
    isDefault?: boolean;
    isPopular?: boolean;
    seoTitle?: string;
    seoDescription?: string;
    seoKeywords?: string[];
    featuredImage?: string;
    gallery?: string[];
    metadata?: Record<string, any>;
    createdAt: Date;
    updatedAt: Date;
    fullName?: string;
    geoLocation?: {
        type: 'Point';
        coordinates: [number, number];
    };
}
export declare class CityListResponseDto {
    success: boolean;
    data: CityResponseDto[];
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
    };
    message?: string;
}
export declare class SingleCityResponseDto {
    success: boolean;
    data: CityResponseDto;
    message?: string;
}
