# Lambda Environment Configuration
# This file contains environment variables for Lambda deployment

# Node Environment
NODE_ENV=production

# Database Configuration
MONGO_URI=mongodb+srv://sj317772:<EMAIL>/trelax_lambda_db?retryWrites=true&w=majority&appName=Cluster0

# JWT Configuration
JWT_SECRET=lambda_jwt_secret_key_change_in_production_2024
JWT_EXPIRES_IN=7d

# AWS Configuration
AWS_REGION=ap-south-1
AWS_S3_BUCKET=trelax-admin-uploads-dev

# API Configuration
API_PREFIX=api/v1

# CORS Configuration
CORS_ORIGIN=*

# Logging Configuration
LOG_LEVEL=info

# Lambda-specific Configuration
LAMBDA_TIMEOUT=30
LAMBDA_MEMORY_SIZE=512

# Feature Flags
ENABLE_SWAGGER=false
ENABLE_WARMUP=false
ENABLE_DEBUG_LOGS=false

# Performance Configuration
MAX_CONCURRENT_REQUESTS=100
CONNECTION_POOL_SIZE=10

# Security Configuration
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100

# File Upload Configuration
MAX_FILE_SIZE=10485760
UPLOAD_TIMEOUT=30000
