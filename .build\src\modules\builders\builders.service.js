"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BuildersService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const builder_schema_1 = require("./schemas/builder.schema");
let BuildersService = class BuildersService {
    constructor(builderModel) {
        this.builderModel = builderModel;
    }
    async create(createBuilderDto) {
        try {
            const existing = await this.builderModel.findOne({ name: createBuilderDto.name });
            if (existing) {
                throw new common_1.ConflictException('Builder with this name already exists');
            }
            const builder = new this.builderModel(createBuilderDto);
            return await builder.save();
        }
        catch (error) {
            if (error instanceof common_1.ConflictException)
                throw error;
            throw new common_1.BadRequestException('Failed to create builder');
        }
    }
    async findAll() {
        return this.builderModel.find().exec();
    }
    async findById(id) {
        const builder = await this.builderModel.findById(id).exec();
        if (!builder)
            throw new common_1.NotFoundException('Builder not found');
        return builder;
    }
    async update(id, updateBuilderDto) {
        const builder = await this.builderModel.findByIdAndUpdate(id, updateBuilderDto, { new: true, runValidators: true }).exec();
        if (!builder)
            throw new common_1.NotFoundException('Builder not found');
        return builder;
    }
    async remove(id) {
        const result = await this.builderModel.findByIdAndDelete(id).exec();
        if (!result)
            throw new common_1.NotFoundException('Builder not found');
    }
};
exports.BuildersService = BuildersService;
exports.BuildersService = BuildersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(builder_schema_1.Builder.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], BuildersService);
//# sourceMappingURL=builders.service.js.map