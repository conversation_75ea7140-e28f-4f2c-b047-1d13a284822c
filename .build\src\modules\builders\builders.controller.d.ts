import { BuildersService } from './builders.service';
import { CreateBuilderDto } from './dto/create-builder.dto';
import { UpdateBuilderDto } from './dto/update-builder.dto';
export declare class BuildersController {
    private readonly buildersService;
    constructor(buildersService: BuildersService);
    create(createBuilderDto: CreateBuilderDto): Promise<{
        success: boolean;
        message: string;
        data: import("./schemas/builder.schema").Builder;
    }>;
    findAll(): Promise<{
        success: boolean;
        message: string;
        data: import("./schemas/builder.schema").Builder[];
    }>;
    findById(id: string): Promise<{
        success: boolean;
        message: string;
        data: import("./schemas/builder.schema").Builder;
    }>;
    update(id: string, updateBuilderDto: UpdateBuilderDto): Promise<{
        success: boolean;
        message: string;
        data: import("./schemas/builder.schema").Builder;
    }>;
    remove(id: string): Promise<{
        success: boolean;
        message: string;
    }>;
}
