import { Document } from 'mongoose';
import { MasterWithNumericValue } from '../../common/schemas/base-master.schema';
import { MasterType } from '../../common/enums/master-types.enum';
export type WashroomDocument = Washroom & Document;
export declare class Washroom extends MasterWithNumericValue {
    masterType: MasterType.WASHROOM;
    numericValue?: number;
    unit?: string;
    washroomType?: string;
    features?: string[];
    typicalArea?: number;
    popularityRating?: number;
    specifications?: {
        hasShower?: boolean;
        hasBathtub?: boolean;
        hasGeyser?: boolean;
        hasExhaustFan?: boolean;
        hasWindow?: boolean;
        fittingsQuality?: string;
    };
    createdAt?: Date;
    updatedAt?: Date;
}
export declare const WashroomSchema: import("mongoose").Schema<Washroom, import("mongoose").Model<Washroom, any, any, any, Document<unknown, any, Washroom, any> & Washroom & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Washroom, Document<unknown, {}, import("mongoose").FlatRecord<Washroom>, {}> & import("mongoose").FlatRecord<Washroom> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
