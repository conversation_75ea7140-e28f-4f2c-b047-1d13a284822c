import { LocationsService } from './locations.service';
import { CreateLocationDto } from './dto/create-location.dto';
import { UpdateLocationDto } from './dto/update-location.dto';
import { QueryLocationDto } from './dto/query-location.dto';
export declare class LocationsController {
    private readonly locationsService;
    constructor(locationsService: LocationsService);
    create(createLocationDto: CreateLocationDto): Promise<{
        success: boolean;
        data: import("./schemas/location.schema").Location;
        message: string;
    }>;
    findAll(queryDto: QueryLocationDto): Promise<import("../common/interfaces/base-master.interface").MasterListResponse<import("./schemas/location.schema").Location>>;
    getStatistics(): Promise<{
        success: boolean;
        data: {
            locationsWithRealEstateData: number;
            averagePropertyPrice: any;
            byLocationType: any;
            byLocationCategory: any;
            topCitiesByLocationCount: {
                cityId: any;
                locationCount: any;
            }[];
            totalCount: number;
            activeCount: number;
            inactiveCount: number;
            popularCount: number;
            defaultCount: number;
            byCategory?: Record<string, number>;
            byStatus?: Record<string, number>;
        };
        message: string;
    }>;
    findPopular(limit?: number): Promise<{
        success: boolean;
        data: import("./schemas/location.schema").Location[];
        message: string;
    }>;
    findByCity(cityId: string): Promise<{
        success: boolean;
        data: import("./schemas/location.schema").Location[];
        message: string;
    }>;
    findByType(type: string): Promise<{
        success: boolean;
        data: import("./schemas/location.schema").Location[];
        message: string;
    }>;
    findNearLocation(longitude: number, latitude: number, maxDistance?: number): Promise<{
        success: boolean;
        data: import("./schemas/location.schema").Location[];
        message: string;
    }>;
    findOne(id: string): Promise<{
        success: boolean;
        data: import("./schemas/location.schema").Location;
        message: string;
    }>;
    update(id: string, updateLocationDto: UpdateLocationDto): Promise<{
        success: boolean;
        data: import("./schemas/location.schema").Location;
        message: string;
    }>;
    remove(id: string): Promise<{
        success: boolean;
        message: string;
    }>;
}
