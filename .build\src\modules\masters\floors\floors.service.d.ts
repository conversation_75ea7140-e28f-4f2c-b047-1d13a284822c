import { Model } from 'mongoose';
import { Floor, FloorDocument } from './schemas/floor.schema';
import { BaseMasterService } from '../common/services/base-master.service';
import { CreateFloorDto } from './dto/create-floor.dto';
import { UpdateFloorDto } from './dto/update-floor.dto';
import { QueryMasterWithNumericRangeDto } from '../common/dto/query-master.dto';
import { MasterListResponse } from '../common/interfaces/base-master.interface';
export declare class FloorsService extends BaseMasterService<Floor> {
    private floorModel;
    constructor(floorModel: Model<FloorDocument>);
    createFloor(createFloorDto: CreateFloorDto): Promise<Floor>;
    findAllFloors(queryDto?: QueryMasterWithNumericRangeDto): Promise<MasterListResponse<Floor>>;
    findFloorById(id: string): Promise<Floor>;
    updateFloor(id: string, updateFloorDto: UpdateFloorDto): Promise<Floor>;
    removeFloor(id: string): Promise<void>;
    findFloorsByType(floorType: string): Promise<Floor[]>;
    findFloorsByUsage(usage: string): Promise<Floor[]>;
    findAvailableFloors(): Promise<Floor[]>;
    findFloorsInRange(minFloor: number, maxFloor: number): Promise<Floor[]>;
    findBasementFloors(): Promise<Floor[]>;
    findGroundFloor(): Promise<Floor | null>;
    findUpperFloors(): Promise<Floor[]>;
    findPremiumFloors(): Promise<Floor[]>;
    getFloorStatistics(): Promise<{
        averagePriceMultiplier: any;
        byFloorType: any;
        byUsage: any;
        availability: any;
        floorRange: any;
        totalCount: number;
        activeCount: number;
        inactiveCount: number;
        popularCount: number;
        defaultCount: number;
        byCategory?: Record<string, number>;
        byStatus?: Record<string, number>;
    }>;
    protected checkUsageBeforeDelete(id: string): Promise<void>;
}
