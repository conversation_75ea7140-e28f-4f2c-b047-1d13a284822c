import { Document, Types } from 'mongoose';
import { ProjectStatus, PropertyType, UnitType, FacingDirection, PossessionStatus, ApprovalStatus } from '../enums/project-status.enum';
export type ProjectDocument = Project & Document;
export declare class Location {
    address: string;
    cityId: Types.ObjectId;
    locationId: Types.ObjectId;
    state: string;
    country: string;
    pincode: string;
    landmark?: string;
    coordinates?: [number, number];
}
export declare class Builder {
    name: string;
    description?: string;
    website?: string;
    contactEmail?: string;
    contactPhone?: string;
    logo?: string;
}
export declare class UnitConfiguration {
    type: UnitType;
    name: string;
    bedrooms: number;
    bathrooms: number;
    balconies?: number;
    carpetArea: number;
    builtUpArea?: number;
    superBuiltUpArea?: number;
    priceMin: number;
    priceMax: number;
    totalUnits?: number;
    availableUnits?: number;
    facing?: FacingDirection[];
    floorPlans?: string[];
}
export declare class Amenities {
    amenityIds?: Types.ObjectId[];
}
export declare class Media {
    images?: string[];
    videos?: string[];
    brochures?: string[];
    floorPlans?: string[];
    masterPlan?: string[];
    locationMap?: string[];
}
export declare class Documents {
    approvals?: string[];
    legalDocuments?: string[];
    certificates?: string[];
    others?: string[];
}
export declare class Project {
    projectName: string;
    projectDescription: string;
    builder: Builder;
    projectStatus: ProjectStatus;
    location: Location;
    reraNumber?: string;
    propertyType: PropertyType;
    unitConfigurations: UnitConfiguration[];
    possessionStatus?: PossessionStatus;
    possessionDate?: Date;
    totalArea?: number;
    totalUnits?: number;
    totalFloors?: number;
    totalTowers?: number;
    priceMin?: number;
    priceMax?: number;
    pricePerSqFt?: number;
    amenities?: Amenities;
    media?: Media;
    documents?: Documents;
    tags?: string[];
    highlights?: string;
    approvalStatus?: ApprovalStatus;
    nearbyFacilities?: string[];
    isActive: boolean;
    isFeatured: boolean;
    viewCount: number;
    inquiryCount: number;
    createdAt?: Date;
    updatedAt?: Date;
}
export declare const ProjectSchema: import("mongoose").Schema<Project, import("mongoose").Model<Project, any, any, any, Document<unknown, any, Project, any> & Project & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Project, Document<unknown, {}, import("mongoose").FlatRecord<Project>, {}> & import("mongoose").FlatRecord<Project> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
export declare const LocationSchema: import("mongoose").Schema<Location, import("mongoose").Model<Location, any, any, any, Document<unknown, any, Location, any> & Location & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Location, Document<unknown, {}, import("mongoose").FlatRecord<Location>, {}> & import("mongoose").FlatRecord<Location> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
export declare const BuilderSchema: import("mongoose").Schema<Builder, import("mongoose").Model<Builder, any, any, any, Document<unknown, any, Builder, any> & Builder & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Builder, Document<unknown, {}, import("mongoose").FlatRecord<Builder>, {}> & import("mongoose").FlatRecord<Builder> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
export declare const UnitConfigurationSchema: import("mongoose").Schema<UnitConfiguration, import("mongoose").Model<UnitConfiguration, any, any, any, Document<unknown, any, UnitConfiguration, any> & UnitConfiguration & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, UnitConfiguration, Document<unknown, {}, import("mongoose").FlatRecord<UnitConfiguration>, {}> & import("mongoose").FlatRecord<UnitConfiguration> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
export declare const AmenitiesSchema: import("mongoose").Schema<Amenities, import("mongoose").Model<Amenities, any, any, any, Document<unknown, any, Amenities, any> & Amenities & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Amenities, Document<unknown, {}, import("mongoose").FlatRecord<Amenities>, {}> & import("mongoose").FlatRecord<Amenities> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
export declare const MediaSchema: import("mongoose").Schema<Media, import("mongoose").Model<Media, any, any, any, Document<unknown, any, Media, any> & Media & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Media, Document<unknown, {}, import("mongoose").FlatRecord<Media>, {}> & import("mongoose").FlatRecord<Media> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
export declare const DocumentsSchema: import("mongoose").Schema<Documents, import("mongoose").Model<Documents, any, any, any, Document<unknown, any, Documents, any> & Documents & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Documents, Document<unknown, {}, import("mongoose").FlatRecord<Documents>, {}> & import("mongoose").FlatRecord<Documents> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
