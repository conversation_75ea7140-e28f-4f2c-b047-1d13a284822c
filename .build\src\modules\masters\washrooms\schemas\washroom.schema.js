"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WashroomSchema = exports.Washroom = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const base_master_schema_1 = require("../../common/schemas/base-master.schema");
const master_types_enum_1 = require("../../common/enums/master-types.enum");
let Washroom = class Washroom extends base_master_schema_1.MasterWithNumericValue {
};
exports.Washroom = Washroom;
__decorate([
    (0, mongoose_1.Prop)({
        required: true,
        type: String,
        enum: Object.values(master_types_enum_1.MasterType),
        default: master_types_enum_1.MasterType.WASHROOM,
        immutable: true
    }),
    __metadata("design:type", String)
], Washroom.prototype, "masterType", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        index: true
    }),
    __metadata("design:type", Number)
], Washroom.prototype, "numericValue", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 20,
        default: 'Bathroom'
    }),
    __metadata("design:type", String)
], Washroom.prototype, "unit", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        trim: true,
        maxlength: 50,
        enum: ['attached', 'common', 'powder_room', 'master_bathroom', 'guest_bathroom']
    }),
    __metadata("design:type", String)
], Washroom.prototype, "washroomType", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: [String],
        default: []
    }),
    __metadata("design:type", Array)
], Washroom.prototype, "features", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false
    }),
    __metadata("design:type", Number)
], Washroom.prototype, "typicalArea", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        min: 1,
        max: 5
    }),
    __metadata("design:type", Number)
], Washroom.prototype, "popularityRating", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: Object,
        default: {}
    }),
    __metadata("design:type", Object)
], Washroom.prototype, "specifications", void 0);
exports.Washroom = Washroom = __decorate([
    (0, mongoose_1.Schema)({
        timestamps: true,
        collection: 'masters',
        discriminatorKey: 'masterType'
    })
], Washroom);
exports.WashroomSchema = mongoose_1.SchemaFactory.createForClass(Washroom);
exports.WashroomSchema.index({ numericValue: 1 }, { unique: true });
exports.WashroomSchema.index({ code: 1 }, { unique: true, sparse: true });
exports.WashroomSchema.index({ washroomType: 1, status: 1 });
exports.WashroomSchema.pre('save', function (next) {
    if (this.isNew) {
        this.masterType = master_types_enum_1.MasterType.WASHROOM;
    }
    next();
});
exports.WashroomSchema.virtual('displayName').get(function () {
    return this.numericValue
        ? `${this.numericValue} ${this.unit || 'Bathroom'}`
        : this.name;
});
exports.WashroomSchema.set('toJSON', { virtuals: true });
exports.WashroomSchema.set('toObject', { virtuals: true });
//# sourceMappingURL=washroom.schema.js.map