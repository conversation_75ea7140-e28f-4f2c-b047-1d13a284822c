"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LocationsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const locations_service_1 = require("./locations.service");
const create_location_dto_1 = require("./dto/create-location.dto");
const update_location_dto_1 = require("./dto/update-location.dto");
const query_location_dto_1 = require("./dto/query-location.dto");
const location_response_dto_1 = require("./dto/location-response.dto");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let LocationsController = class LocationsController {
    constructor(locationsService) {
        this.locationsService = locationsService;
    }
    async create(createLocationDto) {
        const location = await this.locationsService.createLocation(createLocationDto);
        return {
            success: true,
            data: location,
            message: 'Location created successfully'
        };
    }
    async findAll(queryDto) {
        return await this.locationsService.findAllLocations(queryDto);
    }
    async getStatistics() {
        const statistics = await this.locationsService.getLocationStatistics();
        return {
            success: true,
            data: statistics,
            message: 'Location statistics retrieved successfully'
        };
    }
    async findPopular(limit) {
        const locations = await this.locationsService.findPopularLocations(limit);
        return {
            success: true,
            data: locations,
            message: 'Popular locations retrieved successfully'
        };
    }
    async findByCity(cityId) {
        const locations = await this.locationsService.findLocationsByCity(cityId);
        return {
            success: true,
            data: locations,
            message: 'Locations retrieved successfully'
        };
    }
    async findByType(type) {
        const locations = await this.locationsService.findLocationsByType(type);
        return {
            success: true,
            data: locations,
            message: `${type} locations retrieved successfully`
        };
    }
    async findNearLocation(longitude, latitude, maxDistance) {
        const locations = await this.locationsService.findLocationsNearLocation(longitude, latitude, maxDistance);
        return {
            success: true,
            data: locations,
            message: 'Nearby locations retrieved successfully'
        };
    }
    async findOne(id) {
        const location = await this.locationsService.findLocationById(id);
        return {
            success: true,
            data: location,
            message: 'Location retrieved successfully'
        };
    }
    async update(id, updateLocationDto) {
        const location = await this.locationsService.updateLocation(id, updateLocationDto);
        return {
            success: true,
            data: location,
            message: 'Location updated successfully'
        };
    }
    async remove(id) {
        await this.locationsService.removeLocation(id);
        return {
            success: true,
            message: 'Location deleted successfully'
        };
    }
};
exports.LocationsController = LocationsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Create a new location',
        description: 'Create a new location master data entry with comprehensive information including connectivity, real estate data, and nearby facilities.'
    }),
    (0, swagger_1.ApiBody)({ type: create_location_dto_1.CreateLocationDto }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Location created successfully',
        type: location_response_dto_1.SingleLocationResponseDto
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid input data or parent city not found'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CONFLICT,
        description: 'Location with same name already exists in the city'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Body)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_location_dto_1.CreateLocationDto]),
    __metadata("design:returntype", Promise)
], LocationsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get all locations',
        description: 'Retrieve all locations with advanced filtering, search, and pagination capabilities.'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Locations retrieved successfully',
        type: location_response_dto_1.LocationListResponseDto
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid query parameters'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Query)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [query_location_dto_1.QueryLocationDto]),
    __metadata("design:returntype", Promise)
], LocationsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('statistics'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get location statistics',
        description: 'Retrieve comprehensive statistics about locations including counts by type, category, and real estate data.'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Location statistics retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], LocationsController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)('popular'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get popular locations',
        description: 'Retrieve list of popular locations marked as popular in the system.'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Number of popular locations to retrieve',
        example: 10
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Popular locations retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], LocationsController.prototype, "findPopular", null);
__decorate([
    (0, common_1.Get)('by-city/:cityId'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get locations by city',
        description: 'Retrieve all locations belonging to a specific city.'
    }),
    (0, swagger_1.ApiParam)({
        name: 'cityId',
        description: 'City ID',
        example: '507f1f77bcf86cd799439011'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Locations retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Param)('cityId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], LocationsController.prototype, "findByCity", null);
__decorate([
    (0, common_1.Get)('by-type/:type'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get locations by type',
        description: 'Retrieve all locations of a specific type (residential, commercial, etc.).'
    }),
    (0, swagger_1.ApiParam)({
        name: 'type',
        description: 'Location type',
        example: 'residential'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Locations retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Param)('type')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], LocationsController.prototype, "findByType", null);
__decorate([
    (0, common_1.Get)('near'),
    (0, swagger_1.ApiOperation)({
        summary: 'Find locations near coordinates',
        description: 'Find locations near a specific geographical location using coordinates.'
    }),
    (0, swagger_1.ApiQuery)({
        name: 'longitude',
        required: true,
        type: Number,
        description: 'Longitude coordinate',
        example: 72.8265
    }),
    (0, swagger_1.ApiQuery)({
        name: 'latitude',
        required: true,
        type: Number,
        description: 'Latitude coordinate',
        example: 19.0596
    }),
    (0, swagger_1.ApiQuery)({
        name: 'maxDistance',
        required: false,
        type: Number,
        description: 'Maximum distance in meters',
        example: 50000
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Nearby locations retrieved successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid coordinates'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Query)('longitude')),
    __param(1, (0, common_1.Query)('latitude')),
    __param(2, (0, common_1.Query)('maxDistance')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, Number]),
    __metadata("design:returntype", Promise)
], LocationsController.prototype, "findNearLocation", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get location by ID',
        description: 'Retrieve a specific location by its unique identifier.'
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Location ID',
        example: '507f1f77bcf86cd799439012'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Location retrieved successfully',
        type: location_response_dto_1.SingleLocationResponseDto
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Location not found'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid location ID'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], LocationsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Update location by ID',
        description: 'Update a specific location with new information.'
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Location ID',
        example: '507f1f77bcf86cd799439012'
    }),
    (0, swagger_1.ApiBody)({ type: update_location_dto_1.UpdateLocationDto }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Location updated successfully',
        type: location_response_dto_1.SingleLocationResponseDto
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Location not found'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid input data or location ID'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CONFLICT,
        description: 'Location with same name already exists in the city'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_location_dto_1.UpdateLocationDto]),
    __metadata("design:returntype", Promise)
], LocationsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Delete location by ID',
        description: 'Soft delete a specific location (marks as archived).'
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Location ID',
        example: '507f1f77bcf86cd799439012'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Location deleted successfully'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Location not found'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Cannot delete location as it is being used'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Unauthorized access'
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], LocationsController.prototype, "remove", null);
exports.LocationsController = LocationsController = __decorate([
    (0, swagger_1.ApiTags)('Masters - Locations'),
    (0, common_1.Controller)('masters/locations'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [locations_service_1.LocationsService])
], LocationsController);
//# sourceMappingURL=locations.controller.js.map