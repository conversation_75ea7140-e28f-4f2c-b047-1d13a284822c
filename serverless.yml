# Serverless Framework Configuration for TrelaX Admin Backend
service: trelax-admin-backend

# Framework version constraint
frameworkVersion: '3'

# Provider configuration
provider:
  name: aws
  runtime: nodejs18.x
  region: ap-south-1
  stage: ${opt:stage, 'dev'}
  memorySize: 512
  timeout: 30
  logRetentionInDays: 14
  
  # Environment variables
  environment:
    NODE_ENV: ${self:provider.stage}
    STAGE: ${self:provider.stage}
    REGION: ${self:provider.region}
    # Database configuration
    MONGO_URI: ${env:MONGO_URI, 'mongodb+srv://sj317772:<EMAIL>/trelax_${self:provider.stage}_db?retryWrites=true&w=majority&appName=Cluster0'}
    # JWT configuration
    JWT_SECRET: ${env:JWT_SECRET, 'lambda_jwt_secret_key_change_in_production_2024'}
    JWT_EXPIRES_IN: ${env:JWT_EXPIRES_IN, '7d'}
    # AWS S3 configuration
    AWS_S3_BUCKET: ${env:AWS_S3_BUCKET, 'trelax-admin-uploads-${self:provider.stage}'}
    AWS_REGION: ${self:provider.region}
    # API configuration
    API_PREFIX: api/v1
    # CORS configuration
    CORS_ORIGIN: ${env:CORS_ORIGIN, '*'}
    
  # IAM role statements
  iam:
    role:
      statements:
        # S3 permissions
        - Effect: Allow
          Action:
            - s3:GetObject
            - s3:PutObject
            - s3:DeleteObject
            - s3:ListBucket
          Resource:
            - arn:aws:s3:::trelax-admin-uploads-${self:provider.stage}
            - arn:aws:s3:::trelax-admin-uploads-${self:provider.stage}/*
        # CloudWatch Logs permissions
        - Effect: Allow
          Action:
            - logs:CreateLogGroup
            - logs:CreateLogStream
            - logs:PutLogEvents
          Resource: arn:aws:logs:${self:provider.region}:*:*
        # Lambda permissions
        - Effect: Allow
          Action:
            - lambda:InvokeFunction
          Resource: arn:aws:lambda:${self:provider.region}:*:function:${self:service}-${self:provider.stage}-*

  # API Gateway configuration
  apiGateway:
    restApiId: ${env:API_GATEWAY_REST_API_ID, ''}
    restApiRootResourceId: ${env:API_GATEWAY_ROOT_RESOURCE_ID, ''}
    minimumCompressionSize: 1024
    binaryMediaTypes:
      - 'multipart/form-data'
      - 'image/*'
      - 'application/pdf'
      - 'application/octet-stream'

# Plugins
plugins:
  - serverless-plugin-typescript
  - serverless-offline

# Custom configuration
custom:
  # Serverless Offline configuration
  serverless-offline:
    httpPort: 3000
    host: 0.0.0.0
    stage: ${self:provider.stage}
    prefix: api/v1
    printOutput: true
    noAuth: true
    corsAllowOrigin: '*'
    corsAllowHeaders: 'accept,content-type,x-api-key,authorization'
    corsExposedHeaders: 'WWW-Authenticate,Server-Authorization'
    
  # TypeScript configuration
  serverless-plugin-typescript:
    tsConfigFileLocation: './tsconfig.build.json'

# Functions
functions:
  # Main API handler
  main:
    handler: src/lambda.handler
    name: ${self:service}-${self:provider.stage}-main
    description: Main API handler for TrelaX Admin Backend
    memorySize: 1024
    timeout: 30
    reservedConcurrency: 10
    events:
      - http:
          path: /{proxy+}
          method: ANY
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - Accept
            allowCredentials: true
          integration: lambda-proxy
          request:
            passThrough: WHEN_NO_MATCH
      - http:
          path: /
          method: ANY
          cors:
            origin: '*'
            headers:
              - Content-Type
              - X-Amz-Date
              - Authorization
              - X-Api-Key
              - X-Amz-Security-Token
              - X-Amz-User-Agent
              - Accept
            allowCredentials: true
          integration: lambda-proxy

  # Health check function
  health:
    handler: src/lambda.healthCheck
    name: ${self:service}-${self:provider.stage}-health
    description: Health check endpoint
    memorySize: 128
    timeout: 10
    events:
      - http:
          path: /health
          method: GET
          cors:
            origin: '*'
            headers:
              - Content-Type
            allowCredentials: false

  # Warmup function to prevent cold starts
  warmup:
    handler: src/lambda.warmup
    name: ${self:service}-${self:provider.stage}-warmup
    description: Lambda warmup function
    memorySize: 128
    timeout: 10
    events:
      - schedule:
          rate: rate(5 minutes)
          enabled: ${env:ENABLE_WARMUP, 'false'}
          input:
            source: serverless-plugin-warmup

# Resources
resources:
  Resources:
    # S3 Bucket for file uploads
    S3BucketUploads:
      Type: AWS::S3::Bucket
      Properties:
        BucketName: trelax-admin-uploads-${self:provider.stage}
        PublicAccessBlockConfiguration:
          BlockPublicAcls: false
          BlockPublicPolicy: false
          IgnorePublicAcls: false
          RestrictPublicBuckets: false
        CorsConfiguration:
          CorsRules:
            - AllowedHeaders:
                - '*'
              AllowedMethods:
                - GET
                - PUT
                - POST
                - DELETE
                - HEAD
              AllowedOrigins:
                - '*'
              ExposedHeaders:
                - ETag
              MaxAge: 3000

    # S3 Bucket Policy
    S3BucketUploadsPolicy:
      Type: AWS::S3::BucketPolicy
      Properties:
        Bucket: !Ref S3BucketUploads
        PolicyDocument:
          Statement:
            - Sid: PublicReadGetObject
              Effect: Allow
              Principal: '*'
              Action:
                - s3:GetObject
              Resource: !Sub '${S3BucketUploads}/*'

    # CloudWatch Log Group
    MainLogGroup:
      Type: AWS::Logs::LogGroup
      Properties:
        LogGroupName: /aws/lambda/${self:service}-${self:provider.stage}-main
        RetentionInDays: 14

  # Outputs
  Outputs:
    ApiGatewayRestApiId:
      Value: !Ref ApiGatewayRestApi
      Export:
        Name: ${self:service}-${self:provider.stage}-ApiGatewayRestApiId
    
    ApiGatewayRestApiRootResourceId:
      Value: !GetAtt ApiGatewayRestApi.RootResourceId
      Export:
        Name: ${self:service}-${self:provider.stage}-ApiGatewayRestApiRootResourceId
    
    S3BucketName:
      Value: !Ref S3BucketUploads
      Export:
        Name: ${self:service}-${self:provider.stage}-S3BucketName
    
    LambdaFunctionArn:
      Value: !GetAtt MainLambdaFunction.Arn
      Export:
        Name: ${self:service}-${self:provider.stage}-LambdaFunctionArn

# Package configuration
package:
  individually: false
  excludeDevDependencies: true
  exclude:
    - .git/**
    - .vscode/**
    - .idea/**
    - node_modules/.cache/**
    - coverage/**
    - test/**
    - tests/**
    - '**/*.test.ts'
    - '**/*.spec.ts'
    - '**/*.e2e-spec.ts'
    - docker/**
    - logs/**
    - uploads/**
    - '*.md'
    - '.env*'
    - 'docker-compose*.yml'
    - 'Dockerfile*'
    - '.dockerignore'
    - '*.sh'
    - '*.bat'
  include:
    - src/**
    - dist/**
    - node_modules/**
    - package.json
