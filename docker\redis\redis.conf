# Redis Configuration for TrelaX Admin Backend
# Optimized for Docker environment

# Network
bind 0.0.0.0
port 6379
protected-mode no

# General
daemonize no
supervised no
pidfile /var/run/redis_6379.pid

# Logging
loglevel notice
logfile ""
syslog-enabled no

# Persistence
save 900 1
save 300 10
save 60 10000

stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb
dir /data

# Replication
replica-serve-stale-data yes
replica-read-only yes
repl-diskless-sync no
repl-diskless-sync-delay 5
repl-ping-replica-period 10
repl-timeout 60
repl-disable-tcp-nodelay no
repl-backlog-size 1mb
repl-backlog-ttl 3600

# Security
requirepass ""
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command DEBUG ""

# Memory Management
maxmemory 256mb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# Lazy Freeing
lazyfree-lazy-eviction no
lazyfree-lazy-expire no
lazyfree-lazy-server-del no
replica-lazy-flush no

# Threaded I/O
io-threads 4
io-threads-do-reads no

# Kernel OOM Score
oom-score-adj no

# Client Output Buffer Limits
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# Client Query Buffer Limit
client-query-buffer-limit 1gb

# Protocol Buffer Limit
proto-max-bulk-len 512mb

# Frequency
hz 10

# Dynamic HZ
dynamic-hz yes

# AOF (Append Only File)
appendonly yes
appendfilename "appendonly.aof"
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
aof-load-truncated yes
aof-use-rdb-preamble yes

# Lua Scripting
lua-time-limit 5000

# Slow Log
slowlog-log-slower-than 10000
slowlog-max-len 128

# Latency Monitor
latency-monitor-threshold 0

# Event Notification
notify-keyspace-events ""

# Hash Configuration
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# List Configuration
list-max-ziplist-size -2
list-compress-depth 0

# Set Configuration
set-max-intset-entries 512

# Sorted Set Configuration
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# HyperLogLog Configuration
hll-sparse-max-bytes 3000

# Streams Configuration
stream-node-max-bytes 4096
stream-node-max-entries 100

# Active Rehashing
activerehashing yes

# Client Timeout
timeout 0
tcp-keepalive 300

# TCP Backlog
tcp-backlog 511

# Databases
databases 16

# Keyspace Notifications for Sessions
notify-keyspace-events Ex
