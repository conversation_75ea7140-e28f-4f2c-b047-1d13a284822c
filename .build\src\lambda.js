"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.warmup = exports.healthCheck = exports.handler = void 0;
const core_1 = require("@nestjs/core");
const platform_express_1 = require("@nestjs/platform-express");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const serverless_express_1 = require("@vendia/serverless-express");
const express_1 = require("express");
const app_module_1 = require("./app.module");
let cachedServer;
async function createApp() {
    const expressApp = (0, express_1.default)();
    const app = await core_1.NestFactory.create(app_module_1.AppModule, new platform_express_1.ExpressAdapter(expressApp), {
        logger: process.env.NODE_ENV === 'production' ? ['error', 'warn'] : ['log', 'error', 'warn', 'debug', 'verbose'],
    });
    app.enableCors({
        origin: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
        credentials: true,
    });
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true,
        forbidNonWhitelisted: true,
        transform: true,
        transformOptions: {
            enableImplicitConversion: true,
        },
    }));
    app.setGlobalPrefix('api/v1');
    if (process.env.NODE_ENV !== 'production') {
        const config = new swagger_1.DocumentBuilder()
            .setTitle('TrelaX Admin Backend API')
            .setDescription('RESTful API for TrelaX Real Estate Admin Backend')
            .setVersion('1.0')
            .addBearerAuth({
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT',
            name: 'JWT',
            description: 'Enter JWT token',
            in: 'header',
        }, 'JWT-auth')
            .addServer('/', 'Local Development')
            .addServer('/dev', 'Development Stage')
            .addServer('/prod', 'Production Stage')
            .build();
        const document = swagger_1.SwaggerModule.createDocument(app, config);
        swagger_1.SwaggerModule.setup('api/v1/docs', app, document, {
            swaggerOptions: {
                persistAuthorization: true,
                tagsSorter: 'alpha',
                operationsSorter: 'alpha',
            },
        });
    }
    await app.init();
    return app;
}
async function bootstrap() {
    if (!cachedServer) {
        try {
            const app = await createApp();
            const expressApp = app.getHttpAdapter().getInstance();
            cachedServer = (0, serverless_express_1.configure)({
                app: expressApp,
                logSettings: {
                    level: process.env.NODE_ENV === 'production' ? 'warn' : 'info',
                },
                resolutionMode: 'PROMISE',
            });
        }
        catch (error) {
            console.error('Error during Lambda bootstrap:', error);
            throw error;
        }
    }
    return cachedServer;
}
const handler = async (event, context) => {
    context.callbackWaitsForEmptyEventLoop = false;
    try {
        const server = await bootstrap();
        return await server(event, context);
    }
    catch (error) {
        console.error('Lambda handler error:', error);
        return {
            statusCode: 500,
            headers: {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization, Accept',
            },
            body: JSON.stringify({
                success: false,
                message: 'Internal server error',
                error: process.env.NODE_ENV === 'production' ? 'Something went wrong' : error.message,
            }),
        };
    }
};
exports.handler = handler;
const healthCheck = async () => {
    return {
        statusCode: 200,
        headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
        },
        body: JSON.stringify({
            success: true,
            message: 'Lambda function is healthy',
            timestamp: new Date().toISOString(),
            environment: process.env.NODE_ENV || 'development',
        }),
    };
};
exports.healthCheck = healthCheck;
const warmup = async () => {
    console.log('Lambda warmup invoked');
    return {
        statusCode: 200,
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            success: true,
            message: 'Lambda warmed up successfully',
            timestamp: new Date().toISOString(),
        }),
    };
};
exports.warmup = warmup;
//# sourceMappingURL=lambda.js.map