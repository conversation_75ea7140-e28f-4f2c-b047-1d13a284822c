import { Model, Document } from 'mongoose';
import { BaseMaster } from '../schemas/base-master.schema';
import { MasterQueryOptions, MasterListResponse, MasterStatistics } from '../interfaces/base-master.interface';
import { MasterType } from '../enums/master-types.enum';
export declare abstract class BaseMasterService<T extends BaseMaster> {
    protected readonly model: Model<T & Document>;
    protected readonly masterType: MasterType;
    constructor(model: Model<T & Document>, masterType: MasterType);
    create(createDto: Partial<T>): Promise<T>;
    findAll(queryOptions?: MasterQueryOptions): Promise<MasterListResponse<T>>;
    findById(id: string): Promise<T>;
    update(id: string, updateDto: Partial<T>): Promise<T>;
    remove(id: string): Promise<void>;
    getStatistics(): Promise<MasterStatistics>;
    protected checkUsageBeforeDelete(id: string): Promise<void>;
    findByParentId(parentId: string): Promise<T[]>;
    findByCategory(category: string): Promise<T[]>;
}
