{"version": 3, "file": "examples.js", "sourceRoot": "", "sources": ["../../../../src/common/swagger/examples.ts"], "names": [], "mappings": ";;;AAKa,QAAA,eAAe,GAAG;IAE7B,IAAI,EAAE;QACJ,YAAY,EAAE;YACZ,KAAK,EAAE,kBAAkB;YACzB,QAAQ,EAAE,UAAU;SACrB;QACD,aAAa,EAAE;YACb,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,kBAAkB;YAC3B,IAAI,EAAE;gBACJ,IAAI,EAAE;oBACJ,EAAE,EAAE,QAAQ;oBACZ,KAAK,EAAE,kBAAkB;oBACzB,SAAS,EAAE,OAAO;oBAClB,QAAQ,EAAE,MAAM;oBAChB,IAAI,EAAE,OAAO;oBACb,QAAQ,EAAE,IAAI;iBACf;gBACD,MAAM,EAAE;oBACN,WAAW,EAAE,0KAA0K;iBACxL;aACF;YACD,SAAS,EAAE,0BAA0B;SACtC;QACD,eAAe,EAAE;YACf,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,gCAAgC;YACzC,IAAI,EAAE;gBACJ,EAAE,EAAE,QAAQ;gBACZ,KAAK,EAAE,kBAAkB;gBACzB,SAAS,EAAE,OAAO;gBAClB,QAAQ,EAAE,MAAM;gBAChB,IAAI,EAAE,OAAO;gBACb,QAAQ,EAAE,IAAI;aACf;YACD,SAAS,EAAE,0BAA0B;SACtC;KACF;IAGD,QAAQ,EAAE;QACR,aAAa,EAAE;YACb,WAAW,EAAE,0BAA0B;YACvC,kBAAkB,EAAE,wDAAwD;YAC5E,OAAO,EAAE;gBACP,IAAI,EAAE,cAAc;gBACpB,WAAW,EAAE,+BAA+B;gBAC5C,OAAO,EAAE,yBAAyB;gBAClC,KAAK,EAAE,sBAAsB;gBAC7B,KAAK,EAAE,kBAAkB;aAC1B;YACD,aAAa,EAAE,oBAAoB;YACnC,QAAQ,EAAE;gBACR,OAAO,EAAE,8BAA8B;gBACvC,IAAI,EAAE,QAAQ;gBACd,KAAK,EAAE,aAAa;gBACpB,OAAO,EAAE,OAAO;gBAChB,OAAO,EAAE,QAAQ;gBACjB,QAAQ,EAAE,qBAAqB;gBAC/B,WAAW,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;aAChC;YACD,UAAU,EAAE,cAAc;YAC1B,YAAY,EAAE,aAAa;YAC3B,kBAAkB,EAAE;gBAClB;oBACE,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,OAAO;oBACb,QAAQ,EAAE,CAAC;oBACX,SAAS,EAAE,CAAC;oBACZ,IAAI,EAAE,IAAI;oBACV,UAAU,EAAE;wBACV,GAAG,EAAE,OAAO;wBACZ,GAAG,EAAE,QAAQ;qBACd;oBACD,UAAU,EAAE,EAAE;oBACd,cAAc,EAAE,EAAE;oBAClB,MAAM,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC;iBAC1B;aACF;YACD,SAAS,EAAE;gBACT,KAAK,EAAE,CAAC,eAAe,EAAE,WAAW,EAAE,QAAQ,CAAC;gBAC/C,QAAQ,EAAE,CAAC,mBAAmB,EAAE,eAAe,CAAC;gBAChD,YAAY,EAAE,CAAC,YAAY,EAAE,oBAAoB,CAAC;aACnD;YACD,UAAU,EAAE,IAAI;YAChB,QAAQ,EAAE,IAAI;SACf;QACD,YAAY,EAAE;YACZ,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,iCAAiC;YAC1C,IAAI,EAAE;gBACJ;oBACE,EAAE,EAAE,0BAA0B;oBAC9B,WAAW,EAAE,0BAA0B;oBACvC,kBAAkB,EAAE,6BAA6B;oBACjD,OAAO,EAAE;wBACP,IAAI,EAAE,cAAc;qBACrB;oBACD,aAAa,EAAE,oBAAoB;oBACnC,QAAQ,EAAE;wBACR,IAAI,EAAE,QAAQ;wBACd,KAAK,EAAE,aAAa;qBACrB;oBACD,YAAY,EAAE,aAAa;oBAC3B,UAAU,EAAE,IAAI;oBAChB,SAAS,EAAE,0BAA0B;iBACtC;aACF;YACD,UAAU,EAAE;gBACV,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,EAAE;gBACT,KAAK,EAAE,CAAC;gBACR,UAAU,EAAE,CAAC;gBACb,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,KAAK;aACf;YACD,SAAS,EAAE,0BAA0B;SACtC;KACF;IAGD,OAAO,EAAE;QACP,aAAa,EAAE;YACb,SAAS,EAAE,MAAM;YACjB,IAAI,EAAE,QAAQ;YACd,WAAW,EAAE,uBAAuB;YACpC,MAAM,EAAE,QAAQ;YAChB,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE,KAAK;SACjB;QACD,qBAAqB,EAAE;YACrB,SAAS,EAAE,UAAU;YACrB,IAAI,EAAE,aAAa;YACnB,WAAW,EAAE,4BAA4B;YACzC,QAAQ,EAAE,0BAA0B;YACpC,MAAM,EAAE,QAAQ;YAChB,SAAS,EAAE,CAAC;YACZ,SAAS,EAAE,KAAK;SACjB;QACD,oBAAoB,EAAE;YACpB,SAAS,EAAE,SAAS;YACpB,IAAI,EAAE,gBAAgB;YACtB,WAAW,EAAE,0CAA0C;YACvD,MAAM,EAAE,QAAQ;YAChB,SAAS,EAAE,EAAE;YACb,SAAS,EAAE,KAAK;SACjB;QACD,YAAY,EAAE;YACZ,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,sCAAsC;YAC/C,IAAI,EAAE;gBACJ;oBACE,EAAE,EAAE,0BAA0B;oBAC9B,SAAS,EAAE,MAAM;oBACjB,IAAI,EAAE,QAAQ;oBACd,WAAW,EAAE,4BAA4B;oBACzC,MAAM,EAAE,QAAQ;oBAChB,SAAS,EAAE,CAAC;oBACZ,SAAS,EAAE,IAAI;oBACf,SAAS,EAAE,0BAA0B;iBACtC;aACF;YACD,UAAU,EAAE;gBACV,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,EAAE;gBACT,KAAK,EAAE,CAAC;gBACR,UAAU,EAAE,CAAC;gBACb,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,KAAK;aACf;YACD,SAAS,EAAE,0BAA0B;SACtC;QACD,gBAAgB,EAAE;YAChB,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,+BAA+B;YACxC,IAAI,EAAE;gBACJ;oBACE,EAAE,EAAE,0BAA0B;oBAC9B,SAAS,EAAE,MAAM;oBACjB,IAAI,EAAE,QAAQ;oBACd,SAAS,EAAE,CAAC;oBACZ,SAAS,EAAE,IAAI;iBAChB;gBACD;oBACE,EAAE,EAAE,0BAA0B;oBAC9B,SAAS,EAAE,MAAM;oBACjB,IAAI,EAAE,MAAM;oBACZ,SAAS,EAAE,CAAC;oBACZ,SAAS,EAAE,KAAK;iBACjB;aACF;YACD,SAAS,EAAE,0BAA0B;SACtC;KACF;IAGD,KAAK,EAAE;QACL,cAAc,EAAE;YACd,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,4BAA4B;YACrC,IAAI,EAAE;gBACJ,EAAE,EAAE,0BAA0B;gBAC9B,YAAY,EAAE,mBAAmB;gBACjC,QAAQ,EAAE,kDAAkD;gBAC5D,QAAQ,EAAE,YAAY;gBACtB,IAAI,EAAE,OAAO;gBACb,QAAQ,EAAE,eAAe;gBACzB,KAAK,EAAE,kDAAkD;gBACzD,KAAK,EAAE,uFAAuF;gBAC9F,UAAU,EAAE,QAAQ;gBACpB,SAAS,EAAE,0BAA0B;aACtC;YACD,SAAS,EAAE,0BAA0B;SACtC;QACD,YAAY,EAAE;YACZ,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,8BAA8B;YACvC,IAAI,EAAE;gBACJ;oBACE,EAAE,EAAE,0BAA0B;oBAC9B,YAAY,EAAE,mBAAmB;oBACjC,QAAQ,EAAE,kDAAkD;oBAC5D,QAAQ,EAAE,YAAY;oBACtB,IAAI,EAAE,OAAO;oBACb,QAAQ,EAAE,eAAe;oBACzB,KAAK,EAAE,uFAAuF;oBAC9F,SAAS,EAAE,0BAA0B;iBACtC;aACF;YACD,UAAU,EAAE;gBACV,IAAI,EAAE,CAAC;gBACP,KAAK,EAAE,EAAE;gBACT,KAAK,EAAE,CAAC;gBACR,UAAU,EAAE,CAAC;gBACb,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,KAAK;aACf;YACD,SAAS,EAAE,0BAA0B;SACtC;KACF;IAGD,MAAM,EAAE;QACN,YAAY,EAAE;YACZ,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,qBAAqB;YAC9B,KAAK,EAAE,8BAA8B;YACrC,SAAS,EAAE,0BAA0B;SACtC;QACD,UAAU,EAAE;YACV,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,mBAAmB;YAC5B,KAAK,EAAE,6BAA6B;YACpC,SAAS,EAAE,0BAA0B;SACtC;QACD,QAAQ,EAAE;YACR,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,oBAAoB;YAC7B,KAAK,EAAE,uCAAuC;YAC9C,SAAS,EAAE,0BAA0B;SACtC;KACF;CACF,CAAC;AAEW,QAAA,cAAc,GAAG;IAC5B,gBAAgB,EAAE;QAChB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,OAAO,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;YAC5B,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;YAC3B,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;YACxB,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE;SACnD;KACF;IACD,iBAAiB,EAAE;QACjB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,OAAO,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;YAC5B,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;YAC3B,IAAI,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,EAAE;YAClD,UAAU,EAAE;gBACV,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,IAAI,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;oBACxB,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;oBACzB,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;oBACzB,UAAU,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;oBAC9B,OAAO,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;oBAC5B,OAAO,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;iBAC7B;aACF;YACD,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE;SACnD;KACF;IACD,aAAa,EAAE;QACb,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,OAAO,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE;YAC5C,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;YAC3B,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;YACzB,SAAS,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE;SACnD;KACF;CACF,CAAC"}