"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentsService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const agent_schema_1 = require("./schemas/agent.schema");
let AgentsService = class AgentsService {
    constructor(agentModel) {
        this.agentModel = agentModel;
    }
    async create(createAgentDto) {
        try {
            const existing = await this.agentModel.findOne({ name: createAgentDto.name });
            if (existing) {
                throw new common_1.ConflictException('Agent with this name already exists');
            }
            const agent = new this.agentModel(createAgentDto);
            return await agent.save();
        }
        catch (error) {
            if (error instanceof common_1.ConflictException)
                throw error;
            throw new common_1.BadRequestException('Failed to create agent');
        }
    }
    async findAll() {
        return this.agentModel.find().exec();
    }
    async findById(id) {
        const agent = await this.agentModel.findById(id).exec();
        if (!agent)
            throw new common_1.NotFoundException('Agent not found');
        return agent;
    }
    async update(id, updateAgentDto) {
        const agent = await this.agentModel.findByIdAndUpdate(id, updateAgentDto, { new: true, runValidators: true }).exec();
        if (!agent)
            throw new common_1.NotFoundException('Agent not found');
        return agent;
    }
    async remove(id) {
        const result = await this.agentModel.findByIdAndDelete(id).exec();
        if (!result)
            throw new common_1.NotFoundException('Agent not found');
    }
};
exports.AgentsService = AgentsService;
exports.AgentsService = AgentsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(agent_schema_1.Agent.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], AgentsService);
//# sourceMappingURL=agents.service.js.map