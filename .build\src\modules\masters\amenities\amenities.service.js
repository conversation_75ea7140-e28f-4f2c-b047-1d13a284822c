"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AmenitiesService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const amenity_schema_1 = require("./schemas/amenity.schema");
const base_master_service_1 = require("../common/services/base-master.service");
const master_types_enum_1 = require("../common/enums/master-types.enum");
let AmenitiesService = class AmenitiesService extends base_master_service_1.BaseMasterService {
    constructor(amenityModel) {
        super(amenityModel, master_types_enum_1.MasterType.AMENITY);
        this.amenityModel = amenityModel;
    }
    async createAmenity(createAmenityDto) {
        return await this.create(createAmenityDto);
    }
    async findAllAmenities(queryDto = {}) {
        try {
            const { page = 1, limit = 10, search, status, isDefault, isPopular, sortBy = 'sortOrder', sortOrder = 'asc', category } = queryDto;
            const filter = {
                masterType: master_types_enum_1.MasterType.AMENITY,
                status: { $ne: 'archived' }
            };
            if (search) {
                filter.$or = [
                    { name: { $regex: search, $options: 'i' } },
                    { description: { $regex: search, $options: 'i' } },
                    { code: { $regex: search, $options: 'i' } },
                    { tags: { $regex: search, $options: 'i' } },
                    { keywords: { $regex: search, $options: 'i' } },
                    { relatedAmenities: { $regex: search, $options: 'i' } }
                ];
            }
            if (status)
                filter.status = status;
            if (typeof isDefault === 'boolean')
                filter.isDefault = isDefault;
            if (typeof isPopular === 'boolean')
                filter.isPopular = isPopular;
            if (category)
                filter.category = category;
            const sort = {};
            sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
            const skip = (page - 1) * limit;
            const [data, total] = await Promise.all([
                this.amenityModel
                    .find(filter)
                    .sort(sort)
                    .skip(skip)
                    .limit(limit)
                    .exec(),
                this.amenityModel.countDocuments(filter)
            ]);
            const totalPages = Math.ceil(total / limit);
            return {
                success: true,
                data: data,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages
                }
            };
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch amenities: ${error.message}`);
        }
    }
    async findAmenityById(id) {
        return await this.findById(id);
    }
    async updateAmenity(id, updateAmenityDto) {
        return await this.update(id, updateAmenityDto);
    }
    async removeAmenity(id) {
        return await this.remove(id);
    }
    async findAmenitiesByCategory(category) {
        try {
            return await this.amenityModel
                .find({
                category,
                masterType: master_types_enum_1.MasterType.AMENITY,
                status: { $ne: 'archived' }
            })
                .sort({ sortOrder: 1, name: 1 })
                .exec();
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch amenities by category: ${error.message}`);
        }
    }
    async findPopularAmenities(limit = 10) {
        try {
            return await this.amenityModel
                .find({
                isPopular: true,
                masterType: master_types_enum_1.MasterType.AMENITY,
                status: 'active'
            })
                .sort({ popularityScore: -1, sortOrder: 1, name: 1 })
                .limit(limit)
                .exec();
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch popular amenities: ${error.message}`);
        }
    }
    async findAmenitiesByImportance(importanceLevel) {
        try {
            return await this.amenityModel
                .find({
                importanceLevel,
                masterType: master_types_enum_1.MasterType.AMENITY,
                status: { $ne: 'archived' }
            })
                .sort({ sortOrder: 1, name: 1 })
                .exec();
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch amenities by importance: ${error.message}`);
        }
    }
    async findResidentialAmenities() {
        try {
            return await this.amenityModel
                .find({
                'availability.residential': true,
                masterType: master_types_enum_1.MasterType.AMENITY,
                status: 'active'
            })
                .sort({ popularityScore: -1, sortOrder: 1, name: 1 })
                .exec();
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch residential amenities: ${error.message}`);
        }
    }
    async findCommercialAmenities() {
        try {
            return await this.amenityModel
                .find({
                'availability.commercial': true,
                masterType: master_types_enum_1.MasterType.AMENITY,
                status: 'active'
            })
                .sort({ popularityScore: -1, sortOrder: 1, name: 1 })
                .exec();
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch commercial amenities: ${error.message}`);
        }
    }
    async findLuxuryAmenities() {
        try {
            return await this.amenityModel
                .find({
                'availability.luxury': true,
                masterType: master_types_enum_1.MasterType.AMENITY,
                status: 'active'
            })
                .sort({ popularityScore: -1, sortOrder: 1, name: 1 })
                .exec();
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch luxury amenities: ${error.message}`);
        }
    }
    async findBasicAmenities() {
        try {
            return await this.amenityModel
                .find({
                'availability.basic': true,
                masterType: master_types_enum_1.MasterType.AMENITY,
                status: 'active'
            })
                .sort({ importanceLevel: -1, sortOrder: 1, name: 1 })
                .exec();
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch basic amenities: ${error.message}`);
        }
    }
    async searchAmenitiesByTags(tags) {
        try {
            return await this.amenityModel
                .find({
                tags: { $in: tags },
                masterType: master_types_enum_1.MasterType.AMENITY,
                status: { $ne: 'archived' }
            })
                .sort({ popularityScore: -1, sortOrder: 1, name: 1 })
                .exec();
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to search amenities by tags: ${error.message}`);
        }
    }
    async getAmenityStatistics() {
        try {
            const baseStats = await this.getStatistics();
            const [categoryStats, importanceStats, availabilityStats, topPopularAmenities, averagePopularityScore] = await Promise.all([
                this.amenityModel.aggregate([
                    {
                        $match: {
                            masterType: master_types_enum_1.MasterType.AMENITY,
                            status: { $ne: 'archived' }
                        }
                    },
                    { $group: { _id: '$category', count: { $sum: 1 } } },
                    { $sort: { count: -1 } }
                ]),
                this.amenityModel.aggregate([
                    {
                        $match: {
                            masterType: master_types_enum_1.MasterType.AMENITY,
                            status: { $ne: 'archived' },
                            importanceLevel: { $exists: true, $ne: null }
                        }
                    },
                    { $group: { _id: '$importanceLevel', count: { $sum: 1 } } },
                    { $sort: { _id: 1 } }
                ]),
                this.amenityModel.aggregate([
                    {
                        $match: {
                            masterType: master_types_enum_1.MasterType.AMENITY,
                            status: { $ne: 'archived' }
                        }
                    },
                    {
                        $group: {
                            _id: null,
                            residential: { $sum: { $cond: ['$availability.residential', 1, 0] } },
                            commercial: { $sum: { $cond: ['$availability.commercial', 1, 0] } },
                            luxury: { $sum: { $cond: ['$availability.luxury', 1, 0] } },
                            basic: { $sum: { $cond: ['$availability.basic', 1, 0] } }
                        }
                    }
                ]),
                this.amenityModel.find({
                    masterType: master_types_enum_1.MasterType.AMENITY,
                    status: 'active',
                    popularityScore: { $exists: true, $ne: null }
                })
                    .sort({ popularityScore: -1 })
                    .limit(5)
                    .select('name popularityScore category'),
                this.amenityModel.aggregate([
                    {
                        $match: {
                            masterType: master_types_enum_1.MasterType.AMENITY,
                            popularityScore: { $exists: true, $ne: null },
                            status: { $ne: 'archived' }
                        }
                    },
                    {
                        $group: {
                            _id: null,
                            avgScore: { $avg: '$popularityScore' }
                        }
                    }
                ])
            ]);
            const byCategory = categoryStats.reduce((acc, item) => {
                acc[item._id] = item.count;
                return acc;
            }, {});
            const byImportanceLevel = importanceStats.reduce((acc, item) => {
                acc[`level_${item._id}`] = item.count;
                return acc;
            }, {});
            return {
                ...baseStats,
                averagePopularityScore: averagePopularityScore[0]?.avgScore || 0,
                byCategory,
                byImportanceLevel,
                availability: availabilityStats[0] || {
                    residential: 0,
                    commercial: 0,
                    luxury: 0,
                    basic: 0
                },
                topPopularAmenities
            };
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to get amenity statistics: ${error.message}`);
        }
    }
    async checkUsageBeforeDelete(id) {
    }
};
exports.AmenitiesService = AmenitiesService;
exports.AmenitiesService = AmenitiesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(amenity_schema_1.Amenity.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], AmenitiesService);
//# sourceMappingURL=amenities.service.js.map