import { Document, Types } from 'mongoose';
import { MasterWithParent } from '../../common/schemas/base-master.schema';
import { MasterType } from '../../common/enums/master-types.enum';
export type LocationDocument = Location & Document;
export declare class Location extends MasterWithParent {
    masterType: MasterType.LOCATION;
    parentId: Types.ObjectId;
    parentType: MasterType.CITY;
    locationCode?: string;
    coordinates?: [number, number];
    address?: string;
    pincode?: string;
    landmark?: string;
    alternateNames?: string[];
    locationType?: string;
    locationCategory?: string;
    connectivity?: {
        nearestMetroStation?: string;
        metroDistance?: number;
        nearestRailwayStation?: string;
        railwayDistance?: number;
        nearestAirport?: string;
        airportDistance?: number;
        majorRoads?: string[];
        busConnectivity?: string[];
    };
    realEstateData?: {
        averagePropertyPrice?: number;
        priceRange?: {
            min: number;
            max: number;
        };
        priceAppreciationRate?: number;
        rentalYield?: number;
        popularPropertyTypes?: string[];
        upcomingProjects?: number;
        totalProjects?: number;
        occupancyRate?: number;
    };
    nearbyFacilities?: {
        schools?: string[];
        hospitals?: string[];
        shoppingMalls?: string[];
        restaurants?: string[];
        banks?: string[];
        parks?: string[];
        gyms?: string[];
        temples?: string[];
    };
    demographics?: {
        population?: number;
        averageAge?: number;
        familyOriented?: boolean;
        professionalHub?: boolean;
        studentPopulation?: number;
        seniorCitizens?: number;
    };
    safetyData?: {
        safetyRating?: number;
        policeStation?: string;
        fireBrigade?: string;
        crimeRate?: string;
        streetLighting?: string;
        cctvCoverage?: boolean;
    };
    investmentData?: {
        investmentPotential?: string;
        liquidityIndex?: number;
        capitalAppreciation?: number;
        rentalDemand?: string;
        futureGrowthProspects?: string[];
        governmentProjects?: string[];
    };
    seoTitle?: string;
    seoDescription?: string;
    seoKeywords?: string[];
    featuredImage?: string;
    gallery?: string[];
    createdAt?: Date;
    updatedAt?: Date;
}
export declare const LocationSchema: import("mongoose").Schema<Location, import("mongoose").Model<Location, any, any, any, Document<unknown, any, Location, any> & Location & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Location, Document<unknown, {}, import("mongoose").FlatRecord<Location>, {}> & import("mongoose").FlatRecord<Location> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
