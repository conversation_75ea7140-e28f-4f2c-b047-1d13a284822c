version: '3.8'

services:
  # Test Database
  mongodb-test:
    image: mongo:7.0
    container_name: trelax-mongodb-test
    restart: unless-stopped
    ports:
      - "27020:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: admin123
      MONGO_INITDB_DATABASE: trelax_test_db
    volumes:
      - mongodb_test_data:/data/db
      - ./docker/mongodb/init-scripts:/docker-entrypoint-initdb.d
    networks:
      - trelax-test-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Test Redis
  redis-test:
    image: redis:7.2-alpine
    container_name: trelax-redis-test
    restart: unless-stopped
    ports:
      - "6381:6379"
    volumes:
      - redis_test_data:/data
    command: redis-server --appendonly yes
    networks:
      - trelax-test-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Test LocalStack
  localstack-test:
    image: localstack/localstack:3.0
    container_name: trelax-localstack-test
    restart: unless-stopped
    ports:
      - "4568:4566"
    environment:
      - SERVICES=s3
      - DEBUG=1
      - DATA_DIR=/tmp/localstack/data
      - AWS_DEFAULT_REGION=us-east-1
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
    volumes:
      - localstack_test_data:/tmp/localstack
      - ./docker/localstack/init-scripts:/etc/localstack/init/ready.d
    networks:
      - trelax-test-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4566/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # E2E Test Runner
  e2e-tests:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: trelax-e2e-tests
    environment:
      - NODE_ENV=test
      - MONGO_URI_TEST=***************************************************************************
      - REDIS_HOST=redis-test
      - AWS_ENDPOINT_URL=http://localstack-test:4566
    env_file:
      - .env.test
    volumes:
      - .:/app
      - /app/node_modules
      - ./test-results:/app/test-results
    depends_on:
      mongodb-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
      localstack-test:
        condition: service_healthy
    networks:
      - trelax-test-network
    profiles:
      - testing
    command: npm run test:e2e:runner

  # Unit Test Runner
  unit-tests:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: trelax-unit-tests
    environment:
      - NODE_ENV=test
    volumes:
      - .:/app
      - /app/node_modules
      - ./test-results:/app/test-results
    networks:
      - trelax-test-network
    profiles:
      - testing
    command: npm run test

  # Test Coverage Reporter
  test-coverage:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: trelax-test-coverage
    environment:
      - NODE_ENV=test
      - MONGO_URI_TEST=***************************************************************************
    env_file:
      - .env.test
    volumes:
      - .:/app
      - /app/node_modules
      - ./coverage:/app/coverage
    depends_on:
      mongodb-test:
        condition: service_healthy
    networks:
      - trelax-test-network
    profiles:
      - testing
    command: npm run test:e2e:coverage

  # Performance Test Runner
  performance-tests:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: trelax-performance-tests
    environment:
      - NODE_ENV=test
      - MONGO_URI_TEST=***************************************************************************
    volumes:
      - .:/app
      - /app/node_modules
      - ./performance-results:/app/performance-results
    depends_on:
      mongodb-test:
        condition: service_healthy
    networks:
      - trelax-test-network
    profiles:
      - performance
    command: npm run test:performance

# Test Volumes
volumes:
  mongodb_test_data:
    driver: local
  redis_test_data:
    driver: local
  localstack_test_data:
    driver: local

# Test Network
networks:
  trelax-test-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
