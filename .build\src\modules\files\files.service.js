"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FilesService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const file_schema_1 = require("./schemas/file.schema");
const s3_service_1 = require("./services/s3.service");
let FilesService = class FilesService {
    constructor(fileModel, s3Service) {
        this.fileModel = fileModel;
        this.s3Service = s3Service;
    }
    async uploadFile(file, uploadFileDto, userId, folder = 'uploads') {
        try {
            const s3Result = await this.s3Service.uploadFile(file, folder, uploadFileDto.isPublic || false);
            const fileDocument = new this.fileModel({
                filename: s3Result.key.split('/').pop(),
                originalName: file.originalname,
                mimetype: file.mimetype,
                size: file.size,
                url: s3Result.url,
                key: s3Result.key,
                bucket: s3Result.bucket,
                description: uploadFileDto.description,
                tags: uploadFileDto.tags || [],
                uploadedBy: new mongoose_2.Types.ObjectId(userId),
                isPublic: uploadFileDto.isPublic || false,
            });
            return await fileDocument.save();
        }
        catch (error) {
            throw new common_1.BadRequestException('Failed to upload file');
        }
    }
    async uploadMultipleFiles(files, uploadFileDto, userId, folder = 'uploads') {
        const uploadPromises = files.map(file => this.uploadFile(file, uploadFileDto, userId, folder));
        return await Promise.all(uploadPromises);
    }
    async findAll(queryDto, userId) {
        const { page, limit, search, category, isActive, isPublic, uploadedBy, sortBy, sortOrder, } = queryDto;
        const filter = {};
        if (search) {
            filter.$or = [
                { filename: { $regex: search, $options: 'i' } },
                { originalName: { $regex: search, $options: 'i' } },
                { description: { $regex: search, $options: 'i' } },
            ];
        }
        if (category) {
            filter.category = category;
        }
        if (typeof isActive === 'boolean') {
            filter.isActive = isActive;
        }
        if (typeof isPublic === 'boolean') {
            filter.isPublic = isPublic;
        }
        if (uploadedBy) {
            filter.uploadedBy = new mongoose_2.Types.ObjectId(uploadedBy);
        }
        if (userId && !uploadedBy) {
            filter.$or = [
                { uploadedBy: new mongoose_2.Types.ObjectId(userId) },
                { isPublic: true },
            ];
        }
        const sort = {};
        sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
        const skip = (page - 1) * limit;
        const [files, total] = await Promise.all([
            this.fileModel
                .find(filter)
                .populate('uploadedBy', 'firstName lastName email')
                .sort(sort)
                .skip(skip)
                .limit(limit)
                .exec(),
            this.fileModel.countDocuments(filter),
        ]);
        return {
            files,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit),
                hasNext: page < Math.ceil(total / limit),
                hasPrev: page > 1,
            },
        };
    }
    async findById(id, userId) {
        try {
            const file = await this.fileModel
                .findById(id)
                .populate('uploadedBy', 'firstName lastName email')
                .exec();
            if (!file) {
                throw new common_1.NotFoundException('File not found');
            }
            if (!file.isPublic && userId && file.uploadedBy.toString() !== userId) {
                throw new common_1.NotFoundException('File not found');
            }
            await this.fileModel.findByIdAndUpdate(id, {
                lastAccessedAt: new Date(),
                $inc: { downloadCount: 1 },
            });
            return file;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            throw new common_1.BadRequestException('Invalid file ID');
        }
    }
    async getDownloadUrl(id, userId) {
        const file = await this.findById(id, userId);
        return await this.s3Service.getPresignedUrl(file.key);
    }
    async remove(id, userId) {
        try {
            const file = await this.fileModel.findById(id);
            if (!file) {
                throw new common_1.NotFoundException('File not found');
            }
            if (file.uploadedBy.toString() !== userId) {
                throw new common_1.BadRequestException('You can only delete your own files');
            }
            await this.s3Service.deleteFile(file.key);
            await this.fileModel.findByIdAndDelete(id);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException || error instanceof common_1.BadRequestException) {
                throw error;
            }
            throw new common_1.BadRequestException('Failed to delete file');
        }
    }
    async getStatistics(userId) {
        const filter = userId ? { uploadedBy: new mongoose_2.Types.ObjectId(userId) } : {};
        const [totalFiles, activeFiles, inactiveFiles, publicFiles, privateFiles, categoryStats, totalSize,] = await Promise.all([
            this.fileModel.countDocuments(filter),
            this.fileModel.countDocuments({ ...filter, isActive: true }),
            this.fileModel.countDocuments({ ...filter, isActive: false }),
            this.fileModel.countDocuments({ ...filter, isPublic: true }),
            this.fileModel.countDocuments({ ...filter, isPublic: false }),
            this.fileModel.aggregate([
                { $match: filter },
                { $group: { _id: '$category', count: { $sum: 1 } } },
            ]),
            this.fileModel.aggregate([
                { $match: filter },
                { $group: { _id: null, totalSize: { $sum: '$size' } } },
            ]),
        ]);
        return {
            totalFiles,
            activeFiles,
            inactiveFiles,
            publicFiles,
            privateFiles,
            totalSize: totalSize[0]?.totalSize || 0,
            categoryDistribution: categoryStats.reduce((acc, stat) => {
                acc[stat._id] = stat.count;
                return acc;
            }, {}),
        };
    }
    async getFilesByCategory(category, limit = 20) {
        return await this.fileModel
            .find({ category, isActive: true, isPublic: true })
            .populate('uploadedBy', 'firstName lastName email')
            .sort({ createdAt: -1 })
            .limit(limit)
            .exec();
    }
    async searchByTags(tags, limit = 20) {
        return await this.fileModel
            .find({ tags: { $in: tags }, isActive: true, isPublic: true })
            .populate('uploadedBy', 'firstName lastName email')
            .sort({ createdAt: -1 })
            .limit(limit)
            .exec();
    }
};
exports.FilesService = FilesService;
exports.FilesService = FilesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(file_schema_1.File.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        s3_service_1.S3Service])
], FilesService);
//# sourceMappingURL=files.service.js.map