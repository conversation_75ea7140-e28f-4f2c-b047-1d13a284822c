export interface ApiResponse<T = any> {
    success: boolean;
    message: string;
    data?: T;
    error?: string;
    timestamp: string;
}
export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    };
}
export interface ErrorResponse {
    success: false;
    message: string;
    error: string;
    statusCode: number;
    timestamp: string;
    path?: string;
}
export interface FileUploadResponse {
    success: boolean;
    message: string;
    data: {
        filename: string;
        originalName: string;
        mimetype: string;
        size: number;
        url: string;
        key: string;
    };
    timestamp: string;
}
export interface AuthResponse {
    success: boolean;
    message: string;
    data: {
        user: {
            id: string;
            email: string;
            firstName: string;
            lastName: string;
            role: string;
        };
        tokens: {
            accessToken: string;
            refreshToken?: string;
        };
    };
    timestamp: string;
}
