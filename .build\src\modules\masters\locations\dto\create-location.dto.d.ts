import { MasterStatus } from '../../common/enums/master-types.enum';
export declare class CreateLocationDto {
    name: string;
    cityId: string;
    parentId: string;
    area?: string;
    description?: string;
    coordinates?: [number, number];
    pinCodes?: string[];
    landmarks?: string[];
    connectivity?: {
        nearestMetroStation?: string;
        metroDistance?: number;
        nearestRailwayStation?: string;
        railwayDistance?: number;
        nearestAirport?: string;
        airportDistance?: number;
        majorRoads?: string[];
        busConnectivity?: string[];
    };
    status?: MasterStatus;
    sortOrder?: number;
    isPopular?: boolean;
    averagePrice?: number;
    nearbyFacilities?: string[];
}
