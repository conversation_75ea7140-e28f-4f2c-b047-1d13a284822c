import { MasterWithNumericValueDto } from '../../common/dto/base-master.dto';
import { MasterType } from '../../common/enums/master-types.enum';
export declare class FloorSpecificationsDto {
    ceilingHeight?: number;
    loadCapacity?: number;
    fireRating?: string;
    accessibility?: boolean;
    hvacType?: string;
    electricalLoad?: number;
}
export declare class FloorAmenitiesDto {
    elevator?: boolean;
    escalator?: boolean;
    fireExit?: boolean;
    emergencyStaircase?: boolean;
    restrooms?: boolean;
    powerBackup?: boolean;
}
export declare class CreateFloorDto extends MasterWithNumericValueDto {
    name: string;
    description?: string;
    code?: string;
    numericValue: number;
    unit?: string;
    floorType?: string;
    usage?: string;
    height?: number;
    area?: number;
    specifications?: FloorSpecificationsDto;
    amenities?: FloorAmenitiesDto;
    restrictions?: string[];
    features?: string[];
    priceMultiplier?: number;
    premiumPercentage?: number;
    isAvailable?: boolean;
    maxUnitsPerFloor?: number;
    displayName?: string;
    shortDescription?: string;
    masterType: MasterType.FLOOR;
}
