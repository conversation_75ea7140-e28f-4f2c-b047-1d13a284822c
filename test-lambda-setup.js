#!/usr/bin/env node

/**
 * Lambda Setup Test Script
 * This script tests the Lambda configuration and setup
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

console.log('🧪 Testing Lambda Setup for TrelaX Admin Backend');
console.log('================================================');

// Test 1: Check if required files exist
console.log('\n1. 📁 Checking required files...');

const requiredFiles = [
  'src/lambda.ts',
  'serverless.yml',
  'serverless.dev.yml',
  'serverless.prod.yml',
  'tsconfig.build.json',
  'scripts/deploy-lambda.sh',
  'scripts/deploy-lambda.bat',
  '.env.lambda'
];

let allFilesExist = true;

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`   ✅ ${file}`);
  } else {
    console.log(`   ❌ ${file} - MISSING`);
    allFilesExist = false;
  }
});

if (!allFilesExist) {
  console.log('\n❌ Some required files are missing. Please check the setup.');
  process.exit(1);
}

// Test 2: Check package.json dependencies
console.log('\n2. 📦 Checking package.json dependencies...');

const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));

const requiredDependencies = [
  '@vendia/serverless-express'
];

const requiredDevDependencies = [
  'serverless',
  'serverless-offline',
  'serverless-plugin-typescript',
  '@types/aws-lambda'
];

let allDepsExist = true;

console.log('   Dependencies:');
requiredDependencies.forEach(dep => {
  if (packageJson.dependencies && packageJson.dependencies[dep]) {
    console.log(`   ✅ ${dep}`);
  } else {
    console.log(`   ❌ ${dep} - MISSING`);
    allDepsExist = false;
  }
});

console.log('   Dev Dependencies:');
requiredDevDependencies.forEach(dep => {
  if (packageJson.devDependencies && packageJson.devDependencies[dep]) {
    console.log(`   ✅ ${dep}`);
  } else {
    console.log(`   ❌ ${dep} - MISSING`);
    allDepsExist = false;
  }
});

if (!allDepsExist) {
  console.log('\n❌ Some required dependencies are missing. Run: npm install');
  process.exit(1);
}

// Test 3: Check package.json scripts
console.log('\n3. 🔧 Checking package.json scripts...');

const requiredScripts = [
  'build:lambda',
  'sls:offline',
  'sls:deploy',
  'sls:deploy:dev',
  'sls:deploy:prod'
];

let allScriptsExist = true;

requiredScripts.forEach(script => {
  if (packageJson.scripts && packageJson.scripts[script]) {
    console.log(`   ✅ ${script}`);
  } else {
    console.log(`   ❌ ${script} - MISSING`);
    allScriptsExist = false;
  }
});

if (!allScriptsExist) {
  console.log('\n❌ Some required scripts are missing in package.json.');
}

// Test 4: Validate TypeScript configuration
console.log('\n4. 📝 Validating TypeScript configuration...');

try {
  const tsConfig = JSON.parse(fs.readFileSync('tsconfig.build.json', 'utf8'));
  
  if (tsConfig.include && tsConfig.include.includes('src/lambda.ts')) {
    console.log('   ✅ lambda.ts included in tsconfig.build.json');
  } else {
    console.log('   ❌ lambda.ts not included in tsconfig.build.json');
  }
  
  if (tsConfig.compilerOptions && tsConfig.compilerOptions.target === 'es2020') {
    console.log('   ✅ Target set to es2020');
  } else {
    console.log('   ⚠️  Target should be es2020 for Lambda');
  }
  
} catch (error) {
  console.log('   ❌ Error reading tsconfig.build.json:', error.message);
}

// Test 5: Validate Serverless configuration
console.log('\n5. ⚙️  Validating Serverless configuration...');

try {
  const serverlessContent = fs.readFileSync('serverless.yml', 'utf8');
  
  if (serverlessContent.includes('nodejs18.x')) {
    console.log('   ✅ Node.js 18.x runtime configured');
  } else {
    console.log('   ❌ Node.js 18.x runtime not found');
  }
  
  if (serverlessContent.includes('ap-south-1')) {
    console.log('   ✅ Region set to ap-south-1');
  } else {
    console.log('   ❌ Region not set to ap-south-1');
  }
  
  if (serverlessContent.includes('serverless-offline')) {
    console.log('   ✅ Serverless offline plugin configured');
  } else {
    console.log('   ❌ Serverless offline plugin not configured');
  }
  
} catch (error) {
  console.log('   ❌ Error reading serverless.yml:', error.message);
}

// Test 6: Check Lambda entry point
console.log('\n6. 🚀 Checking Lambda entry point...');

try {
  const lambdaContent = fs.readFileSync('src/lambda.ts', 'utf8');
  
  if (lambdaContent.includes('@vendia/serverless-express')) {
    console.log('   ✅ @vendia/serverless-express imported');
  } else {
    console.log('   ❌ @vendia/serverless-express not imported');
  }
  
  if (lambdaContent.includes('export const handler')) {
    console.log('   ✅ Handler function exported');
  } else {
    console.log('   ❌ Handler function not exported');
  }
  
  if (lambdaContent.includes('APIGatewayProxyEvent')) {
    console.log('   ✅ AWS Lambda types used');
  } else {
    console.log('   ❌ AWS Lambda types not used');
  }
  
} catch (error) {
  console.log('   ❌ Error reading src/lambda.ts:', error.message);
}

// Test 7: Test TypeScript compilation
console.log('\n7. 🔨 Testing TypeScript compilation...');

async function testCompilation() {
  try {
    console.log('   Compiling TypeScript...');
    await execAsync('npx tsc --noEmit --project tsconfig.build.json');
    console.log('   ✅ TypeScript compilation successful');
  } catch (error) {
    console.log('   ❌ TypeScript compilation failed:');
    console.log('   ', error.message);
  }
}

// Test 8: Test Serverless configuration validation
console.log('\n8. 🔍 Testing Serverless configuration validation...');

async function testServerlessConfig() {
  try {
    console.log('   Validating Serverless configuration...');
    await execAsync('npx serverless print --stage dev > /dev/null 2>&1');
    console.log('   ✅ Serverless configuration is valid');
  } catch (error) {
    console.log('   ❌ Serverless configuration validation failed');
    console.log('   Make sure serverless is installed: npm install -g serverless');
  }
}

// Run async tests
async function runAsyncTests() {
  await testCompilation();
  await testServerlessConfig();
  
  console.log('\n🎉 Lambda setup test completed!');
  console.log('\n📋 Next steps:');
  console.log('   1. Install dependencies: npm install');
  console.log('   2. Configure AWS credentials: aws configure');
  console.log('   3. Test locally: npm run sls:offline');
  console.log('   4. Deploy to dev: npm run sls:deploy:dev');
  console.log('   5. Deploy to prod: npm run sls:deploy:prod');
  console.log('\n📚 For detailed instructions, see LAMBDA_DEPLOYMENT_GUIDE.md');
}

runAsyncTests().catch(console.error);
