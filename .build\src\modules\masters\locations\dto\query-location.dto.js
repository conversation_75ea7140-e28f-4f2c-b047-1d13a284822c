"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QueryLocationDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const query_master_dto_1 = require("../../common/dto/query-master.dto");
class QueryLocationDto extends query_master_dto_1.QueryMasterWithParentDto {
}
exports.QueryLocationDto = QueryLocationDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by city ID',
        example: '507f1f77bcf86cd799439011'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsMongoId)(),
    __metadata("design:type", String)
], QueryLocationDto.prototype, "cityId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by location type',
        example: 'residential',
        enum: ['residential', 'commercial', 'mixed', 'industrial', 'it_hub', 'business_district', 'suburb', 'downtown', 'waterfront', 'hillside']
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['residential', 'commercial', 'mixed', 'industrial', 'it_hub', 'business_district', 'suburb', 'downtown', 'waterfront', 'hillside']),
    __metadata("design:type", String)
], QueryLocationDto.prototype, "locationType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by location category',
        example: 'premium',
        enum: ['prime', 'premium', 'mid_range', 'budget', 'luxury', 'affordable', 'upcoming', 'established']
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['prime', 'premium', 'mid_range', 'budget', 'luxury', 'affordable', 'upcoming', 'established']),
    __metadata("design:type", String)
], QueryLocationDto.prototype, "locationCategory", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by pincode',
        example: '400050'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryLocationDto.prototype, "pincode", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Minimum average property price',
        example: 8000
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], QueryLocationDto.prototype, "minPropertyPrice", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Maximum average property price',
        example: 20000
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], QueryLocationDto.prototype, "maxPropertyPrice", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Minimum price appreciation rate',
        example: 5
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(-100),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], QueryLocationDto.prototype, "minAppreciationRate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Maximum price appreciation rate',
        example: 15
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(-100),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], QueryLocationDto.prototype, "maxAppreciationRate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Minimum rental yield',
        example: 2
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], QueryLocationDto.prototype, "minRentalYield", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Maximum rental yield',
        example: 6
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], QueryLocationDto.prototype, "maxRentalYield", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by nearby metro station',
        example: 'Bandra Station'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryLocationDto.prototype, "nearbyMetroStation", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Maximum distance to metro in km',
        example: 5
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], QueryLocationDto.prototype, "maxMetroDistance", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by nearby railway station',
        example: 'Bandra Railway Station'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryLocationDto.prototype, "nearbyRailwayStation", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Maximum distance to railway in km',
        example: 3
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], QueryLocationDto.prototype, "maxRailwayDistance", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Maximum distance to airport in km',
        example: 25
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Type)(() => Number),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], QueryLocationDto.prototype, "maxAirportDistance", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by property type',
        example: 'apartment'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], QueryLocationDto.prototype, "propertyType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter locations with connectivity data only',
        example: true
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], QueryLocationDto.prototype, "hasConnectivity", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter locations with real estate data only',
        example: true
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], QueryLocationDto.prototype, "hasRealEstateData", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter locations with nearby facilities data only',
        example: true
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], QueryLocationDto.prototype, "hasNearbyFacilities", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter locations with featured images only',
        example: true
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], QueryLocationDto.prototype, "hasFeaturedImage", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter locations with gallery images only',
        example: true
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], QueryLocationDto.prototype, "hasGallery", void 0);
//# sourceMappingURL=query-location.dto.js.map