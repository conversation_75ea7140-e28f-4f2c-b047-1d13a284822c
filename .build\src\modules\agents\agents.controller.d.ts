import { AgentsService } from './agents.service';
import { CreateAgentDto } from './dto/create-agent.dto';
import { UpdateAgentDto } from './dto/update-agent.dto';
export declare class AgentsController {
    private readonly agentsService;
    constructor(agentsService: AgentsService);
    create(createAgentDto: CreateAgentDto): Promise<{
        success: boolean;
        message: string;
        data: import("./schemas/agent.schema").Agent;
    }>;
    findAll(): Promise<{
        success: boolean;
        message: string;
        data: import("./schemas/agent.schema").Agent[];
    }>;
    findById(id: string): Promise<{
        success: boolean;
        message: string;
        data: import("./schemas/agent.schema").Agent;
    }>;
    update(id: string, updateAgentDto: UpdateAgentDto): Promise<{
        success: boolean;
        message: string;
        data: import("./schemas/agent.schema").Agent;
    }>;
    remove(id: string): Promise<{
        success: boolean;
        message: string;
    }>;
}
