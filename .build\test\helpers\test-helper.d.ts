import { INestApplication } from '@nestjs/common';
export declare class TestHelper {
    private static app;
    private static moduleFixture;
    private static authToken;
    private static testUserId;
    static initializeApp(): Promise<INestApplication>;
    static getApp(): INestApplication;
    static login(email?: string, password?: string): Promise<string>;
    static getAuthHeaders(): Promise<{
        Authorization: string;
    }>;
    static getTestUserId(): string;
    static clearAuth(): void;
    static cleanupTestData(): Promise<void>;
    static closeApp(): Promise<void>;
    static createTestCity(): {
        name: string;
        state: string;
        country: string;
        coordinates: number[];
        isPopular: boolean;
    };
    static createTestLocation(cityId: string): {
        name: string;
        parentId: string;
        locationCode: string;
        coordinates: number[];
        locationType: string;
    };
    static createTestAmenity(): {
        name: string;
        category: string;
        importance: number;
        icon: string;
    };
    static createTestBuilder(): {
        name: string;
        description: string;
        website: string;
        contactEmail: string;
        contactPhone: string;
    };
    static createTestAgent(): {
        firstName: string;
        lastName: string;
        email: string;
        phone: string;
        licenseNumber: string;
        experience: number;
    };
    static createTestProject(cityId: string, locationId: string, builderId: string): {
        projectName: string;
        projectDescription: string;
        builder: {
            name: string;
            contactEmail: string;
            contactPhone: string;
        };
        projectStatus: string;
        location: {
            address: string;
            cityId: string;
            locationId: string;
            state: string;
            country: string;
            pincode: string;
            coordinates: number[];
        };
        reraNumber: string;
        propertyType: string;
        totalUnits: number;
        totalFloors: number;
        totalTowers: number;
        priceMin: number;
        priceMax: number;
    };
    static expectSuccessResponse(response: any, expectedMessage?: string): void;
    static expectErrorResponse(response: any, expectedStatus: number, expectedMessage?: string): void;
    static expectValidationError(response: any): void;
    static expectUnauthorizedError(response: any): void;
    static expectNotFoundError(response: any): void;
    static wait(ms: number): Promise<void>;
}
