"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateWashroomDto = exports.WashroomSpecificationsDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const base_master_dto_1 = require("../../common/dto/base-master.dto");
const master_types_enum_1 = require("../../common/enums/master-types.enum");
class WashroomSpecificationsDto {
}
exports.WashroomSpecificationsDto = WashroomSpecificationsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Has shower', example: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], WashroomSpecificationsDto.prototype, "hasShower", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Has bathtub', example: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], WashroomSpecificationsDto.prototype, "hasBathtub", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Has geyser', example: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], WashroomSpecificationsDto.prototype, "hasGeyser", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Has exhaust fan', example: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], WashroomSpecificationsDto.prototype, "hasExhaustFan", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Has window', example: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], WashroomSpecificationsDto.prototype, "hasWindow", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Fittings quality',
        enum: ['basic', 'premium', 'luxury'],
        example: 'premium'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['basic', 'premium', 'luxury']),
    __metadata("design:type", String)
], WashroomSpecificationsDto.prototype, "fittingsQuality", void 0);
class CreateWashroomDto extends base_master_dto_1.MasterWithNumericValueDto {
    constructor() {
        super(...arguments);
        this.unit = 'Bathroom';
        this.masterType = master_types_enum_1.MasterType.WASHROOM;
    }
}
exports.CreateWashroomDto = CreateWashroomDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Washroom configuration name', example: '2 Bathrooms' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], CreateWashroomDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Washroom description' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(500),
    __metadata("design:type", String)
], CreateWashroomDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Washroom code', example: '2BATH' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(20),
    __metadata("design:type", String)
], CreateWashroomDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Number of washrooms', example: 2 }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(10),
    __metadata("design:type", Number)
], CreateWashroomDto.prototype, "numericValue", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Unit type', default: 'Bathroom' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(20),
    __metadata("design:type", String)
], CreateWashroomDto.prototype, "unit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Washroom type',
        enum: ['attached', 'common', 'powder_room', 'master_bathroom', 'guest_bathroom']
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['attached', 'common', 'powder_room', 'master_bathroom', 'guest_bathroom']),
    __metadata("design:type", String)
], CreateWashroomDto.prototype, "washroomType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Washroom features',
        example: ['bathtub', 'shower', 'geyser', 'exhaust_fan']
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateWashroomDto.prototype, "features", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Typical area in sq ft', example: 40 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateWashroomDto.prototype, "typicalArea", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Popularity rating (1-5)', example: 4 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(5),
    __metadata("design:type", Number)
], CreateWashroomDto.prototype, "popularityRating", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ type: WashroomSpecificationsDto }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => WashroomSpecificationsDto),
    __metadata("design:type", WashroomSpecificationsDto)
], CreateWashroomDto.prototype, "specifications", void 0);
//# sourceMappingURL=create-washroom.dto.js.map