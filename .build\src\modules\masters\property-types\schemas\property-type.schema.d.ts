import { Document } from 'mongoose';
import { MasterWithCategory } from '../../common/schemas/base-master.schema';
import { MasterType, PropertyTypeCategory } from '../../common/enums/master-types.enum';
export type PropertyTypeDocument = PropertyType & Document;
export declare class PropertyType extends MasterWithCategory {
    masterType: MasterType.PROPERTY_TYPE;
    category: PropertyTypeCategory;
    icon?: string;
    color?: string;
    suitableFor?: string[];
    specifications?: {
        minArea?: number;
        maxArea?: number;
        typicalFloors?: number;
        parkingSpaces?: number;
        balconies?: number;
        bathrooms?: number;
        bedrooms?: number;
    };
    features?: string[];
    priceRange?: {
        min?: number;
        max?: number;
        currency?: string;
    };
    popularityRating?: number;
    targetAudience?: string[];
    legalRequirements?: string[];
    seoTitle?: string;
    seoDescription?: string;
    featuredImage?: string;
    gallery?: string[];
    createdAt?: Date;
    updatedAt?: Date;
}
export declare const PropertyTypeSchema: import("mongoose").Schema<PropertyType, import("mongoose").Model<PropertyType, any, any, any, Document<unknown, any, PropertyType, any> & PropertyType & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, PropertyType, Document<unknown, {}, import("mongoose").FlatRecord<PropertyType>, {}> & import("mongoose").FlatRecord<PropertyType> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
