"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SeedModule = void 0;
const core_1 = require("@nestjs/core");
const config_1 = require("@nestjs/config");
const mongoose_1 = require("@nestjs/mongoose");
const common_1 = require("@nestjs/common");
const comprehensive_seeder_1 = require("./comprehensive.seeder");
const city_schema_1 = require("../../modules/masters/cities/schemas/city.schema");
const location_schema_1 = require("../../modules/masters/locations/schemas/location.schema");
const amenity_schema_1 = require("../../modules/masters/amenities/schemas/amenity.schema");
const floor_schema_1 = require("../../modules/masters/floors/schemas/floor.schema");
const tower_schema_1 = require("../../modules/masters/towers/schemas/tower.schema");
const property_type_schema_1 = require("../../modules/masters/property-types/schemas/property-type.schema");
const room_schema_1 = require("../../modules/masters/rooms/schemas/room.schema");
const washroom_schema_1 = require("../../modules/masters/washrooms/schemas/washroom.schema");
const project_schema_1 = require("../../modules/projects/schemas/project.schema");
const builder_schema_1 = require("../../modules/builders/schemas/builder.schema");
const agent_schema_1 = require("../../modules/agents/schemas/agent.schema");
let SeedModule = class SeedModule {
};
exports.SeedModule = SeedModule;
exports.SeedModule = SeedModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: '.env',
            }),
            mongoose_1.MongooseModule.forRootAsync({
                imports: [config_1.ConfigModule],
                useFactory: (configService) => {
                    const uri = configService.get('MONGO_URI') ||
                        process.env.MONGO_URI ||
                        'mongodb://localhost:27017/trelax_seeded_db';
                    console.log('🔗 Attempting to connect to:', uri.includes('localhost') ? 'Local MongoDB' : 'MongoDB Atlas');
                    return {
                        uri,
                        maxPoolSize: 10,
                        serverSelectionTimeoutMS: 15000,
                        socketTimeoutMS: 45000,
                        bufferCommands: false,
                    };
                },
                inject: [config_1.ConfigService],
            }),
            mongoose_1.MongooseModule.forFeature([
                { name: city_schema_1.City.name, schema: city_schema_1.CitySchema },
                { name: location_schema_1.Location.name, schema: location_schema_1.LocationSchema },
                { name: amenity_schema_1.Amenity.name, schema: amenity_schema_1.AmenitySchema },
                { name: floor_schema_1.Floor.name, schema: floor_schema_1.FloorSchema },
                { name: tower_schema_1.Tower.name, schema: tower_schema_1.TowerSchema },
                { name: property_type_schema_1.PropertyType.name, schema: property_type_schema_1.PropertyTypeSchema },
                { name: room_schema_1.Room.name, schema: room_schema_1.RoomSchema },
                { name: washroom_schema_1.Washroom.name, schema: washroom_schema_1.WashroomSchema },
                { name: project_schema_1.Project.name, schema: project_schema_1.ProjectSchema },
                { name: builder_schema_1.Builder.name, schema: builder_schema_1.BuilderSchema },
                { name: agent_schema_1.Agent.name, schema: agent_schema_1.AgentSchema },
            ]),
        ],
        providers: [comprehensive_seeder_1.ComprehensiveSeeder],
    })
], SeedModule);
async function bootstrap() {
    console.log('🌱 Starting database seeding process...');
    const mongoUri = process.env.MONGO_URI || 'mongodb+srv://sj317772:<EMAIL>/trelax_seeded_db?retryWrites=true&w=majority&appName=Cluster0';
    console.log('📊 Database URI:', mongoUri.replace(/\/\/[^:]+:[^@]+@/, '//***:***@'));
    try {
        const app = await core_1.NestFactory.createApplicationContext(SeedModule);
        const seeder = app.get(comprehensive_seeder_1.ComprehensiveSeeder);
        console.log('🚀 Initializing seeder...');
        await seeder.seedAll();
        console.log('✅ Seeding completed successfully!');
        console.log('📈 Database has been populated with:');
        console.log('   - 20 Indian cities with coordinates');
        console.log('   - 200+ locations across cities');
        console.log('   - 30+ categorized amenities');
        console.log('   - Property configuration data (floors, towers, types, rooms, washrooms)');
        console.log('   - 25 real estate builders');
        console.log('   - 50 real estate agents');
        console.log('   - 100 realistic projects with complete details');
        console.log('');
        console.log('🎯 You can now:');
        console.log('   1. Start your application: npm run start:dev');
        console.log('   2. Access Swagger UI: http://localhost:3000/api/v1/docs');
        console.log('   3. Login with: <EMAIL> / admin123');
        console.log('   4. Explore all the seeded data through the APIs');
        await app.close();
        process.exit(0);
    }
    catch (error) {
        console.error('❌ Seeding failed:', error);
        process.exit(1);
    }
}
process.on('SIGINT', () => {
    console.log('\n⚠️  Seeding interrupted by user');
    process.exit(1);
});
process.on('SIGTERM', () => {
    console.log('\n⚠️  Seeding terminated');
    process.exit(1);
});
bootstrap();
//# sourceMappingURL=seed.command.js.map