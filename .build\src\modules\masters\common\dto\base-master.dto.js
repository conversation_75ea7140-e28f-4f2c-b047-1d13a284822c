"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MasterWithLocationDto = exports.MasterWithNumericValueDto = exports.MasterWithCategoryDto = exports.MasterWithParentDto = exports.BaseMasterDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const master_types_enum_1 = require("../enums/master-types.enum");
class BaseMasterDto {
    constructor() {
        this.status = master_types_enum_1.MasterStatus.ACTIVE;
        this.isDefault = false;
        this.isPopular = false;
    }
}
exports.BaseMasterDto = BaseMasterDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Name of the master data entry',
        example: 'Mumbai',
        maxLength: 100
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], BaseMasterDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Description of the master data entry',
        example: 'Financial capital of India',
        maxLength: 500
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(500),
    __metadata("design:type", String)
], BaseMasterDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Unique code for the master data entry',
        example: 'MUM',
        maxLength: 20
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(20),
    __metadata("design:type", String)
], BaseMasterDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Type of master data',
        enum: master_types_enum_1.MasterType,
        example: master_types_enum_1.MasterType.CITY
    }),
    (0, class_validator_1.IsEnum)(master_types_enum_1.MasterType),
    __metadata("design:type", String)
], BaseMasterDto.prototype, "masterType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Status of the master data entry',
        enum: master_types_enum_1.MasterStatus,
        default: master_types_enum_1.MasterStatus.ACTIVE
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(master_types_enum_1.MasterStatus),
    __metadata("design:type", String)
], BaseMasterDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Sort order for display',
        example: 1,
        minimum: 0,
        maximum: 9999
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(9999),
    __metadata("design:type", Number)
], BaseMasterDto.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Whether this is a default option',
        example: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], BaseMasterDto.prototype, "isDefault", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Whether this is a popular option',
        example: true
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], BaseMasterDto.prototype, "isPopular", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Additional metadata as key-value pairs',
        example: { color: '#FF5733', icon: 'city-icon' }
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], BaseMasterDto.prototype, "metadata", void 0);
class MasterWithParentDto extends BaseMasterDto {
}
exports.MasterWithParentDto = MasterWithParentDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Parent master data ID',
        example: '507f1f77bcf86cd799439011'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], MasterWithParentDto.prototype, "parentId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Parent master data type',
        enum: master_types_enum_1.MasterType,
        example: master_types_enum_1.MasterType.CITY
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(master_types_enum_1.MasterType),
    __metadata("design:type", String)
], MasterWithParentDto.prototype, "parentType", void 0);
class MasterWithCategoryDto extends BaseMasterDto {
}
exports.MasterWithCategoryDto = MasterWithCategoryDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Category of the master data',
        example: 'basic'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(50),
    __metadata("design:type", String)
], MasterWithCategoryDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Icon identifier',
        example: 'swimming-pool'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(50),
    __metadata("design:type", String)
], MasterWithCategoryDto.prototype, "icon", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Color code',
        example: '#FF5733'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(10),
    __metadata("design:type", String)
], MasterWithCategoryDto.prototype, "color", void 0);
class MasterWithNumericValueDto extends BaseMasterDto {
}
exports.MasterWithNumericValueDto = MasterWithNumericValueDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Numeric value',
        example: 2
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], MasterWithNumericValueDto.prototype, "numericValue", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Unit of measurement',
        example: 'BHK'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(20),
    __metadata("design:type", String)
], MasterWithNumericValueDto.prototype, "unit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Minimum value',
        example: 1
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], MasterWithNumericValueDto.prototype, "minValue", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Maximum value',
        example: 10
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    __metadata("design:type", Number)
], MasterWithNumericValueDto.prototype, "maxValue", void 0);
class MasterWithLocationDto extends BaseMasterDto {
}
exports.MasterWithLocationDto = MasterWithLocationDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'State name',
        example: 'Maharashtra'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], MasterWithLocationDto.prototype, "state", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Country name',
        example: 'India'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(100),
    __metadata("design:type", String)
], MasterWithLocationDto.prototype, "country", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Coordinates [longitude, latitude]',
        example: [72.8777, 19.0760]
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsNumber)({}, { each: true }),
    __metadata("design:type", Array)
], MasterWithLocationDto.prototype, "coordinates", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Timezone',
        example: 'Asia/Kolkata'
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(50),
    __metadata("design:type", String)
], MasterWithLocationDto.prototype, "timezone", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Array of pin codes',
        example: ['400001', '400002', '400003']
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], MasterWithLocationDto.prototype, "pinCodes", void 0);
//# sourceMappingURL=base-master.dto.js.map