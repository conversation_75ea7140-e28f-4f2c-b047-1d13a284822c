"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectsController = void 0;
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const swagger_1 = require("@nestjs/swagger");
const projects_service_1 = require("./projects.service");
const update_project_dto_1 = require("./dto/update-project.dto");
const query_project_dto_1 = require("./dto/query-project.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const response_util_1 = require("../../common/utils/response.util");
const api_response_decorator_1 = require("../../common/decorators/api-response.decorator");
const s3_service_1 = require("../files/services/s3.service");
let ProjectsController = class ProjectsController {
    constructor(projectsService, s3Service) {
        this.projectsService = projectsService;
        this.s3Service = s3Service;
    }
    async create(body, files, req) {
        try {
            let createProjectDto;
            if (body.projectData) {
                createProjectDto = typeof body.projectData === 'string'
                    ? JSON.parse(body.projectData)
                    : body.projectData;
            }
            else {
                createProjectDto = body;
            }
            const media = {
                images: [],
                floorPlans: [],
                brochures: [],
                videos: [],
                masterPlan: [],
                locationMap: []
            };
            const documents = {
                approvals: [],
                legalDocuments: [],
                certificates: [],
                others: []
            };
            if (files && files.length > 0) {
                for (const file of files) {
                    const fieldName = file.fieldname;
                    let s3Folder = 'projects/misc';
                    let targetArray = documents.others;
                    switch (fieldName) {
                        case 'projectImages':
                            s3Folder = 'projects/images';
                            targetArray = media.images;
                            break;
                        case 'floorPlanImages':
                            s3Folder = 'projects/floorplans';
                            targetArray = media.floorPlans;
                            break;
                        case 'brochurePdf':
                            s3Folder = 'projects/brochures';
                            targetArray = media.brochures;
                            break;
                        case 'additionalDocuments':
                            s3Folder = 'projects/documents';
                            targetArray = documents.others;
                            break;
                        default:
                            s3Folder = 'projects/misc';
                            targetArray = documents.others;
                    }
                    const uploadResult = await this.s3Service.uploadFile(file, s3Folder);
                    targetArray.push(uploadResult.url);
                }
            }
            if (media.images.length > 0 || media.floorPlans.length > 0 || media.brochures.length > 0) {
                createProjectDto.media = { ...createProjectDto.media, ...media };
            }
            if (documents.others.length > 0) {
                createProjectDto.documents = { ...createProjectDto.documents, ...documents };
            }
            const project = await this.projectsService.create(createProjectDto, req.user.id);
            return response_util_1.ResponseUtil.created(project, 'Project created successfully');
        }
        catch (error) {
            console.error('Error creating project:', error);
            throw error;
        }
    }
    async findAll(queryDto) {
        const result = await this.projectsService.findAll(queryDto);
        return response_util_1.ResponseUtil.paginated(result.projects, result.pagination.page, result.pagination.limit, result.pagination.total, 'Projects retrieved successfully');
    }
    async findOne(id) {
        const project = await this.projectsService.findById(id);
        return response_util_1.ResponseUtil.success(project, 'Project retrieved successfully');
    }
    async update(id, updateProjectDto, req) {
        const project = await this.projectsService.update(id, updateProjectDto, req.user._id);
        return response_util_1.ResponseUtil.success(project, 'Project updated successfully');
    }
    async remove(id) {
        await this.projectsService.remove(id);
        return response_util_1.ResponseUtil.successMessage('Project deleted successfully');
    }
    async uploadMedia(id, type, files, req) {
        const urls = await this.projectsService.uploadMedia(id, files, type, req.user._id);
        return response_util_1.ResponseUtil.success(urls, 'Media files uploaded successfully');
    }
    async uploadDocuments(id, type, files, req) {
        const urls = await this.projectsService.uploadDocuments(id, files, type, req.user._id);
        return response_util_1.ResponseUtil.success(urls, 'Document files uploaded successfully');
    }
    async getStatistics() {
        const stats = await this.projectsService.getStatistics();
        return response_util_1.ResponseUtil.success(stats, 'Statistics retrieved successfully');
    }
    async getFeaturedProjects(limit = 10) {
        const projects = await this.projectsService.getFeaturedProjects(limit);
        return response_util_1.ResponseUtil.success(projects, 'Featured projects retrieved successfully');
    }
};
exports.ProjectsController = ProjectsController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.UseInterceptors)((0, platform_express_1.AnyFilesInterceptor)()),
    (0, swagger_1.ApiConsumes)('multipart/form-data'),
    (0, swagger_1.ApiOperation)({
        summary: '🏗️ Create New Project (with media upload)',
        description: `
**Create a new real estate project with file uploads**

This endpoint accepts both project data and file uploads in a single request:

**Form Fields:**
- **projectData**: JSON string containing all project information
- **projectImages**: Multiple image files (JPG, PNG, WebP)
- **floorPlanImages**: Multiple floor plan images
- **brochurePdf**: Single PDF brochure file
- **additionalDocuments**: Multiple document files (PDF, DOC, etc.)

**File Storage:**
- All files are uploaded to AWS S3
- Organized in folders: /projects/images/, /projects/floorplans/, /projects/brochures/, /projects/documents/
- URLs are automatically included in the project response

**Example Form Data:**
- projectData: '{"projectName": "Test Project", "projectDescription": "...", ...}'
- projectImages: [file1.jpg, file2.jpg]
- floorPlanImages: [floorplan1.jpg]
- brochurePdf: brochure.pdf
    `,
    }),
    (0, api_response_decorator_1.ApiCreatedResponse)({
        description: 'Project created successfully with uploaded files',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Project created successfully' },
                data: {
                    type: 'object',
                    properties: {
                        id: { type: 'string', example: '507f1f77bcf86cd799439011' },
                        projectName: { type: 'string', example: 'Luxury Heights Residency' },
                        projectStatus: { type: 'string', example: 'under_construction' },
                        media: {
                            type: 'object',
                            properties: {
                                images: {
                                    type: 'array',
                                    items: { type: 'string' },
                                    example: ['https://s3.amazonaws.com/bucket/projects/images/image1.jpg']
                                },
                                floorPlans: {
                                    type: 'array',
                                    items: { type: 'string' },
                                    example: ['https://s3.amazonaws.com/bucket/projects/floorplans/plan1.jpg']
                                },
                                brochures: {
                                    type: 'array',
                                    items: { type: 'string' },
                                    example: ['https://s3.amazonaws.com/bucket/projects/brochures/brochure.pdf']
                                }
                            }
                        },
                        createdAt: { type: 'string', example: '2024-01-01T00:00:00.000Z' }
                    }
                },
                timestamp: { type: 'string', example: '2024-01-01T00:00:00.000Z' }
            }
        }
    }),
    (0, api_response_decorator_1.ApiBadRequestResponse)('Invalid project data or file upload failed'),
    (0, api_response_decorator_1.ApiUnauthorizedResponse)('Invalid or missing JWT token'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.UploadedFiles)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Array, Object]),
    __metadata("design:returntype", Promise)
], ProjectsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get All Projects',
        description: 'Retrieve all projects with advanced filtering and pagination',
    }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, type: Number, description: 'Page number' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Items per page' }),
    (0, swagger_1.ApiQuery)({ name: 'search', required: false, type: String, description: 'Search term' }),
    (0, swagger_1.ApiQuery)({ name: 'city', required: false, type: String, description: 'Filter by city' }),
    (0, swagger_1.ApiQuery)({ name: 'priceMin', required: false, type: Number, description: 'Minimum price' }),
    (0, swagger_1.ApiQuery)({ name: 'priceMax', required: false, type: Number, description: 'Maximum price' }),
    (0, api_response_decorator_1.ApiSuccessResponse)({
        description: 'Projects retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Projects retrieved successfully' },
                data: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            projectName: { type: 'string' },
                            projectDescription: { type: 'string' },
                            projectStatus: { type: 'string' },
                            propertyType: { type: 'string' },
                            location: { type: 'object' },
                            builder: { type: 'object' },
                            priceMin: { type: 'number' },
                            priceMax: { type: 'number' },
                            createdAt: { type: 'string' },
                        },
                    },
                },
                pagination: {
                    type: 'object',
                    properties: {
                        page: { type: 'number' },
                        limit: { type: 'number' },
                        total: { type: 'number' },
                        totalPages: { type: 'number' },
                        hasNext: { type: 'boolean' },
                        hasPrev: { type: 'boolean' },
                    },
                },
                timestamp: { type: 'string' },
            },
        },
    }),
    (0, api_response_decorator_1.ApiUnauthorizedResponse)('Invalid or missing JWT token'),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [query_project_dto_1.QueryProjectDto]),
    __metadata("design:returntype", Promise)
], ProjectsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get Project by ID',
        description: 'Retrieve a specific project by its ID with full details',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Project ID' }),
    (0, api_response_decorator_1.ApiSuccessResponse)({
        description: 'Project retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Project retrieved successfully' },
                data: {
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        projectName: { type: 'string' },
                        projectDescription: { type: 'string' },
                        projectStatus: { type: 'string' },
                        propertyType: { type: 'string' },
                        location: { type: 'object' },
                        builder: { type: 'object' },
                        unitConfigurations: { type: 'array' },
                        amenities: { type: 'object' },
                        media: { type: 'object' },
                        documents: { type: 'object' },
                        viewCount: { type: 'number' },
                        createdAt: { type: 'string' },
                        updatedAt: { type: 'string' },
                    },
                },
                timestamp: { type: 'string' },
            },
        },
    }),
    (0, api_response_decorator_1.ApiNotFoundResponse)('Project not found'),
    (0, api_response_decorator_1.ApiUnauthorizedResponse)('Invalid or missing JWT token'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ProjectsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Update Project',
        description: 'Update project information by ID',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Project ID' }),
    (0, api_response_decorator_1.ApiSuccessResponse)({
        description: 'Project updated successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Project updated successfully' },
                data: {
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        projectName: { type: 'string' },
                        projectDescription: { type: 'string' },
                        projectStatus: { type: 'string' },
                        propertyType: { type: 'string' },
                        location: { type: 'object' },
                        builder: { type: 'object' },
                        unitConfigurations: { type: 'array' },
                        updatedAt: { type: 'string' },
                    },
                },
                timestamp: { type: 'string' },
            },
        },
    }),
    (0, api_response_decorator_1.ApiBadRequestResponse)('Invalid input data'),
    (0, api_response_decorator_1.ApiNotFoundResponse)('Project not found'),
    (0, api_response_decorator_1.ApiUnauthorizedResponse)('Invalid or missing JWT token'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_project_dto_1.UpdateProjectDto, Object]),
    __metadata("design:returntype", Promise)
], ProjectsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Delete Project',
        description: 'Soft delete project by setting isActive to false',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Project ID' }),
    (0, api_response_decorator_1.ApiSuccessResponse)({
        description: 'Project deleted successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Project deleted successfully' },
                timestamp: { type: 'string' },
            },
        },
    }),
    (0, api_response_decorator_1.ApiNotFoundResponse)('Project not found'),
    (0, api_response_decorator_1.ApiUnauthorizedResponse)('Invalid or missing JWT token'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ProjectsController.prototype, "remove", null);
__decorate([
    (0, common_1.Post)(':id/media/:type'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FilesInterceptor)('files', 10)),
    (0, swagger_1.ApiConsumes)('multipart/form-data'),
    (0, swagger_1.ApiOperation)({
        summary: 'Upload Project Media',
        description: 'Upload media files (images, videos, brochures, etc.) for a project',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Project ID' }),
    (0, swagger_1.ApiParam)({
        name: 'type',
        description: 'Media type',
        enum: ['images', 'videos', 'brochures', 'floorPlans', 'masterPlan', 'locationMap'],
    }),
    (0, api_response_decorator_1.ApiSuccessResponse)({
        description: 'Media files uploaded successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Media files uploaded successfully' },
                data: {
                    type: 'array',
                    items: { type: 'string' },
                    example: ['https://s3.amazonaws.com/bucket/project1/images/image1.jpg'],
                },
                timestamp: { type: 'string' },
            },
        },
    }),
    (0, api_response_decorator_1.ApiBadRequestResponse)('Invalid files or project not found'),
    (0, api_response_decorator_1.ApiUnauthorizedResponse)('Invalid or missing JWT token'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Param)('type')),
    __param(2, (0, common_1.UploadedFiles)()),
    __param(3, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Array, Object]),
    __metadata("design:returntype", Promise)
], ProjectsController.prototype, "uploadMedia", null);
__decorate([
    (0, common_1.Post)(':id/documents/:type'),
    (0, common_1.UseInterceptors)((0, platform_express_1.FilesInterceptor)('files', 10)),
    (0, swagger_1.ApiConsumes)('multipart/form-data'),
    (0, swagger_1.ApiOperation)({
        summary: 'Upload Project Documents',
        description: 'Upload document files (approvals, legal documents, certificates, etc.) for a project',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Project ID' }),
    (0, swagger_1.ApiParam)({
        name: 'type',
        description: 'Document type',
        enum: ['approvals', 'legalDocuments', 'certificates', 'others'],
    }),
    (0, api_response_decorator_1.ApiSuccessResponse)({
        description: 'Document files uploaded successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Document files uploaded successfully' },
                data: {
                    type: 'array',
                    items: { type: 'string' },
                    example: ['https://s3.amazonaws.com/bucket/project1/documents/approval.pdf'],
                },
                timestamp: { type: 'string' },
            },
        },
    }),
    (0, api_response_decorator_1.ApiBadRequestResponse)('Invalid files or project not found'),
    (0, api_response_decorator_1.ApiUnauthorizedResponse)('Invalid or missing JWT token'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Param)('type')),
    __param(2, (0, common_1.UploadedFiles)()),
    __param(3, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Array, Object]),
    __metadata("design:returntype", Promise)
], ProjectsController.prototype, "uploadDocuments", null);
__decorate([
    (0, common_1.Get)('admin/statistics'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get Project Statistics',
        description: 'Get comprehensive project statistics (Admin only)',
    }),
    (0, api_response_decorator_1.ApiSuccessResponse)({
        description: 'Statistics retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Statistics retrieved successfully' },
                data: {
                    type: 'object',
                    properties: {
                        totalProjects: { type: 'number' },
                        activeProjects: { type: 'number' },
                        inactiveProjects: { type: 'number' },
                        featuredProjects: { type: 'number' },
                        statusDistribution: { type: 'object' },
                        propertyTypeDistribution: { type: 'object' },
                        topCities: { type: 'array' },
                    },
                },
                timestamp: { type: 'string' },
            },
        },
    }),
    (0, api_response_decorator_1.ApiUnauthorizedResponse)('Invalid or missing JWT token'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ProjectsController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)('featured'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get Featured Projects',
        description: 'Get list of featured projects',
    }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, type: Number, description: 'Number of projects to return' }),
    (0, api_response_decorator_1.ApiSuccessResponse)({
        description: 'Featured projects retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                success: { type: 'boolean', example: true },
                message: { type: 'string', example: 'Featured projects retrieved successfully' },
                data: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            id: { type: 'string' },
                            projectName: { type: 'string' },
                            projectDescription: { type: 'string' },
                            location: { type: 'object' },
                            builder: { type: 'object' },
                            media: { type: 'object' },
                            priceMin: { type: 'number' },
                            priceMax: { type: 'number' },
                        },
                    },
                },
                timestamp: { type: 'string' },
            },
        },
    }),
    (0, api_response_decorator_1.ApiUnauthorizedResponse)('Invalid or missing JWT token'),
    __param(0, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number]),
    __metadata("design:returntype", Promise)
], ProjectsController.prototype, "getFeaturedProjects", null);
exports.ProjectsController = ProjectsController = __decorate([
    (0, swagger_1.ApiTags)('🏢 Projects'),
    (0, common_1.Controller)('projects'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [projects_service_1.ProjectsService,
        s3_service_1.S3Service])
], ProjectsController);
//# sourceMappingURL=projects.controller.js.map