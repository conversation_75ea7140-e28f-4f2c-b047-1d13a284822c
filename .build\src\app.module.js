"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const mongoose_1 = require("@nestjs/mongoose");
const jwt_1 = require("@nestjs/jwt");
const passport_1 = require("@nestjs/passport");
const platform_express_1 = require("@nestjs/platform-express");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const auth_module_1 = require("./modules/auth/auth.module");
const projects_module_1 = require("./modules/projects/projects.module");
const masters_module_1 = require("./modules/masters/masters.module");
const builders_module_1 = require("./modules/builders/builders.module");
const agents_module_1 = require("./modules/agents/agents.module");
let AppModule = class AppModule {
    constructor(configService) {
        this.configService = configService;
        console.log('🔧 Application Configuration:');
        console.log(`   - Environment: ${this.configService.get('NODE_ENV', 'development')}`);
        console.log(`   - Port: ${this.configService.get('PORT', 3000)}`);
        console.log(`   - API Prefix: ${this.configService.get('API_PREFIX', 'api/v1')}`);
        console.log(`   - MongoDB: ${this.configService.get('MONGO_URI') ? '✅ Connected' : '❌ Not configured'}`);
        console.log(`   - JWT: ${this.configService.get('JWT_SECRET') ? '✅ Configured' : '❌ Not configured'}`);
        console.log(`   - AWS S3: ${this.configService.get('AWS_S3_BUCKET') ? '✅ Configured' : '❌ Not configured'}`);
    }
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: '.env',
                cache: true,
            }),
            mongoose_1.MongooseModule.forRootAsync({
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => {
                    const uri = configService.get('MONGO_URI');
                    if (!uri) {
                        throw new Error('MONGO_URI is not defined in environment variables');
                    }
                    return {
                        uri,
                        maxPoolSize: 10,
                        serverSelectionTimeoutMS: 5000,
                        socketTimeoutMS: 45000,
                        bufferCommands: false,
                    };
                },
                inject: [config_1.ConfigService],
            }),
            jwt_1.JwtModule.registerAsync({
                global: true,
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => {
                    const secret = configService.get('JWT_SECRET');
                    if (!secret) {
                        throw new Error('JWT_SECRET is not defined in environment variables');
                    }
                    return {
                        secret,
                        signOptions: {
                            expiresIn: configService.get('JWT_EXPIRES_IN', '7d'),
                        },
                    };
                },
                inject: [config_1.ConfigService],
            }),
            passport_1.PassportModule.register({
                defaultStrategy: 'jwt',
                session: false,
            }),
            platform_express_1.MulterModule.registerAsync({
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => ({
                    limits: {
                        fileSize: 10 * 1024 * 1024,
                    },
                    fileFilter: (req, file, callback) => {
                        const allowedMimes = [
                            'image/jpeg',
                            'image/png',
                            'image/gif',
                            'image/webp',
                            'application/pdf',
                            'text/plain',
                            'application/msword',
                            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                        ];
                        if (allowedMimes.includes(file.mimetype)) {
                            callback(null, true);
                        }
                        else {
                            callback(new Error('Invalid file type'), false);
                        }
                    },
                }),
                inject: [config_1.ConfigService],
            }),
            auth_module_1.AuthModule,
            projects_module_1.ProjectsModule,
            masters_module_1.MastersModule,
            builders_module_1.BuildersModule,
            agents_module_1.AgentsModule,
        ],
        controllers: [app_controller_1.AppController],
        providers: [app_service_1.AppService],
    }),
    __metadata("design:paramtypes", [config_1.ConfigService])
], AppModule);
//# sourceMappingURL=app.module.js.map