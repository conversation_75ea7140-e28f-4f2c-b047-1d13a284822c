import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { LoginDto } from './dto/login.dto';
export declare class AuthService {
    private readonly jwtService;
    private readonly configService;
    private readonly adminAccounts;
    constructor(jwtService: JwtService, configService: ConfigService);
    private initializeAdminPasswords;
    validateUser(email: string, password: string): Promise<any>;
    login(loginDto: LoginDto): Promise<{
        user: {
            id: any;
            email: any;
            firstName: any;
            lastName: any;
            role: any;
            isActive: boolean;
        };
        tokens: {
            accessToken: string;
        };
    }>;
    validateJwtPayload(payload: any): Promise<any>;
    getProfile(adminId: string): Promise<{
        id: string;
        email: string;
        firstName: string;
        lastName: string;
        role: string;
        isActive: boolean;
    }>;
    refreshToken(adminId: string): Promise<{
        accessToken: string;
    }>;
}
