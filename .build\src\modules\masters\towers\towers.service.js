"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TowersService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const tower_schema_1 = require("./schemas/tower.schema");
const base_master_service_1 = require("../common/services/base-master.service");
const master_types_enum_1 = require("../common/enums/master-types.enum");
let TowersService = class TowersService extends base_master_service_1.BaseMasterService {
    constructor(towerModel) {
        super(towerModel, master_types_enum_1.MasterType.TOWER);
        this.towerModel = towerModel;
    }
    async createTower(createTowerDto) {
        return await this.create(createTowerDto);
    }
    async findAllTowers(queryDto = {}) {
        try {
            const { page = 1, limit = 10, search, status, isDefault, isPopular, sortBy = 'numericValue', sortOrder = 'asc', minValue, maxValue, unit } = queryDto;
            const filter = {
                masterType: master_types_enum_1.MasterType.TOWER,
                status: { $ne: 'archived' }
            };
            if (search) {
                filter.$or = [
                    { name: { $regex: search, $options: 'i' } },
                    { description: { $regex: search, $options: 'i' } },
                    { code: { $regex: search, $options: 'i' } },
                    { features: { $regex: search, $options: 'i' } },
                    { amenities: { $regex: search, $options: 'i' } }
                ];
            }
            if (status)
                filter.status = status;
            if (typeof isDefault === 'boolean')
                filter.isDefault = isDefault;
            if (typeof isPopular === 'boolean')
                filter.isPopular = isPopular;
            if (unit)
                filter.unit = unit;
            if (minValue !== undefined || maxValue !== undefined) {
                filter.numericValue = {};
                if (minValue !== undefined)
                    filter.numericValue.$gte = minValue;
                if (maxValue !== undefined)
                    filter.numericValue.$lte = maxValue;
            }
            const sort = {};
            sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
            const skip = (page - 1) * limit;
            const [data, total] = await Promise.all([
                this.towerModel.find(filter).sort(sort).skip(skip).limit(limit).exec(),
                this.towerModel.countDocuments(filter)
            ]);
            const totalPages = Math.ceil(total / limit);
            return {
                success: true,
                data: data,
                pagination: { page, limit, total, totalPages }
            };
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch towers: ${error.message}`);
        }
    }
    async findTowerById(id) {
        return await this.findById(id);
    }
    async updateTower(id, updateTowerDto) {
        return await this.update(id, updateTowerDto);
    }
    async removeTower(id) {
        return await this.remove(id);
    }
    async findTowersByType(towerType) {
        try {
            return await this.towerModel
                .find({
                towerType,
                masterType: master_types_enum_1.MasterType.TOWER,
                status: { $ne: 'archived' }
            })
                .sort({ numericValue: 1 })
                .exec();
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch towers by type: ${error.message}`);
        }
    }
    async findActiveTowers() {
        try {
            return await this.towerModel
                .find({
                isActive: true,
                masterType: master_types_enum_1.MasterType.TOWER,
                status: 'active'
            })
                .sort({ numericValue: 1 })
                .exec();
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to fetch active towers: ${error.message}`);
        }
    }
    async getTowerStatistics() {
        try {
            const baseStats = await this.getStatistics();
            const [towerTypeStats, activeTowersCount, totalUnitsSum] = await Promise.all([
                this.towerModel.aggregate([
                    {
                        $match: {
                            masterType: master_types_enum_1.MasterType.TOWER,
                            status: { $ne: 'archived' },
                            towerType: { $exists: true, $ne: null }
                        }
                    },
                    { $group: { _id: '$towerType', count: { $sum: 1 } } },
                    { $sort: { count: -1 } }
                ]),
                this.towerModel.countDocuments({
                    masterType: master_types_enum_1.MasterType.TOWER,
                    isActive: true,
                    status: 'active'
                }),
                this.towerModel.aggregate([
                    {
                        $match: {
                            masterType: master_types_enum_1.MasterType.TOWER,
                            totalUnits: { $exists: true, $ne: null },
                            status: { $ne: 'archived' }
                        }
                    },
                    { $group: { _id: null, totalUnits: { $sum: '$totalUnits' } } }
                ])
            ]);
            const byTowerType = towerTypeStats.reduce((acc, item) => {
                acc[item._id] = item.count;
                return acc;
            }, {});
            return {
                ...baseStats,
                activeTowersCount,
                totalUnitsAcrossAllTowers: totalUnitsSum[0]?.totalUnits || 0,
                byTowerType
            };
        }
        catch (error) {
            throw new common_1.BadRequestException(`Failed to get tower statistics: ${error.message}`);
        }
    }
    async checkUsageBeforeDelete(id) {
    }
};
exports.TowersService = TowersService;
exports.TowersService = TowersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(tower_schema_1.Tower.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], TowersService);
//# sourceMappingURL=towers.service.js.map