export declare enum MasterType {
    CITY = "city",
    LOCATION = "location",
    AMENITY = "amenity",
    FLOOR = "floor",
    TOWER = "tower",
    PROPERTY_TYPE = "property_type",
    ROOM = "room",
    WASHROOM = "washroom"
}
export declare enum MasterStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    ARCHIVED = "archived"
}
export declare enum AmenityCategory {
    BASIC = "basic",
    RECREATIONAL = "recreational",
    SECURITY = "security",
    CONVENIENCE = "convenience",
    WELLNESS = "wellness",
    SPORTS = "sports",
    COMMUNITY = "community",
    PARKING = "parking",
    UTILITIES = "utilities"
}
export declare enum PropertyTypeCategory {
    RESIDENTIAL = "residential",
    COMMERCIAL = "commercial",
    MIXED_USE = "mixed_use",
    INDUSTRIAL = "industrial",
    AGRICULTURAL = "agricultural",
    INSTITUTIONAL = "institutional"
}
export declare enum RoomTypeCategory {
    STUDIO = "studio",
    BHK = "bhk",
    VILLA = "villa",
    PENTHOUSE = "penthouse",
    DUPLEX = "duplex",
    TRIPLEX = "triplex"
}
