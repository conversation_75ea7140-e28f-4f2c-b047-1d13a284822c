"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SingleCityResponseDto = exports.CityListResponseDto = exports.CityResponseDto = exports.RealEstateDataResponseDto = exports.ClimateDataResponseDto = exports.EconomicDataResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const master_types_enum_1 = require("../../common/enums/master-types.enum");
class EconomicDataResponseDto {
}
exports.EconomicDataResponseDto = EconomicDataResponseDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'GDP in billions',
        example: 310.5
    }),
    __metadata("design:type", Number)
], EconomicDataResponseDto.prototype, "gdp", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Major industries in the city',
        example: ['IT', 'Finance', 'Manufacturing']
    }),
    __metadata("design:type", Array)
], EconomicDataResponseDto.prototype, "majorIndustries", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Economic growth rate percentage',
        example: 7.2
    }),
    __metadata("design:type", Number)
], EconomicDataResponseDto.prototype, "economicGrowthRate", void 0);
class ClimateDataResponseDto {
}
exports.ClimateDataResponseDto = ClimateDataResponseDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Average temperature in Celsius',
        example: 28.5
    }),
    __metadata("design:type", Number)
], ClimateDataResponseDto.prototype, "averageTemperature", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Annual rainfall in mm',
        example: 2400
    }),
    __metadata("design:type", Number)
], ClimateDataResponseDto.prototype, "rainfall", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Average humidity percentage',
        example: 75
    }),
    __metadata("design:type", Number)
], ClimateDataResponseDto.prototype, "humidity", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Predominant season',
        example: 'Tropical'
    }),
    __metadata("design:type", String)
], ClimateDataResponseDto.prototype, "season", void 0);
class RealEstateDataResponseDto {
}
exports.RealEstateDataResponseDto = RealEstateDataResponseDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Average property price per sq ft',
        example: 8500
    }),
    __metadata("design:type", Number)
], RealEstateDataResponseDto.prototype, "averagePropertyPrice", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Price appreciation rate percentage',
        example: 12.5
    }),
    __metadata("design:type", Number)
], RealEstateDataResponseDto.prototype, "priceAppreciationRate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Rental yield percentage',
        example: 3.2
    }),
    __metadata("design:type", Number)
], RealEstateDataResponseDto.prototype, "rentalYield", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Popular areas for real estate',
        example: ['Bandra', 'Andheri', 'Powai']
    }),
    __metadata("design:type", Array)
], RealEstateDataResponseDto.prototype, "popularAreas", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Number of upcoming projects',
        example: 45
    }),
    __metadata("design:type", Number)
], RealEstateDataResponseDto.prototype, "upcomingProjects", void 0);
class CityResponseDto {
}
exports.CityResponseDto = CityResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'City ID',
        example: '507f1f77bcf86cd799439011'
    }),
    __metadata("design:type", String)
], CityResponseDto.prototype, "_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'City name',
        example: 'Mumbai'
    }),
    __metadata("design:type", String)
], CityResponseDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'City description',
        example: 'Financial capital of India'
    }),
    __metadata("design:type", String)
], CityResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Unique city code',
        example: 'MUM'
    }),
    __metadata("design:type", String)
], CityResponseDto.prototype, "code", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Master type',
        enum: master_types_enum_1.MasterType,
        example: master_types_enum_1.MasterType.CITY
    }),
    __metadata("design:type", String)
], CityResponseDto.prototype, "masterType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Status',
        enum: master_types_enum_1.MasterStatus,
        example: master_types_enum_1.MasterStatus.ACTIVE
    }),
    __metadata("design:type", String)
], CityResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'State name',
        example: 'Maharashtra'
    }),
    __metadata("design:type", String)
], CityResponseDto.prototype, "state", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Country name',
        example: 'India'
    }),
    __metadata("design:type", String)
], CityResponseDto.prototype, "country", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'State code',
        example: 'MH'
    }),
    __metadata("design:type", String)
], CityResponseDto.prototype, "stateCode", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Country code',
        example: 'IN'
    }),
    __metadata("design:type", String)
], CityResponseDto.prototype, "countryCode", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Coordinates [longitude, latitude]',
        example: [72.8777, 19.0760]
    }),
    __metadata("design:type", Array)
], CityResponseDto.prototype, "coordinates", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Timezone',
        example: 'Asia/Kolkata'
    }),
    __metadata("design:type", String)
], CityResponseDto.prototype, "timezone", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Pin codes',
        example: ['400001', '400002', '400003']
    }),
    __metadata("design:type", Array)
], CityResponseDto.prototype, "pinCodes", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Population count',
        example: 12442373
    }),
    __metadata("design:type", Number)
], CityResponseDto.prototype, "population", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Area in square kilometers',
        example: 603.4
    }),
    __metadata("design:type", Number)
], CityResponseDto.prototype, "area", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Major language spoken',
        example: 'Marathi'
    }),
    __metadata("design:type", String)
], CityResponseDto.prototype, "majorLanguage", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Alternative names',
        example: ['Bombay', 'Mumbai City']
    }),
    __metadata("design:type", Array)
], CityResponseDto.prototype, "alternateNames", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Economic data',
        type: EconomicDataResponseDto
    }),
    __metadata("design:type", EconomicDataResponseDto)
], CityResponseDto.prototype, "economicData", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Climate data',
        type: ClimateDataResponseDto
    }),
    __metadata("design:type", ClimateDataResponseDto)
], CityResponseDto.prototype, "climateData", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Nearby airports',
        example: ['BOM', 'VABB']
    }),
    __metadata("design:type", Array)
], CityResponseDto.prototype, "nearbyAirports", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Major railway stations',
        example: ['Mumbai Central', 'Chhatrapati Shivaji Terminus']
    }),
    __metadata("design:type", Array)
], CityResponseDto.prototype, "majorRailwayStations", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Major highways',
        example: ['NH-8', 'NH-4', 'Eastern Express Highway']
    }),
    __metadata("design:type", Array)
], CityResponseDto.prototype, "highways", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Real estate data',
        type: RealEstateDataResponseDto
    }),
    __metadata("design:type", RealEstateDataResponseDto)
], CityResponseDto.prototype, "realEstateData", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'District name',
        example: 'Mumbai Suburban'
    }),
    __metadata("design:type", String)
], CityResponseDto.prototype, "district", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Division name',
        example: 'Konkan Division'
    }),
    __metadata("design:type", String)
], CityResponseDto.prototype, "division", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Neighboring cities',
        example: ['Pune', 'Nashik', 'Thane']
    }),
    __metadata("design:type", Array)
], CityResponseDto.prototype, "neighboringCities", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Sort order',
        example: 1
    }),
    __metadata("design:type", Number)
], CityResponseDto.prototype, "sortOrder", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Is default city',
        example: false
    }),
    __metadata("design:type", Boolean)
], CityResponseDto.prototype, "isDefault", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Is popular city',
        example: true
    }),
    __metadata("design:type", Boolean)
], CityResponseDto.prototype, "isPopular", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'SEO title',
        example: 'Mumbai - Financial Capital of India | Real Estate'
    }),
    __metadata("design:type", String)
], CityResponseDto.prototype, "seoTitle", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'SEO description',
        example: 'Discover premium real estate opportunities in Mumbai, the financial capital of India.'
    }),
    __metadata("design:type", String)
], CityResponseDto.prototype, "seoDescription", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'SEO keywords',
        example: ['mumbai real estate', 'mumbai properties', 'mumbai apartments']
    }),
    __metadata("design:type", Array)
], CityResponseDto.prototype, "seoKeywords", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Featured image URL',
        example: 'https://s3.amazonaws.com/bucket/mumbai-skyline.jpg'
    }),
    __metadata("design:type", String)
], CityResponseDto.prototype, "featuredImage", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Gallery images',
        example: ['https://s3.amazonaws.com/bucket/mumbai-1.jpg', 'https://s3.amazonaws.com/bucket/mumbai-2.jpg']
    }),
    __metadata("design:type", Array)
], CityResponseDto.prototype, "gallery", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Additional metadata',
        example: { color: '#FF5733', icon: 'city-icon' }
    }),
    __metadata("design:type", Object)
], CityResponseDto.prototype, "metadata", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Creation date',
        example: '2024-01-15T10:30:00.000Z'
    }),
    __metadata("design:type", Date)
], CityResponseDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Last update date',
        example: '2024-01-15T10:30:00.000Z'
    }),
    __metadata("design:type", Date)
], CityResponseDto.prototype, "updatedAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Full location name (virtual field)',
        example: 'Mumbai, Maharashtra, India'
    }),
    __metadata("design:type", String)
], CityResponseDto.prototype, "fullName", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'GeoJSON location (virtual field)',
        example: { type: 'Point', coordinates: [72.8777, 19.0760] }
    }),
    __metadata("design:type", Object)
], CityResponseDto.prototype, "geoLocation", void 0);
class CityListResponseDto {
}
exports.CityListResponseDto = CityListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success status',
        example: true
    }),
    __metadata("design:type", Boolean)
], CityListResponseDto.prototype, "success", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'List of cities',
        type: [CityResponseDto]
    }),
    __metadata("design:type", Array)
], CityListResponseDto.prototype, "data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Pagination information'
    }),
    __metadata("design:type", Object)
], CityListResponseDto.prototype, "pagination", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Response message',
        example: 'Cities retrieved successfully'
    }),
    __metadata("design:type", String)
], CityListResponseDto.prototype, "message", void 0);
class SingleCityResponseDto {
}
exports.SingleCityResponseDto = SingleCityResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success status',
        example: true
    }),
    __metadata("design:type", Boolean)
], SingleCityResponseDto.prototype, "success", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'City data',
        type: CityResponseDto
    }),
    __metadata("design:type", CityResponseDto)
], SingleCityResponseDto.prototype, "data", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Response message',
        example: 'City retrieved successfully'
    }),
    __metadata("design:type", String)
], SingleCityResponseDto.prototype, "message", void 0);
//# sourceMappingURL=city-response.dto.js.map