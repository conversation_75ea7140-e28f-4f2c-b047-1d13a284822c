"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const request = require("supertest");
const app_module_1 = require("./../src/app.module");
describe('AppController (e2e)', () => {
    let app;
    beforeEach(async () => {
        const moduleFixture = await testing_1.Test.createTestingModule({
            imports: [app_module_1.AppModule],
        }).compile();
        app = moduleFixture.createNestApplication();
        await app.init();
    });
    it('/ (GET)', () => {
        return request(app.getHttpServer())
            .get('/')
            .expect(200)
            .expect((res) => {
            expect(res.body.message).toContain('TrelaX Core Admin Backend API is running successfully!');
        });
    });
    it('/info (GET)', () => {
        return request(app.getHttpServer())
            .get('/info')
            .expect(200)
            .expect((res) => {
            expect(res.body.name).toBe('TrelaX Core Admin Backend API');
            expect(res.body.version).toBe('1.0.0');
            expect(res.body.endpoints).toBeDefined();
        });
    });
    afterAll(async () => {
        await app.close();
    });
});
//# sourceMappingURL=app.e2e-spec.js.map