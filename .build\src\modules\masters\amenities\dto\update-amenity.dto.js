"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateAmenityDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const create_amenity_dto_1 = require("./create-amenity.dto");
const master_types_enum_1 = require("../../common/enums/master-types.enum");
class UpdateAmenityDto extends (0, swagger_1.PartialType)(create_amenity_dto_1.CreateAmenityDto) {
}
exports.UpdateAmenityDto = UpdateAmenityDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Status of the amenity',
        enum: master_types_enum_1.MasterStatus,
        example: master_types_enum_1.MasterStatus.ACTIVE
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(master_types_enum_1.MasterStatus),
    __metadata("design:type", String)
], UpdateAmenityDto.prototype, "status", void 0);
//# sourceMappingURL=update-amenity.dto.js.map