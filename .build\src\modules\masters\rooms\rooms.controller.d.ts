import { RoomsService } from './rooms.service';
import { CreateRoomDto } from './dto/create-room.dto';
import { UpdateRoomDto } from './dto/update-room.dto';
import { QueryMasterWithNumericRangeDto } from '../common/dto/query-master.dto';
export declare class RoomsController {
    private readonly roomsService;
    constructor(roomsService: RoomsService);
    create(createRoomDto: CreateRoomDto): Promise<{
        success: boolean;
        data: import("./schemas/room.schema").Room;
        message: string;
    }>;
    findAll(queryDto: QueryMasterWithNumericRangeDto): Promise<import("../common/interfaces/base-master.interface").MasterListResponse<import("./schemas/room.schema").Room>>;
    getStatistics(): Promise<{
        success: boolean;
        data: {
            byRoomType: any;
            byPopularityRating: any;
            totalCount: number;
            activeCount: number;
            inactiveCount: number;
            popularCount: number;
            defaultCount: number;
            byCategory?: Record<string, number>;
            byStatus?: Record<string, number>;
        };
        message: string;
    }>;
    findByType(type: string): Promise<{
        success: boolean;
        data: import("./schemas/room.schema").Room[];
        message: string;
    }>;
    findOne(id: string): Promise<{
        success: boolean;
        data: import("./schemas/room.schema").Room;
        message: string;
    }>;
    update(id: string, updateRoomDto: UpdateRoomDto): Promise<{
        success: boolean;
        data: import("./schemas/room.schema").Room;
        message: string;
    }>;
    remove(id: string): Promise<{
        success: boolean;
        message: string;
    }>;
}
