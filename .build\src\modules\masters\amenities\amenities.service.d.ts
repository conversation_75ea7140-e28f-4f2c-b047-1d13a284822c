import { Model } from 'mongoose';
import { Amenity, AmenityDocument } from './schemas/amenity.schema';
import { BaseMasterService } from '../common/services/base-master.service';
import { AmenityCategory } from '../common/enums/master-types.enum';
import { CreateAmenityDto } from './dto/create-amenity.dto';
import { UpdateAmenityDto } from './dto/update-amenity.dto';
import { QueryMasterWithCategoryDto } from '../common/dto/query-master.dto';
import { MasterListResponse } from '../common/interfaces/base-master.interface';
export declare class AmenitiesService extends BaseMasterService<Amenity> {
    private amenityModel;
    constructor(amenityModel: Model<AmenityDocument>);
    createAmenity(createAmenityDto: CreateAmenityDto): Promise<Amenity>;
    findAllAmenities(queryDto?: QueryMasterWithCategoryDto): Promise<MasterListResponse<Amenity>>;
    findAmenityById(id: string): Promise<Amenity>;
    updateAmenity(id: string, updateAmenityDto: UpdateAmenityDto): Promise<Amenity>;
    removeAmenity(id: string): Promise<void>;
    findAmenitiesByCategory(category: AmenityCategory): Promise<Amenity[]>;
    findPopularAmenities(limit?: number): Promise<Amenity[]>;
    findAmenitiesByImportance(importanceLevel: number): Promise<Amenity[]>;
    findResidentialAmenities(): Promise<Amenity[]>;
    findCommercialAmenities(): Promise<Amenity[]>;
    findLuxuryAmenities(): Promise<Amenity[]>;
    findBasicAmenities(): Promise<Amenity[]>;
    searchAmenitiesByTags(tags: string[]): Promise<Amenity[]>;
    getAmenityStatistics(): Promise<{
        averagePopularityScore: any;
        byCategory: any;
        byImportanceLevel: any;
        availability: any;
        topPopularAmenities: (import("mongoose").Document<unknown, {}, AmenityDocument, {}> & Amenity & import("mongoose").Document<unknown, any, any, Record<string, any>> & Required<{
            _id: unknown;
        }> & {
            __v: number;
        })[];
        totalCount: number;
        activeCount: number;
        inactiveCount: number;
        popularCount: number;
        defaultCount: number;
        byStatus?: Record<string, number>;
    }>;
    protected checkUsageBeforeDelete(id: string): Promise<void>;
}
