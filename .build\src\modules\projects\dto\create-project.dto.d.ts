import { ProjectStatus, PropertyType, UnitType, FacingDirection, PossessionStatus, ApprovalStatus } from '../enums/project-status.enum';
export declare class LocationDto {
    address: string;
    cityId: string;
    locationId: string;
    state: string;
    country: string;
    pincode: string;
    landmark?: string;
    coordinates?: [number, number];
}
export declare class BuilderDto {
    name: string;
    description?: string;
    website?: string;
    contactEmail?: string;
    contactPhone?: string;
    logo?: string;
}
export declare class UnitConfigurationDto {
    type: UnitType;
    name: string;
    bedrooms: number;
    bathrooms: number;
    balconies?: number;
    carpetArea: number;
    builtUpArea?: number;
    superBuiltUpArea?: number;
    priceMin: number;
    priceMax: number;
    totalUnits?: number;
    availableUnits?: number;
    facing?: FacingDirection[];
    floorPlans?: string[];
}
export declare class AmenitiesDto {
    amenityIds?: string[];
}
export declare class MediaDto {
    images?: string[];
    videos?: string[];
    brochures?: string[];
    floorPlans?: string[];
    masterPlan?: string[];
    locationMap?: string[];
}
export declare class DocumentsDto {
    approvals?: string[];
    legalDocuments?: string[];
    certificates?: string[];
    others?: string[];
}
export declare class CreateProjectDto {
    projectName: string;
    projectDescription: string;
    builder: BuilderDto;
    projectStatus: ProjectStatus;
    location: LocationDto;
    reraNumber?: string;
    propertyType: PropertyType;
    unitConfigurations: UnitConfigurationDto[];
    possessionStatus?: PossessionStatus;
    possessionDate?: string;
    totalArea?: number;
    totalUnits?: number;
    totalFloors?: number;
    totalTowers?: number;
    priceMin?: number;
    priceMax?: number;
    pricePerSqFt?: number;
    amenities?: AmenitiesDto;
    media?: MediaDto;
    documents?: DocumentsDto;
    tags?: string[];
    highlights?: string;
    approvalStatus?: ApprovalStatus;
    nearbyFacilities?: string[];
    isFeatured?: boolean;
    projectImages?: any[];
    floorPlanImages?: any[];
    brochurePdf?: any;
    additionalDocuments?: any[];
}
