import { ConfigService } from '@nestjs/config';
export declare class AppService {
    private readonly configService;
    constructor(configService: ConfigService);
    getHello(): {
        message: string;
        timestamp: string;
        version: string;
        environment: any;
    };
    getApiInfo(): {
        name: string;
        version: string;
        description: string;
        endpoints: {
            auth: string;
            projects: string;
            masters: string;
            builders: string;
            agents: string;
            files: string;
            docs: string;
        };
        modules: {
            authentication: string;
            projects: string;
            masters: string;
            builders: string;
            agents: string;
            files: string;
        };
        features: string[];
    };
}
