"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TowersController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const towers_service_1 = require("./towers.service");
const create_tower_dto_1 = require("./dto/create-tower.dto");
const update_tower_dto_1 = require("./dto/update-tower.dto");
const query_master_dto_1 = require("../common/dto/query-master.dto");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
let TowersController = class TowersController {
    constructor(towersService) {
        this.towersService = towersService;
    }
    async create(createTowerDto) {
        const tower = await this.towersService.createTower(createTowerDto);
        return {
            success: true,
            data: tower,
            message: 'Tower created successfully'
        };
    }
    async findAll(queryDto) {
        return await this.towersService.findAllTowers(queryDto);
    }
    async getStatistics() {
        const statistics = await this.towersService.getTowerStatistics();
        return {
            success: true,
            data: statistics,
            message: 'Tower statistics retrieved successfully'
        };
    }
    async findActive() {
        const towers = await this.towersService.findActiveTowers();
        return {
            success: true,
            data: towers,
            message: 'Active towers retrieved successfully'
        };
    }
    async findByType(type) {
        const towers = await this.towersService.findTowersByType(type);
        return {
            success: true,
            data: towers,
            message: `${type} towers retrieved successfully`
        };
    }
    async findOne(id) {
        const tower = await this.towersService.findTowerById(id);
        return {
            success: true,
            data: tower,
            message: 'Tower retrieved successfully'
        };
    }
    async update(id, updateTowerDto) {
        const tower = await this.towersService.updateTower(id, updateTowerDto);
        return {
            success: true,
            data: tower,
            message: 'Tower updated successfully'
        };
    }
    async remove(id) {
        await this.towersService.removeTower(id);
        return {
            success: true,
            message: 'Tower deleted successfully'
        };
    }
};
exports.TowersController = TowersController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new tower' }),
    (0, swagger_1.ApiBody)({ type: create_tower_dto_1.CreateTowerDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.CREATED, description: 'Tower created successfully' }),
    __param(0, (0, common_1.Body)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_tower_dto_1.CreateTowerDto]),
    __metadata("design:returntype", Promise)
], TowersController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all towers' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: 'Towers retrieved successfully' }),
    __param(0, (0, common_1.Query)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [query_master_dto_1.QueryMasterWithNumericRangeDto]),
    __metadata("design:returntype", Promise)
], TowersController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('statistics'),
    (0, swagger_1.ApiOperation)({ summary: 'Get tower statistics' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: 'Tower statistics retrieved successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TowersController.prototype, "getStatistics", null);
__decorate([
    (0, common_1.Get)('active'),
    (0, swagger_1.ApiOperation)({ summary: 'Get active towers' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: 'Active towers retrieved successfully' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], TowersController.prototype, "findActive", null);
__decorate([
    (0, common_1.Get)('type/:type'),
    (0, swagger_1.ApiOperation)({ summary: 'Get towers by type' }),
    (0, swagger_1.ApiParam)({ name: 'type', description: 'Tower type', example: 'residential' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: 'Towers retrieved successfully' }),
    __param(0, (0, common_1.Param)('type')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TowersController.prototype, "findByType", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get tower by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Tower ID' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: 'Tower retrieved successfully' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TowersController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update tower by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Tower ID' }),
    (0, swagger_1.ApiBody)({ type: update_tower_dto_1.UpdateTowerDto }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: 'Tower updated successfully' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_tower_dto_1.UpdateTowerDto]),
    __metadata("design:returntype", Promise)
], TowersController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete tower by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Tower ID' }),
    (0, swagger_1.ApiResponse)({ status: common_1.HttpStatus.OK, description: 'Tower deleted successfully' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], TowersController.prototype, "remove", null);
exports.TowersController = TowersController = __decorate([
    (0, swagger_1.ApiTags)('Masters - Towers'),
    (0, common_1.Controller)('masters/towers'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [towers_service_1.TowersService])
], TowersController);
//# sourceMappingURL=towers.controller.js.map