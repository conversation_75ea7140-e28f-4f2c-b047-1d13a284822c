"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentsSchema = exports.MediaSchema = exports.AmenitiesSchema = exports.UnitConfigurationSchema = exports.BuilderSchema = exports.LocationSchema = exports.ProjectSchema = exports.Project = exports.Documents = exports.Media = exports.Amenities = exports.UnitConfiguration = exports.Builder = exports.Location = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const project_status_enum_1 = require("../enums/project-status.enum");
let Location = class Location {
};
exports.Location = Location;
__decorate([
    (0, mongoose_1.Prop)({ required: true, trim: true }),
    __metadata("design:type", String)
], Location.prototype, "address", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: true,
        type: mongoose_2.Types.ObjectId,
        ref: 'City',
        index: true
    }),
    __metadata("design:type", mongoose_2.Types.ObjectId)
], Location.prototype, "cityId", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: true,
        type: mongoose_2.Types.ObjectId,
        ref: 'Location',
        index: true
    }),
    __metadata("design:type", mongoose_2.Types.ObjectId)
], Location.prototype, "locationId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, trim: true }),
    __metadata("design:type", String)
], Location.prototype, "state", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, trim: true }),
    __metadata("design:type", String)
], Location.prototype, "country", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, trim: true }),
    __metadata("design:type", String)
], Location.prototype, "pincode", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, trim: true }),
    __metadata("design:type", String)
], Location.prototype, "landmark", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: [Number], validate: [arrayLimit, 'Coordinates must have exactly 2 elements'] }),
    __metadata("design:type", Array)
], Location.prototype, "coordinates", void 0);
exports.Location = Location = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], Location);
let Builder = class Builder {
};
exports.Builder = Builder;
__decorate([
    (0, mongoose_1.Prop)({ required: true, trim: true }),
    __metadata("design:type", String)
], Builder.prototype, "name", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, trim: true }),
    __metadata("design:type", String)
], Builder.prototype, "description", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, trim: true }),
    __metadata("design:type", String)
], Builder.prototype, "website", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, trim: true }),
    __metadata("design:type", String)
], Builder.prototype, "contactEmail", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, trim: true }),
    __metadata("design:type", String)
], Builder.prototype, "contactPhone", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, trim: true }),
    __metadata("design:type", String)
], Builder.prototype, "logo", void 0);
exports.Builder = Builder = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], Builder);
let UnitConfiguration = class UnitConfiguration {
};
exports.UnitConfiguration = UnitConfiguration;
__decorate([
    (0, mongoose_1.Prop)({ required: true, enum: project_status_enum_1.UnitType, type: String }),
    __metadata("design:type", String)
], UnitConfiguration.prototype, "type", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, trim: true }),
    __metadata("design:type", String)
], UnitConfiguration.prototype, "name", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, min: 0 }),
    __metadata("design:type", Number)
], UnitConfiguration.prototype, "bedrooms", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, min: 0 }),
    __metadata("design:type", Number)
], UnitConfiguration.prototype, "bathrooms", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, min: 0 }),
    __metadata("design:type", Number)
], UnitConfiguration.prototype, "balconies", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, min: 0 }),
    __metadata("design:type", Number)
], UnitConfiguration.prototype, "carpetArea", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, min: 0 }),
    __metadata("design:type", Number)
], UnitConfiguration.prototype, "builtUpArea", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, min: 0 }),
    __metadata("design:type", Number)
], UnitConfiguration.prototype, "superBuiltUpArea", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, min: 0 }),
    __metadata("design:type", Number)
], UnitConfiguration.prototype, "priceMin", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, min: 0 }),
    __metadata("design:type", Number)
], UnitConfiguration.prototype, "priceMax", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, min: 0 }),
    __metadata("design:type", Number)
], UnitConfiguration.prototype, "totalUnits", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, min: 0 }),
    __metadata("design:type", Number)
], UnitConfiguration.prototype, "availableUnits", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: [String], enum: project_status_enum_1.FacingDirection }),
    __metadata("design:type", Array)
], UnitConfiguration.prototype, "facing", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: [String] }),
    __metadata("design:type", Array)
], UnitConfiguration.prototype, "floorPlans", void 0);
exports.UnitConfiguration = UnitConfiguration = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], UnitConfiguration);
let Amenities = class Amenities {
};
exports.Amenities = Amenities;
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: [mongoose_2.Types.ObjectId],
        ref: 'Amenity',
        default: []
    }),
    __metadata("design:type", Array)
], Amenities.prototype, "amenityIds", void 0);
exports.Amenities = Amenities = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], Amenities);
let Media = class Media {
};
exports.Media = Media;
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: [String], default: [] }),
    __metadata("design:type", Array)
], Media.prototype, "images", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: [String], default: [] }),
    __metadata("design:type", Array)
], Media.prototype, "videos", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: [String], default: [] }),
    __metadata("design:type", Array)
], Media.prototype, "brochures", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: [String], default: [] }),
    __metadata("design:type", Array)
], Media.prototype, "floorPlans", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: [String], default: [] }),
    __metadata("design:type", Array)
], Media.prototype, "masterPlan", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: [String], default: [] }),
    __metadata("design:type", Array)
], Media.prototype, "locationMap", void 0);
exports.Media = Media = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], Media);
let Documents = class Documents {
};
exports.Documents = Documents;
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: [String], default: [] }),
    __metadata("design:type", Array)
], Documents.prototype, "approvals", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: [String], default: [] }),
    __metadata("design:type", Array)
], Documents.prototype, "legalDocuments", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: [String], default: [] }),
    __metadata("design:type", Array)
], Documents.prototype, "certificates", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: [String], default: [] }),
    __metadata("design:type", Array)
], Documents.prototype, "others", void 0);
exports.Documents = Documents = __decorate([
    (0, mongoose_1.Schema)({ _id: false })
], Documents);
let Project = class Project {
};
exports.Project = Project;
__decorate([
    (0, mongoose_1.Prop)({ required: true, trim: true, index: true }),
    __metadata("design:type", String)
], Project.prototype, "projectName", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, trim: true }),
    __metadata("design:type", String)
], Project.prototype, "projectDescription", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: Builder }),
    __metadata("design:type", Builder)
], Project.prototype, "builder", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: true,
        type: String,
        enum: project_status_enum_1.ProjectStatus,
        index: true
    }),
    __metadata("design:type", String)
], Project.prototype, "projectStatus", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: Location }),
    __metadata("design:type", Location)
], Project.prototype, "location", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, trim: true }),
    __metadata("design:type", String)
], Project.prototype, "reraNumber", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: true,
        type: String,
        enum: project_status_enum_1.PropertyType,
        index: true
    }),
    __metadata("design:type", String)
], Project.prototype, "propertyType", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: [UnitConfiguration] }),
    __metadata("design:type", Array)
], Project.prototype, "unitConfigurations", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: String,
        enum: project_status_enum_1.PossessionStatus
    }),
    __metadata("design:type", String)
], Project.prototype, "possessionStatus", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false }),
    __metadata("design:type", Date)
], Project.prototype, "possessionDate", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, min: 0 }),
    __metadata("design:type", Number)
], Project.prototype, "totalArea", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, min: 0 }),
    __metadata("design:type", Number)
], Project.prototype, "totalUnits", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, min: 0 }),
    __metadata("design:type", Number)
], Project.prototype, "totalFloors", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, min: 0 }),
    __metadata("design:type", Number)
], Project.prototype, "totalTowers", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, min: 0 }),
    __metadata("design:type", Number)
], Project.prototype, "priceMin", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, min: 0 }),
    __metadata("design:type", Number)
], Project.prototype, "priceMax", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, min: 0 }),
    __metadata("design:type", Number)
], Project.prototype, "pricePerSqFt", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: Amenities }),
    __metadata("design:type", Amenities)
], Project.prototype, "amenities", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: Media }),
    __metadata("design:type", Media)
], Project.prototype, "media", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: Documents }),
    __metadata("design:type", Documents)
], Project.prototype, "documents", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: [String], default: [] }),
    __metadata("design:type", Array)
], Project.prototype, "tags", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, trim: true }),
    __metadata("design:type", String)
], Project.prototype, "highlights", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: false,
        type: String,
        enum: project_status_enum_1.ApprovalStatus,
        default: project_status_enum_1.ApprovalStatus.PENDING
    }),
    __metadata("design:type", String)
], Project.prototype, "approvalStatus", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: false, type: [String], default: [] }),
    __metadata("design:type", Array)
], Project.prototype, "nearbyFacilities", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: true, index: true }),
    __metadata("design:type", Boolean)
], Project.prototype, "isActive", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: false }),
    __metadata("design:type", Boolean)
], Project.prototype, "isFeatured", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 0 }),
    __metadata("design:type", Number)
], Project.prototype, "viewCount", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 0 }),
    __metadata("design:type", Number)
], Project.prototype, "inquiryCount", void 0);
exports.Project = Project = __decorate([
    (0, mongoose_1.Schema)({
        timestamps: true,
        collection: 'projects',
    })
], Project);
function arrayLimit(val) {
    return val.length === 2;
}
exports.ProjectSchema = mongoose_1.SchemaFactory.createForClass(Project);
exports.LocationSchema = mongoose_1.SchemaFactory.createForClass(Location);
exports.BuilderSchema = mongoose_1.SchemaFactory.createForClass(Builder);
exports.UnitConfigurationSchema = mongoose_1.SchemaFactory.createForClass(UnitConfiguration);
exports.AmenitiesSchema = mongoose_1.SchemaFactory.createForClass(Amenities);
exports.MediaSchema = mongoose_1.SchemaFactory.createForClass(Media);
exports.DocumentsSchema = mongoose_1.SchemaFactory.createForClass(Documents);
exports.ProjectSchema.index({ projectName: 'text', projectDescription: 'text' });
exports.ProjectSchema.index({ 'location.city': 1 });
exports.ProjectSchema.index({ 'location.state': 1 });
exports.ProjectSchema.index({ propertyType: 1 });
exports.ProjectSchema.index({ projectStatus: 1 });
exports.ProjectSchema.index({ createdAt: -1 });
exports.ProjectSchema.index({ 'location.coordinates': '2dsphere' });
//# sourceMappingURL=project.schema.js.map