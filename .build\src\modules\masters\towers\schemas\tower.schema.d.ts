import { Document } from 'mongoose';
import { MasterWithNumericValue } from '../../common/schemas/base-master.schema';
import { MasterType } from '../../common/enums/master-types.enum';
export type TowerDocument = Tower & Document;
export declare class Tower extends MasterWithNumericValue {
    masterType: MasterType.TOWER;
    numericValue?: number;
    unit?: string;
    towerType?: string;
    totalFloors?: number;
    height?: number;
    totalUnits?: number;
    unitsPerFloor?: number;
    specifications?: {
        structure?: string;
        elevators?: number;
        staircases?: number;
        parkingLevels?: number;
        totalParkingSpots?: number;
        powerBackup?: boolean;
        waterStorage?: number;
        sewageTreatment?: boolean;
        rainwaterHarvesting?: boolean;
        solarPanels?: boolean;
        greenBuilding?: boolean;
    };
    amenities?: string[];
    features?: string[];
    isActive?: boolean;
    constructionYear?: number;
    architect?: string;
    contractor?: string;
    createdAt?: Date;
    updatedAt?: Date;
}
export declare const TowerSchema: import("mongoose").Schema<Tower, import("mongoose").Model<Tower, any, any, any, Document<unknown, any, Tower, any> & Tower & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Tower, Document<unknown, {}, import("mongoose").FlatRecord<Tower>, {}> & import("mongoose").FlatRecord<Tower> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
